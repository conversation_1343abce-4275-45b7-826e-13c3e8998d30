import API from '@/api/internalSystem/customerManage/implementation'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
export default {
  name: "implementationContractList",
  components: {
    TableView,
    Pagination
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customer_name: "",
        fk_sell_employee_id: ""
      },
      tableList: [{
          name: "单据编号",
          value: "contract_no",
          width: 120
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "培训方式",
          value: "train_type_name",
          width: 70
        },
        {
          name: "销售类型",
          value: "sell_type_name",
          width: 82
        },
        {
          name: "客户类型",
          value: "customer_type_name",
          width: 70
        },
        {
          name: "手机",
          value: "phone",
          width: 120
        },
        {
          name: "联系人",
          value: "link_man",
          width: 70
        },
        {
          name: "客户地址",
          value: "address"
        }
      ],
      employeeList: []
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "合同列表"
    },
    customerStage: {
      type: Number
    }
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.getList();
          this.$store.dispatch('getEmployee').then(res => {
            this.employeeList = res;
          })
        });
      // }, 300);
    },
    getList() {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.cus_pagination.obtain());
      API.contractList(param).then(res => {
        this.tableData = res.data;
        this.$refs.cus_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    //提交
    submitForm() {
      if (this.selectRecords.length != 1)
        return this.error("请选择一条记录");
      this.$emit("getInfo", this.selectRecords[0]);
      this.dialogCancel();
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.selectRecords = [];
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
    rowDblclick(row, column, event){
      this.selectRecords=[]
      this.selectRecords.push(row)
      this.submitForm()
    }
  }
};