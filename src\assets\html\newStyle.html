<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>吉勤科技</title>
    <style>
      .headForm-li {
        list-style-type: none;
        text-align: center;
        margin-top: 30px;
        width: 100%;
        height: 45px;
        display: flex;
        float: left;
      }

      .labelBox {
        width: 140px;
        flex: 0 0 140px;
        height: 100%;
        text-align: center;
        overflow: hidden;
      }

      .labelName {
        display: inline-block;
        width: 100%;
        line-height: 20px;
        font-size: 14px;
        overflow: hidden;
      }

      .headFormData {
        line-height: 22px;
        width: calc(100% - 140px);
        height: 100%;
        flex: 1;
        border-bottom: 1px solid #000;
        display: flex;
        align-items: center;
      }

      .headFormData-span {
        width: 100%;
        word-wrap: break-word;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        -webkit-box-align: stretch;
      }

      .tr-td {
        text-align: center;
        min-height: 30px;
        height: 50px;
        min-width: 150px;
        padding: 0 10px;
        border-bottom: 1px solid #999;
        border-right: 1px solid #999;
      }
      .support{
       text-align: center;
      }
      .support a{
        text-decoration: none;
      }

    </style>
  </head>
  <body>
    <h2 style="margin-top: 20px;font-size: 1.6rem; text-align: center">
      {{title}}
    </h2>
    <ul
      style="width: 95%;margin: 40px auto; list-style: none; padding-left: 0; height: 100px;"
    >
      <li style="list-style-type: none;
      text-align: center;
      margin-top: 30px;
      min-width: 45%;
      height: 45px;
      display: flex;
      float: left;">
        <div style="width: 140px;
        flex: 0 0 140px;
        height: 100%;
        text-align: center;
        overflow: hidden;">
          <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">收件人</label>
          <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">Receiver:</label>
        </div>
        <div style="line-height: 22px;
        width: calc(100% - 140px);
        height: 100%;
        flex: 1;
        border-bottom: 1px solid #000;
        display: flex;
        align-items: center;">
          <span style="width: 100%;
          word-wrap: break-word;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          -webkit-box-align: stretch;">{{Receiver}}</span>
        </div>
      </li>
      <li style="list-style-type: none;
      text-align: center;
      margin-top: 30px;
      min-width: 45%;
      padding-left: 5%;
      height: 45px;
      display: flex;
      float: left;">
        <div style="width: 140px;
        flex: 0 0 140px;
        height: 100%;
        text-align: center;
        overflow: hidden;">
          <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">公司名称</label>
          <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">ReceiveCompany:</label>
        </div>
        <div style="line-height: 22px;
        width: calc(100% - 140px);
        height: 100%;
        flex: 1;
        border-bottom: 1px solid #000;
        display: flex;
        align-items: center;">
          <span style="width: 100%;
          word-wrap: break-word;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          -webkit-box-align: stretch;">{{ReceiveCompany}}</span>
        </div>
      </li>
    </ul>

    <div style="width: 100%; overflow-x: auto; height: auto;">
      <table
        style="margin-bottom: 30px; border-top: 1px solid #999; border-left: 1px solid #999;"
        cellspacing="0"
        cellpadding="0"
      >
        <thead>
          <tr>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              序号
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              品牌
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              品名
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              规格型号
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              单位
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              数量
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              快递单号
            </td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              {{storageNumber}}
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              {{brand}}
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              {{model}}
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              {{name}}
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              {{measureUnit}}
            </td>
            <td style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              {{publicIntCount}}
            </td>
            <td rowspan="{{listLength}}" style="text-align: center;
            min-height: 30px;
            height: 50px;
            min-width: 150px;
            padding: 0 10px;
            border-bottom: 1px solid #999;
            border-right: 1px solid #999;">
              {{salesAgreement}}
              <div>
                发货方式:
              </div>
              <div>
                快递单号：
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

<div>
  
    <ul
    style="width: 95%;margin: 40px auto; list-style: none; padding-left: 0; min-height: 400px;"
  >
    <li style="list-style-type: none;
    text-align: center;
    margin-top: 30px;
    min-width: 45%;
    height: 45px;
    display: flex;
    float: left;">
      <div style="width: 140px;
        flex: 0 0 140px;
        height: 100%;
        text-align: center;
        overflow: hidden;">
        <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">发件人</label>
        <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">Sender:</label>
      </div>
      <div style="line-height: 22px;
        width: calc(100% - 140px);
        height: 100%;
        flex: 1;
        border-bottom: 1px solid #000;
        display: flex;
        align-items: center;">
        <span style="width: 100%;
          word-wrap: break-word;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          -webkit-box-align: stretch;">{{Sender}}</span>
      </div>
    </li>
    <li style="list-style-type: none;
    text-align: center;
    margin-top: 30px;
    padding-left: 5%;
    min-width: 45%;
    height: 45px;
    display: flex;
    float: left;">
      <div style="width: 140px;
        flex: 0 0 140px;
        height: 100%;
        text-align: center;
        overflow: hidden;">
        <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">发件公司</label>
        <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">SendCompany:</label>
      </div>
      <div style="line-height: 22px;
        width: calc(100% - 140px);
        height: 100%;
        flex: 1;
        border-bottom: 1px solid #000;
        display: flex;
        align-items: center;">
        <span style="width: 100%;
          word-wrap: break-word;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          -webkit-box-align: stretch;">{{SendCompany}}</span>
      </div>
    </li>
    <li style="list-style-type: none;
    text-align: center;
    margin-top: 30px;
    min-width: 45%;
    height: 45px;
    display: flex;
    float: left;">
      <div style="width: 140px;
        flex: 0 0 140px;
        height: 100%;
        text-align: center;
        overflow: hidden;">
        <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">发件地址</label>
        <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">SendAddress:</label>
      </div>
      <div style="line-height: 22px;
        width: calc(100% - 140px);
        height: 100%;
        flex: 1;
        border-bottom: 1px solid #000;
        display: flex;
        align-items: center;">
        <span style="width: 100%;
          word-wrap: break-word;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          -webkit-box-align: stretch;">{{SendAddress}}</span>
      </div>
    </li>
    <li style="list-style-type: none;
    text-align: center;
    margin-top: 30px;
    min-width: 45%;
      padding-left: 5%;
    height: 45px;
    display: flex;
    float: left;">
      <div style="width: 140px;
        flex: 0 0 140px;
        height: 100%;
        text-align: center;
        overflow: hidden;">
        <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">发件人电话</label>
        <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">SendPhone:</label>
      </div>
      <div style="line-height: 22px;
        width: calc(100% - 140px);
        height: 100%;
        flex: 1;
        border-bottom: 1px solid #000;
        display: flex;
        align-items: center;">
        <span style="width: 100%;
          word-wrap: break-word;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          -webkit-box-align: stretch;">{{SendPhone}}</span>
      </div>
    </li>
    <li style="list-style-type: none;
    text-align: center;
    margin-top: 30px;
    min-width: 45%;
    height: 45px;
    display: flex;
    float: left;">
      <div style="width: 140px;
        flex: 0 0 140px;
        height: 100%;
        text-align: center;
        overflow: hidden;">
        <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">发件人邮箱</label>
        <label style=" display: inline-block;
          width: 100%;
          line-height: 20px;
          font-size: 14px;
          overflow: hidden;">SendEmail:</label>
      </div>
      <div style="line-height: 22px;
        width: calc(100% - 140px);
        height: 100%;
        flex: 1;
        border-bottom: 1px solid #000;
        display: flex;
        align-items: center;">
        <span style="width: 100%;
          word-wrap: break-word;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          -webkit-box-align: stretch;">{{SendEmail}}</span>
      </div>
    </li>
   
  </ul>

</div>
  <div style="text-align: center;"><a style="text-decoration: none;" href="https://www.jiqinyun.com/home">技术支持：福州吉勤信息科技有限公司</a></div>
  </body>
</html>
