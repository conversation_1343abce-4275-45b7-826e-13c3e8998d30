export default [{
  path: 'salesManage',
  name: 'salesManage',
  title: '销售管理',
  icon: 'home',
  show: true,
  meta: {
    keepAlive: false
  },
  component: resolve =>
    require([
      "@/views/internalSystem/backstage/components/view/view.vue"
    ], resolve),
  children: [{
      path: 'quotation',
      name: 'quotation',
      title: '销售报价单',
      meta: {
        title: "销售报价单"
      },
      component: () => import('@/views/internalSystem/backstage/salesManage/quotation/index.vue')
    },
    {
      path: 'newContract',
      name: 'newContract',
      title: '新版合同单',
      meta: {
        title: "新版合同单"
      },
      component: () => import('@/views/internalSystem/backstage/salesManage/newContract/index.vue')
    },

    {
      path: 'contract',
      name: 'contract',
      title: '销售合同单',
      meta: {
        title: "销售合同单"
      },
      component: () => import('@/views/internalSystem/backstage/salesManage/contract/index.vue')
    },
    {
      path: 'contractAdd',
      name: 'contractAdd',
      title: '续费销售合同单',
      meta: {
        title: "续费销售合同单"

      },
     
      component: () => import('@/views/internalSystem/backstage/salesManage/contract/components/addContract/index.vue')
    },
    {
      path: 'openTicket',
      name: 'openTicket',
      title: '销售开票单',
      meta: {
        title: "销售开票单"

      },
      component: () => import('@/views/internalSystem/backstage/salesManage/openTicket/index.vue')
    },
    {
      path: 'outbound',
      name: 'outbound',
      title: '销售出库单',
      meta: {
        title: "销售出库单"
      },
      component: () => import('@/views/internalSystem/backstage/salesManage/outbound/index.vue')
    },
    {
      path: 'authorization',
      name: 'authorization',
      title: '销售授权单',
      meta: {
        title: "销售授权单"
      },
      component: () => import('@/views/internalSystem/backstage/salesManage/authorization/index.vue')
    },
    {
      path: 'performance',
      name: 'performance',
      title: '销售业绩统计',
      meta: {
        title: "销售业绩统计"
      },
      component: () => import('@/views/internalSystem/backstage/salesManage/performance/index.vue')
    },
    {
      path: 'performanceAnalysis',
      name: 'performanceAnalysis',
      title: '销售业绩分析',
      meta: {
        title: "销售业绩分析",
      },
      component: () => import('@/views/internalSystem/backstage/salesManage/performanceAnalysis/index.vue')
    }
  ]
}]