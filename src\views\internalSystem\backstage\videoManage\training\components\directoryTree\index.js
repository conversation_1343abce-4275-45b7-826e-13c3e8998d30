import API from "@/api/internalSystem/videoManage/directory/index.js";
import AddDirectory from "./../addDirectory/index.vue";
export default {
  data() {
    return {
      filterText: ``,
      directoryTreeList: [],
      checkedKey: [],
      defaultProps: {
        children: "children",
        label: "directory_name",
        disabled: true,
      },
      showCheckbox: false,
      directory_id: "", //目录Id
      parentId: "", //目录父Id
      parentName: "", //目录父名称
    };
  },

  mounted() {
    this.getList();
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    changeEnableFlag(row){
    },
    cilckDirectoryNode(node) {


      this.$emit("getTrainingFile", { directory_id: node.directory_id });
    },
    GetCheckedKeys() {
      //获取目录树选中的节点
      return this.$refs.tree.getCheckedKeys();
    },
    checkFid(data = [], directory_id = 0) {
      for (let i = 0; i < data.length; i++) {
        let list = data[i];
        if (list.directory_id === directory_id) return true;
        if (list.children && list.children.length)
          return this.checkFid(list.children, directory_id);
        return false;
      }
    },
    //设置选中目录树
    SetCheckedKeys(data = []) {
      this.showCheckbox = true;
      this.$refs.tree.setCheckedKeys(data);
    },
    // getDirectoryTree(brand_id) {
    //   // 获取目录列表
    //   API.query({
    //     brand_id,
    //   })
    //     .then((data) => {
    //       this.directoryTreeList = this.forInt(data.data);
    //     })
    //     .catch(() => {});
    // },
    getList() {
      API.query({})
        .then((data) => {
          this.directoryTreeList = this.forInt(data.data);
        })
        .catch(() => {});
    },
    forInt(data = []) {
      // 遍历把id转为Int类型，后面要求后端直接返回int类型
      let _this = this;
      data.forEach((list) => {
        list.directory_id = _this.stringToInt(list.directory_id);
        if (list.children && list.children.length) _this.forInt(list.children);
      });
      return data;
    },
    stringToInt(id = "0") {
      return parseInt(id);
    },
    filterNode(value, data) {
      // 筛选查询
      if (!value) return true;
      return data.directory_name.indexOf(value) !== -1;
    },
    //打开添加目录会话框
    addDirectory(params = null) {
      if (!params) {
        params = {
          directory_id: 0,
          directory_name: "无上级目录",
          level: 0,
        };
      }

      let directoryObject = {
        parent_id: params.directory_id,
        parent_name: params.directory_name,
        directory_id: "",
        level: params.level + 1,
      };
      this.$refs.AddDirectory.Show(directoryObject);
    },
    //打开编辑目录会话框
    editDirectory(params) {
      let directoryObject = {
        parentId: "",
        parentName: "",
        directory_id: params.directory_id,
        level: "",
      };
      this.$refs.AddDirectory.Show(directoryObject);
    },

    //删除目录
    delDirectory(params) {
      this.$confirm("此操作将删除该目录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let delIds = [];
          delIds.push(params.directory_id);
          API.remove({
            directory_id: delIds,
          })
            .then(() => {
              this.getList();
            })
            .catch(() => {})
            .finally(() => {});
        })
        .catch(() => {});
    },
    clickDeal(currentObj, treeStatus) {
      // 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。
      if (treeStatus) {
        // 子节点只要被选中父节点就被选中
        this.selectedParent(currentObj);
      } else {
        // 未选中 处理子节点全部未选中
        if (currentObj.children.length !== 0) {
          this.sameChildrenNode(currentObj, false);
        }
      }
    },
    // 统一处理子节点为相同的勾选状态
    sameChildrenNode(currentObj, treeStatus) {
      this.$refs.tree.setChecked(currentObj.directory_id, treeStatus);
      for (let i = 0; i < currentObj.children.length; i++) {
        this.sameChildrenNode(currentObj.children[i], treeStatus);
      }
    },
    // 统一处理父节点为选中
    selectedParent(currentObj) {
      let currentNode = this.$refs.tree.getNode(currentObj);
      if (currentNode.parent.key !== undefined) {
        this.$refs.tree.setChecked(currentNode.parent, true);
        this.selectedParent(currentNode.parent);
      }
    },
    //全选
    chooseAll(data, treeStatus) {
      this.sameChildrenNode(data, treeStatus);
    },
  },
  components: {
    AddDirectory,
  },
};
