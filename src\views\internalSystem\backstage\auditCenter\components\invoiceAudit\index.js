import API from '@/api/internalSystem/financialManage/invoice'
import TableCustom from "@/views/internalSystem/backstage/components/tableCustom/index.vue";
import {
  mapGetters
} from "vuex";
import {
  getOptions
} from "@/common/internalSystem/common.js"
export default {
  name: "invoiceAudit",
  components: {
    TableCustom
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        add_user_name: "",
        department_name: "",
        invoice_unit: "",
        invoice_remark: ""
      },
      loading: false,
      tableCol: [{
        label: "费用项目",
        prop: "cost_project_no",
        width: 160
      }, {
        label: "项目名称",
        prop: "cost_project_name",
        width: 160
      }, {
        label: "费用明细",
        prop: "cost_detail_no",
        width: 160
      }, {
        label: "明细名称",
        prop: "cost_detail_name",
        width: 160,
        need: true
      }, {
        label: "报销银行",
        prop: "bank_name",
        width: 160,
        need: true
      }, {
        label: "fk_bank_id",
        prop: "fk_bank_id",
        isHide: true
      }, {
        label: "承担员工",
        prop: "employee_name",
        width: 160,
        need: true
      }, {
        label: "部门名称",
        prop: "department_name",
        width: 160
      }, {
        label: "承担金额",
        prop: "bear_money",
        width: 160,
        need: true
      }, {
        label: "承担日期",
        prop: "bear_date",
        width: 160,
        need: true
      }, {
        label: "凭证号码",
        prop: "voucher_number",
        width: 160,
        need: true
      }, {
        label: "摘要",
        prop: "summary",
        width: 160,
        need: true
      }, {
        label: "备注说明",
        prop: "remark",
        width: 160
      }],
      obj: {},
      dialogVisibleAudit: false,
      auditForm: {
        auditState: 1,
        approval: ""
      },
      rules: {
        auditState: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }]
      },
      auditStateList: []
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.auditStateList = getOptions('t_financial_cost_invoice', 'approval_state');
      this.obj = {
        cost_project_no: {
          value: "",
          type: "input",
          disabled: true
        },
        cost_project_name: {
          value: "",
          type: "input",
          disabled: true
        },
        cost_detail_no: {
          value: "",
          type: "input",
          disabled: true
        },
        cost_detail_name: {
          value: "",
          type: "dialog",
          dialogType: "cost"
        },
        bank_name: {
          value: "",
          type: "dialog",
          dialogType: "bank"
        },
        fk_bank_id: {
          value: "",
          type: "input"
        },
        employee_name: {
          value: "",
          type: "dialog",
          dialogType: "employee"
        },
        department_name: {
          value: 1,
          type: "input",
          disabled: true
        },
        bear_money: {
          value: "",
          type: "float"
        },
        bear_date: {
          value: "",
          type: "date"
        },
        voucher_number: {
          value: "",
          type: "input"
        },
        summary: {
          value: "",
          type: "input"
        },
        remark: {
          value: "",
          type: "input"
        }
      }
      if (data) {
        this.ruleForm = data;
        this.ruleForm.invoice_unit = parseInt(data.invoice_unit);
        API.detailList({
            financial_cost_invoice_id: data.financial_cost_invoice_id
          })
          .then(res => {
            res.data.map(item => {
              let pro = JSON.parse(JSON.stringify(this.obj))
              for (let v in item) {
                if (pro[v]) {
                  pro[v].value = item[v];
                  pro[v].disabled = true;
                }
              }
              this.$refs.tableCustom.add2(pro);
            })
          })
          .catch(() => {}).finally(() => {});
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
    },
    save() {
      let params = this.auditForm;
      params.financial_cost_invoice_id = this.ruleForm.financial_cost_invoice_id;
      params.fk_employee_id = this.userInfo.employeeId;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    //添加明细
    add() {
      this.$refs.tableCustom.add();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        add_user_name: "",
        department_name: "",
        invoice_unit: "",
        invoice_remark: ""
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo","contract_auditStateList"])
  }
};