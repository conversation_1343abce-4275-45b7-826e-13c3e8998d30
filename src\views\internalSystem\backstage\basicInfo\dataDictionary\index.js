import API from '@/api/internalSystem/common/index.js'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddDataDictionary from "./components/add/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import {
  mapGetters
} from "vuex";
export default {
  name: "dataDictionary",
  data() {
    return {
      djType: "BMXX",
      title: "数据字典设置",
      loading: false,
      selectRecords: [],
      isCheck: true,
      tableData: [],
      formSearch: {
        tbName: "",
        tbCode: "",
        sysName: ""
      },
      tableList: [{
          name: "表名",
          value: "tbName",
          width: 200
        },
        {
          name: "字段代码",
          value: "tbCode"
        },
        {
          name: "字段名称",
          value: "sysName"
        },
        {
          name: "字段值",
          value: "sysValue",
          width: 80
        },
        {
          name: "排序",
          value: "sort",
          width: 80
        },
        {
          name: "备注",
          value: "remark"
        },
        {
          name: "创建时间",
          value: "addTime"
        }
      ],
      datalist: []
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
      if (f)
        param.pageNum = 1;
 
      API.queryDataDictionary(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    add() {
      this.$refs.AddDataDictionary.Show();
    },
    //打开修改数据字典会话框
    modify(item) {
      let params = {
        id: item.id
      };
      API.dataDictionaryInfo(params)
        .then(data => {
          this.$refs.AddDataDictionary.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      let params = {
        id: item.id
      };
      API.removeDataDictionary(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    delBatches() {
      let ids = []
      this.selectRecords.forEach(item => {
        ids.push(item.id)
      })
      let params = {
        ids,
      };
      API.removeDataDictionary(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});

    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    }
  },

  components: {
    AddDataDictionary,
    Pagination,
    TableView
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};