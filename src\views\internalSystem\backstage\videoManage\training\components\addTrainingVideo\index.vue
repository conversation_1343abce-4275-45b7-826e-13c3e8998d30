<template>
  <!-- <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    append-to-body
    @close="dialogCancel"
    width="40%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    v-dialogDrag
  > -->
    <!-- <el-button size="small" @click="dialogCancel">取 消</el-button> -->
    <!-- <el-button size="small" type="success" @click="saveFile()"
      >上传到服务器</el-button
    > -->
    <el-upload
      ref="uploadFile"
      action=""
      :show-file-list="false"
      :http-request="(file) => ossFile(file, index)"
      style="width: 100%; margin-top: 10px"
    >
      <el-button size="small" type="primary" :disabled="!directory_id">添加视频</el-button>
    </el-upload>

    <!-- <el-table
      :data="fileList"
      style="width: 100%; margin-top: 10px"
      border
      max-height="800"
    >
      <el-table-column prop="file_name" label="附件名称" width="180">
      </el-table-column>
      <el-table-column prop="file_size" label="附件大小"> </el-table-column>
      <el-table-column prop="file_suffix" label="附件后缀"> </el-table-column>
      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button
            @click="removeFile(scope.row, scope.$index)"
            type="text"
            size="small"
            >移除</el-button
          >
        </template>
      </el-table-column>
    </el-table> -->
    <!-- <el-form
      ref="form"
      :model="form"
      label-width="80px"
      style="margin-top: 10px"
    >
     <el-form-item label="附件类型">
        <el-select v-model="form.file_type" placeholder="请选择">
          <el-option label="视频" value="1"></el-option>
          <el-option label="文档" value="2"></el-option>
        </el-select>
      </el-form-item> 
    </el-form> -->
  <!-- </el-dialog> -->
</template>
<script src="./index.js">
</script>