
import { sortTable } from "@/common/global/vxeTableDrag.js";
export default {
  
  name: "tableView",
  props: {
    tableData: {
      default () {
        return {}
      },
      type: Array
    },
    tableList: {
      default () {
        return {}
      },
      type: Array
    },
    isEdit: {
      default: '',
      type: String
    },
    isDel: {
      default: '',
      type: String
    },
    isSel: {
      default: false,
      type: Boolean
    },
    isRadio: {
      default: false,
      type: Boolean
    },
    tableHeight: {
      type: Number
    },
    isThrid: {
      default: '',
      type: String
    },
    thridTitle: {
      type: String
    },
    isFour: {
      default: '',
      type: String
    },
    fourTitle: {
      type: String
    },
    isFive:{
      default: '',
      type: String
    },
    fiveTitle: {
      type: String
    },
    handleWidth: {
      type: Number,
      default: 120
    },
    isOperation:{  //是否显示操作
      type: Boolean,
      default: true
    },
    //是否双击
    isDblclick:{
      type: Boolean,
      default: false
    },
    isOrder:{
      type: Boolean,
      default: false
    },
    isTableIndex:{
      type: Boolean,
      default: false
    },
    tableId:{
      default:'tableView'
    }
  },
  data() {
    return {
      loading:false,
      sortable:""
    }
  },
  mounted() {
    this.columnDrop();
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  },
  methods: {
    columnDrop() {
      this.$nextTick(() => {
        let xTable = this.$refs.xTable;
        this.sortable = sortTable(xTable);
      });
    },
    getSelectRecords() {
      let records = [];
     
      records = this.$refs.xTable.getCheckboxRecords(); 
      this.$emit("getSelectRecords", records);
    },
    getRadioRow({ row }){
      this.$emit("getRadioRow", row);
    },
    modify(item) {
      this.$emit("modify", item);
    },
    del(item) {
      if(item.data_state ===2){
        return this.error('该记录是旧数据，请上旧系统继续操作')
      }
      this.$confirm('此操作将删除该条记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit("del", item);
      }).catch(() => {})
    },
    thrid(item) {
      this.$emit("thrid", item);
    },
    four(item) {
      this.$emit("four", item);
    },
    five(item) {
      this.$emit("five", item);
    },
    
    rowDblclick({row, column, $event}){
      if(this.isDblclick){
        this.$emit("rowDblclick", row, column, $event);
      }
    },
    sortChange({ column, property, order }){
      if(this.isOrder){
        this.$emit("sortChange", { column, prop:property, order });
      }
    },
    tableRowClassName() {

      // if (row.balance_days > 30) {
      //   return '';
      // } else if (row.balance_days <=30 && row.balance_days >=0) {
      //   return 'danger-row';
      // } else if(row.balance_days <0){
      //   return 'death-row';
      // }
      return '';
    }
  }
};