#erp {
  .table-list-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    //表头
    .table-headform {
      .head-ruleForm {
        margin-top: 10px;

        .el-row {
          /* 解决表单内各项目高度不一致的问题 */
          .el-col {
            .el-form-item {
              .el-form-item__content {
                height: 32px;
              }
            }
          }
        }
      }

      flex: 0 0 auto;
      height: auto;
      width: 100%;
      padding: 0 20px;
      box-sizing: border-box;
      margin-left: auto;
      margin-right: auto;
      border-bottom: 1px solid #ccc;
      user-select: none;

      .is-label {
        display: flex;
        margin-bottom: 10px;

        label {
          width: auto;
          flex: 0 0 auto;
          font-size: 14px;
          line-height: 32px;
          margin-right: 5px;
        }
      }
    }
    //按钮组
    .table-box-mainButton {
      height: auto;
      flex: 0 0 auto;
      width: 100%;
      box-sizing: border-box;
      margin: 5px 0;
      display: flex;

      .btn-warp {
        flex: 1;
        overflow-x: auto;
        margin: 0 10px;
        display: flex;

        &::-webkit-scrollbar {
          height: 0;
        }
      }

      .hidden-warp {
        flex: 0 0 32px;
        width: 32px;
        margin-right: 10px;
        height: 100%;
      }
    }
    //表格列表
    .tableList {
      flex: 1;
      height: 100%;
      // width: calc(100vw - (180px + 40px));
      width: 100%;
      margin-left: auto;
      margin-right: auto;
      overflow: hidden;
      display: flex;

      /*表格隐藏的定位*/
      .main-table {
        position: relative;
        height: 100%;
        width: 100%;

        .vxe-table--header-wrapper {
          height: 41px;
        }

        .hiddenButton {
          position: absolute;
          top: -37px;
          right: 10px;
          width: 32px;
          height: 32px;

          &.vxe-toolbar {
            padding: 0;
          }

          .vxe-button--wrapper {
            display: none;
          }
        }
      }

      //下面两个变量是基础资料类别树的类
      .leftTree {
        height: 100%;
        flex: 0 0 250px;
        border: 1px solid #eee;
      }

      .rightTable {
        padding-left: 10px;
        height: 100%;
        flex: 1;
        overflow: auto;
      }
    }
    //双表的列表
    .doubleTableList {
      flex: 1;
      height: 100%;
      width: calc(100vw - (180px + 40px));
      margin-left: auto;
      margin-right: auto;
      display: flex;
      flex-direction: column;

      /*双表，一个占三分之一，一个占三分之二*/
      .tableBox1 {
        min-height: 150px;
        flex: 1;
        position: relative;
      }

      .tableBox2 {
        min-height: 250px;
        flex: 2;
        position: relative;
      }
    }
    //分页条
    .table-pagination {
      flex: 0 0 auto;
      height: auto;
    }

    /*用来自定义样式，部分双表的需要 中间的字，用flex三等分  库存组装、拆卸，生产加工*/
    
    .tableAuto {
      flex: 0 0 45px;
      width: 100%;
      box-sizing: border-box;
      padding: 0 10px;
      justify-content: space-between;
      align-items: center;

      div {
        display: flex;
        flex: 1;
      }

      div:nth-child(1) {
        justify-content: flex-start;
      }
      div:nth-child(2) {
        text-align: center;
        font-weight: 700;
        line-height: 30px;
        height: 30px;
        overflow: hidden;
        justify-content: center;
      }
      div:nth-child(3) {
        justify-content: flex-end;
      }
    }
    .table-statistics {
      flex: 0 0 auto;
      height: auto;
    }

    .table-remake {
      flex: 0 0 auto;
      margin-top: 5px;
      margin-bottom: 5px;
      height: auto;
    }
  }
}
