import API from '@/api/internalSystem/customerManage/customerInfo'
import auditAPI from '@/api/internalSystem/common/auditInfo.js'
import CustomerList from "@/views/internalSystem/backstage/components/customerList/index.vue";
import comAPI from '@/api/internalSystem/common/index.js'
import validationRules from "@/common/internalSystem/validationRules.js"
import {
  getOptions
} from "@/common/internalSystem/common.js"
import {
  mapGetters
} from 'vuex'
export default {
  name: "upCustomer",
  components: {
    CustomerList
  },
  props: {
    customerTempId: {
      type: Number,
      default: () => {
        return null
      }
    },
  },
  watch: {
    customerTempId: {
      handler(newValue, oldValue) {
    

        if(newValue){
          this.Show(newValue)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      ruleForm: {
        customer_id: "",
        customer_no: "",
        fk_sale_employee_id: "",
        link_man: "",
        fk_sale_employee_id_number: "",
        customer_name: "",
        phone: "",
        fk_sale_employee_id_name: "",
        customer_synopsis: "",
        telephone: "",
        department_name: "",
        customer_legal_name: "",
        email: "",
        company_site: "",
        customer_legal_person: "",
        qq: "",
        personnel_scale_low: "",
        personnel_scale_high: "",
        customer_type: "",
        fax: "",
        company_scale_low: "",
        company_scale_high: "",
        belong_industry: "",
        postal_code: "",
        province: "",
        customer_source: "",
        important_rank: "",
        city: "",
        introducer: "",
        introducer_name: "",
        link_address: "",
        customer_stage: 1
      },
      rules: {
        customer_no: [{
          required: true,
          message: " "
        }],
        link_man: [{
          required: true,
          message: " "
        }],
        customer_name: [{
          required: true,
          message: " "
        }],
        telephone: [{
          required: true,
          validator: validationRules.checkPhone
        }],
        customer_legal_name: [{
          required: true,
          message: " "
        }],
        customer_type: [{
          required: true,
          message: " "
        }],
        belong_industry: [{
          required: true,
          message: " "
        }],
        province: [{
          required: true,
          message: " "
        }],
        customer_source: [{
          required: true,
          message: " "
        }],
        city: [{
          required: true,
          message: " "
        }],
        // email: [{
        //   type: "email",
        //   message: "请输入正确的邮箱格式"
        // }]
      },
      financeForm: {
        opening_bank: "",
        customer_account: "",
        customer_tax_number: "",
        open_ticket_address: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: ""
      },
      financeRules: {
        opening_bank: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        customer_account: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        customer_tax_number: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        open_ticket_address: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        open_ticket_phone: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        receive_ticket_address: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        receive_ticket_person: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        receive_ticket_phone: [{
          required: true,
          message: " ",
          trigger: "blur"
        }]
      },
      provinceList: [], //省
      cityList: [], //市
      loading: false,
      checked: true,
      isDisabled:false
    };
  },
  mounted() {
    this.getProvinceList();
  },
  methods: {
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel(flag=true) {
      if(flag){
        this.isDisabled = false
        this.$emit("selectData");
      }
      this.resetForm('ruleForm');
      this.resetForm('financeForm');
      this.clearData();


    },
    save() {
      let params = this.ruleForm;
      let flag = true;
      if (this.checked) {
        this.$refs['financeForm'].validate(valid => {
          if (!valid) {
            flag = false;
          } else {
            params = Object.assign(params, this.financeForm);
          }
        });
      }
      // if(!params.email){
      //   return this.error('请填写邮箱')
      // }
      if (!flag) return;
      if (this.ruleForm.personnel_scale_low && this.ruleForm.personnel_scale_high)
        params.personnel_scale = this.ruleForm.personnel_scale_low + "-" + this.ruleForm.personnel_scale_high;
      if (this.ruleForm.company_scale_low && this.ruleForm.company_scale_high)
        params.company_scale = this.ruleForm.company_scale_low + "-" + this.ruleForm.company_scale_high;
      params.is_add_finance = this.checked ? 1 : 0;
      params.customer_name = params.customer_name.replace(/\s*/g, "");
      this.loading = true;
      API.addCustomerTemp(params)
        .then(() => {
          this.dialogCancel(false);
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    //选择介绍人
    chooseIntroducer() {
      this.$refs.dealCustomerList.Show();
    },
    //获取介绍人
    getInfo(info = {}) {
      this.ruleForm.introducer = info.customer_id;
      this.ruleForm.introducer_name = info.customer_name;
    },
    async Show(customer_temp_id) {
      let params = {
        customer_temp_id: customer_temp_id,
      };
      auditAPI.updateInfo(params)
        .then((data) => {

          this.ruleForm = data.data
          this.financeForm = data.data
          this.isDisabled = true
        })
        .catch(() => {});


    },
    //选择客户
    chooseCustomer() {
      this.$refs.customerList.Show();
    },
    getCustomerInfo(info = {}) {
      API.getInfo({
          customer_id: info.customer_id
        })
        .then(data => {
          this.ruleForm = data.data;
          if (!data.data.customer_finance_info_id) {
            this.checked = false;
          } else {
            this.checked = true;
            this.financeForm.opening_bank = data.data.opening_bank;
            this.financeForm.customer_account = data.data.customer_account;
            this.financeForm.customer_tax_number = data.data.customer_tax_number;
            this.financeForm.open_ticket_address = data.data.open_ticket_address;
            this.financeForm.open_ticket_phone = data.data.open_ticket_phone;
            this.financeForm.receive_ticket_address = data.data.receive_ticket_address;
            this.financeForm.receive_ticket_person = data.data.receive_ticket_person;
            this.financeForm.receive_ticket_phone = data.data.receive_ticket_phone;
          }
          if (data.data.personnel_scale) {
            this.ruleForm.personnel_scale_low = data.data.personnel_scale.split("-")[0];
            this.ruleForm.personnel_scale_high = data.data.personnel_scale.split("-")[1];
          }
          if (data.data.company_scale) {
            this.ruleForm.company_scale_low = data.data.company_scale.split("-")[0];
            this.ruleForm.company_scale_high = data.data.company_scale.split("-")[1];
          }
          this.getCityList();
        })
        .catch(() => {});
    },
    //获取省列表
    getProvinceList() {
      comAPI
        .queryAreaCode({
          level: 1
        })
        .then(data => {
          this.provinceList = data.data;
        })
        .catch(() => {});
    },
    //更换省
    changeProvince() {
      this.cityList = [];
      this.ruleForm.city = "";
      this.getCityList();
    },
    //获取市列表
    getCityList() {
      if (!this.ruleForm.province) return;
      comAPI
        .queryAreaCode({
          level: 2,
          province: this.ruleForm.province
        })
        .then(data => {
          this.cityList = data.data;
        })
        .catch(() => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_id: "",
        customer_no: "",
        fk_sale_employee_id: "",
        link_man: "",
        fk_sale_employee_id_number: "",
        customer_name: "",
        phone: "",
        fk_sale_employee_id_name: "",
        customer_synopsis: "",
        telephone: "",
        department_name: "",
        customer_legal_name: "",
        email: "",
        company_site: "",
        customer_legal_person: "",
        qq: "",
        personnel_scale_low: "",
        personnel_scale_high: "",
        customer_type: "",
        fax: "",
        company_scale_low: "",
        company_scale_high: "",
        belong_industry: "",
        postal_code: "",
        province: "",
        customer_source: "",
        important_rank: "",
        city: "",
        introducer: "",
        introducer_name: "",
        link_address: "",
        customer_stage: 1
      }
      this.financeForm = {
        opening_bank: "",
        customer_account: "",
        customer_tax_number: "",
        open_ticket_address: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: ""
      }

    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'params_constant_customer_stage',
      'params_constant_customer_type',
      'params_constant_belong_industry',
      'params_constant_customer_source',
      'params_constant_important_rank'
    ]),
    fkSaleEmployeeUserInfo() {
      let name = ``
      if(this.ruleForm.fk_sale_employee_id_name && this.ruleForm.fk_sale_employee_id_number && this.ruleForm.department_name){
         name =  this.ruleForm.fk_sale_employee_id_name + '-' +
        this.ruleForm.fk_sale_employee_id_number + '-' + 
         this.ruleForm.department_name 
      }

       return name
      }
  },
};