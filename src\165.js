let hmtlStr = `

    <textarea onmousedown="rightEvent(this,event)" class="textareas_td"
    style="font-size: 30px; margin-bottom: 2px; height: 26px; font-weight: 700;margin-top: 30px;"
    onchange="makeExpandingArea(this)">软 件 购 买 合 同 书</textarea>
    
            <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 24px; font-size: 22px; font-weight: 700; margin-top: 10px;"
    onchange="makeExpandingArea(this)">甲方：{{contract.customerName}}</textarea>
            <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 24px; font-size: 22px; font-weight: 700; margin-bottom: 10px;"
            onchange="makeExpandingArea(this)">乙方：{{salesUnit.companyName}}</textarea>

<textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 38px; font-size: 18px;"
    onchange="makeExpandingArea(this)">    甲乙双方经友好协商一致， 甲方向乙方购买软件产品，根据《中华人民共和国合同法》及其他法律法规签订本合同，并由双方共同恪守。条款如下：</textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
    style="width: 100%; height: 24px; font-size: 20px; font-weight: 700; margin-top:4px;" rows="1"
    onchange="makeExpandingArea(this)">一、软件产品名称、规格、价格：  </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 19px; font-size: 18px;"
    rows="1"
    onchange="makeExpandingArea(this)">    乙方向甲方销售的软件产品为：</textarea>

<table width="100%" border="2" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
    <tbody>
            <tr>
                    <td style="width:50px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">序号</textarea></td>
                    <td style="width:425px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">产品名称</textarea></td>
                    <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">版本</textarea></td>
                    <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">单位</textarea></td>
                    <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">合同数量</textarea></td>
                    <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">合同单价(元)</textarea></td>
                    <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">合同金额(元)</textarea></td>
            </tr>
            <start>
            <tr>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)" style="font-size: 18px;">1</textarea></td>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="{{contractDetail.rows}}"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">{{contractDetail.brandName}}</textarea>
                    </td>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)" style="font-size: 18px;">{{contractDetail.brandVersion}}</textarea></td>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);">套</textarea>
                    </td>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">1</textarea></td>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">{{contractDetail.contractAmount}}</textarea></td>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">{{contractDetail.contractAmount}}</textarea></td>
            </tr>
        </start>
            <tr>
                    <td colspan="2"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">共计人民币（大写）：</textarea></td>
                    <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                    <td colspan="5"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">{{contract.upCase}}</textarea> </td>
                    <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                    <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                    <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                    <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                    rows="1" onchange="makeExpandingArea(this)"></textarea></td>
            </tr>

    </tbody>
</table>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
    style="width: 100%; height: 24px; font-weight: 700; font-size: 20px; margin-top:4px;" rows="1"
    onchange="makeExpandingArea(this)">二、付款方式：  </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 38px; font-size: 18px;"
    rows="1"
    onchange="makeExpandingArea(this)">    合同签订后且十五个工作日内甲方支付乙方{{contractDetail.upCasePreAmount}}(￥{{contractDetail.preAmount}}元)作为软件款，含税票:增值税发票、盒装软件包，款到授权正式版本。</textarea><textarea
    onmousedown="rightEvent(this,event)" class="textareas"
    style="width: 100%; height: 24px; font-weight: 700; font-size: 20px;margin-top:4px;" rows="1"
    onchange="makeExpandingArea(this)">三、版权归属及保密条款：  </textarea>

<textarea onmousedown="rightEvent(this,event)" class="textareas"
    style="width: 100%; height: 38px; font-size: 18px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);"
    onchange="makeExpandingArea(this)">    本软件之版权归乙方，甲方经由乙方授权永久使用本软件，其数据版权归甲方。乙方对在本合同签订及履行过程中知悉的甲方的商业秘密(采购价格，采购渠道和供应商单位信息,销售价格，销售客户信息)负责保密，保证不向任何第三方进行透露并使用.同时甲方需对本合同价格、软件功能签订的条款等保密，不得向其他用户透入，如造成乙方损失必须赔偿。</textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
    style="width: 100%; height: 24px; font-weight: 700; font-size: 20px; margin-top:4px;" rows="1"
    onchange="makeExpandingArea(this)">四、服务项目：  </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 144px; font-size: 18px;"
    onchange="makeExpandingArea(this)">    1、安装调试：软件的安装、调试均由乙方负责，除合同条款商定的费用以外，免收软件的安装、调试费用，免费维护期过后的维护费用。
    2、培训：乙方为甲方提供软件操作的免费培训，培训的方式为：远程培训，程度达到公司员工熟练操作。				
    3、乙方对系统提供终身技术支持，免费维护期满后双方须签订《软件服务协议》。
    4、售后维护：
      A、乙方在国家正常工作日内随时为甲方以电话、传真、电子邮件方式免费提供所买产品的服务与技术支持维护，通常软件系统故障，提供实时响应远程解决。
      B、非正常使用造成软件损坏，乙方负责收费保修。
    5、自甲方购买软件之日起{{contract.maxNumber}}年内，乙方为甲方提供如下免费的维护服务
      A、具体内容：维护软件现有功能的正常使用，免费升级大众化功能；
      B、在正常使用情况下，乙方对所提供的软件提供{{contract.maxNumber}}年的免费保修本软件的问题与漏洞。</textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
    style="width: 100%; height: 24px; font-weight: 700; font-size: 20px; margin-top:4px;" rows="1"
    onchange="makeExpandingArea(this)">五、争议解决方式：    </textarea>
    
<textarea onmousedown="rightEvent(this,event)" class="textareas"
    style="width: 100%; height: 38px; font-size: 18px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);"
    onchange="makeExpandingArea(this)">    1、双方合同履行过程中发生争议，双方应协商解决或请求调解，否则应提交合同签定地仲裁机关仲裁。 
    2、甲乙双方确定：以上合同签定，以乙方所在地为准。 
    3、未尽事宜双方友好协商解决。  </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
    style="width: 100%; height: 24px; font-weight: 700; font-size: 20px; margin-top:4px;" onchange="makeExpandingArea(this)"
    rows="1">六、本合同正本壹式贰份，甲乙双方各执壹份，经双方盖章后生效。 </textarea>

<table width="100%" border="2" cellspacing="0" cellpadding="0" style="border-collapse: collapse; margin-top: 10px;">
    <tbody>
            <tr>
                    <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px; height: 30px;">甲方：</textarea></td>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">{{contract.customerName}}</textarea>
                    </td>
                    <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px; height: 30px;">乙方：</textarea></td>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px;">{{salesUnit.companyName}}</textarea>
                    </td>
            </tr>
            <tr>
            <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                            rows="1" onchange="makeExpandingArea(this)"
                            style="font-size: 18px; height: 30px;">代理人签字</textarea></td>
            <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                            onchange="makeExpandingArea(this)"
                            style="font-size: 18px;">{{contract.linkMan}}</textarea></td>
            <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                            rows="1" onchange="makeExpandingArea(this)"
                            style="font-size: 18px; height: 30px;">代理人签 字</textarea></td>
            <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                            onchange="makeExpandingArea(this)"
                            style="font-size: 18px;">{{contract.sellEmployeeName}}</textarea>
            </td>
    </tr>
            <tr>
                    <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px; height: 30px;">电     话：</textarea></td>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px; height: 30px;line-height: 38px;">{{contract.customerTelephone}}</textarea>
                    </td>
                    <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                    rows="1" onchange="makeExpandingArea(this)"
                                    style="font-size: 18px; height: 30px;">电     话：</textarea></td>
                    <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                    onchange="makeExpandingArea(this)"
                                    style="font-size: 18px; height: 30px;line-height: 38px;">{{contract.sellTel}}</textarea></td>
            </tr>
            <tr>
            <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                            rows="1" onchange="makeExpandingArea(this)"
                            style="font-size: 18px; height: 30px;">日     期：</textarea></td>
            <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                            onchange="makeExpandingArea(this)"
                            style="font-size: 18px; height: 30px;line-height: 38px;">{{contract.createTimeStr}}</textarea>
            </td>
            <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                            rows="1" onchange="makeExpandingArea(this)"
                            style="font-size: 18px; height: 30px;">日     期：</textarea></td>
            <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                            onchange="makeExpandingArea(this)"
                            style="font-size: 18px; height: 30px;line-height: 38px;">{{contract.createTimeStr}}</textarea></td>
    </tr>
    </tbody>
</table>
`

export function getHtmlStr() {
    // console.log(hmtlStr);
    return hmtlStr;
  
  }