  import axios from "@/api/index.js";
  import environment from "@/api/environment";
  export default {
    //系统当前岗位所有菜单
    menuList: params => {
      return axios.post(`${environment.basicAPI}basicDataApi/systemAllMenuByToken`, params);
    },
    //erp定制版接口 modularNames：array 传模块中文名称
    erpHomeMenuList: params => {
      return axios.post(`${environment.tradeAPI}basicDataApi/functionMenuList`, params);
    },
    // 获取参数常量
    getConstant: params => {
      return axios.post(`${environment.internalSystemAPI}constant/query`, params)
    }
  }