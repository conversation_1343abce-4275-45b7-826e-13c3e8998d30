<template>
  <el-form
  class="mt10"
    :model="head"
    :rules="rules"
    :status-icon="true"
    ref="headForm"
    label-width="80px"
  >
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="单据编号" prop="voucherCode">
          <el-input
            type="text"
            placeholder="YHZZ-XXXX-9999"
            v-model="head.voucherCode"
            autocomplete="off"
            readonly
            :disabled="true"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="单据日期" prop="voucherDate">
          <el-input
            type="text"
            v-model="head.voucherDate"
            autocomplete="off"
            readonly
            :disabled="true"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="制单员" prop="userId">
          <el-input
            type="text"
            v-model="head.userName"
            autocomplete="off"
            readonly
            :disabled="true"
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  props: {
    isEdit: {
      type: Boolean,
      required: true,
      default: true
    }
  },
  data() {
    return {
      head: {  //头部数据

      },
      rules: {   //表单验证
        userId: [
          { required: true, message: '制单人未填', trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
       //重置表单数据的方法
       ResetHead() {  
      Object.assign(this.$data.head, this.$options.data().head);
      this.head.userName = this.userInfo.fullName;
      this.head.userId = this.userInfo.userId;
    },
    //设置数据
     SetHeadData(headData) {   
      this.head = headData;
    },
    //效验表单
    ValidationData() {
      this.$refs.headForm.validate((valid) => {
        if (valid) {
          return true;
        } else {
          return false;
        }
      });
    },
  },
  components: {
    ...mapGetters(["userInfo"])
  }
}
</script>

<style scoped lang="scss">
</style>
