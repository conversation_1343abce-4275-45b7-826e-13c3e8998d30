<template>
  <div class="body-p10">
    <el-form
      :inline="true"
      :model="formSearch"
      size="small"
      v-if="!isAdd && !isDetail && !isTracking && !isDblclick"
    >
      <el-form-item label="查询条件">
        <el-input
          v-model="formSearch.customerName"
          placeholder="请输入客户名称或联系人"
          style="width: 200px"
          clearable
        />
      </el-form-item>
      <el-form-item >
        <el-input
          v-model="formSearch.customerNameOld"
          placeholder="请输入客户曾用名"
          style="width: 200px"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.fk_sale_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"

        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.customerStage"
          placeholder="请选择客户阶段"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in customer_stage"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.startTime"
          hint="维护开始时间"
          style="width: 140px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.endTime"
          hint="维护结束时间"
          style="width: 140px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.createTimeStart"
          hint="创建开始时间"
          style="width: 140px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.createTimeEnd"
          hint="创建开始时间"
          style="width: 140px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.expirationTime"
          placeholder="过期时间"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option label="30天过期" value="30"/>
          <el-option label="60天过期" value="60"/>
          <el-option label="90天过期" value="90"/>
          <el-option label="过期" value="-1"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button
          type="primary"
          @click="add"
          v-permit="'ADD_CUSTOMER_INFO_NEW'"
          >制单</el-button
        >
        <el-button type="primary" @click="handleExcel(1)" 
          >本页导出</el-button
        >
        <el-button type="primary" @click="handleExcel(2)" 
          >全部导出</el-button
        >
        <!-- <el-button
          type="primary"
          @click="exportCustomer"
          :loading="exLoading"
          v-permit="'CUSTOMER_INFO_EXPORT_NEW'"
          >导出
        </el-button> -->
        <el-button
          type="primary"
          @click="refreshWechatBindingState"
          :loading="refreshWechatBindingStateLoading"
          v-permit="'CUSTOMER_INFO_REFRESH_WECHAT_NEW'"
          >更新客户公众号绑定
        </el-button>
      </el-form-item>
    </el-form>
    <TableView
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd && !isDetail && !isTracking && !isDblclick"
      isEdit="update"
      isDel="DEL_CUSTOMER_INFO_NEW"
      isThrid="CUSTOMER_INFO_TRACKING_NEW"
      thridTitle="跟踪"
      @getSelectRecords="getSelectRecords"
      @modify="modify"
      @del="del"
      @thrid="tracking"
      @rowDblclick="rowDblclick"
      @sortChange="sortChange"
      isFour="true"
      fourTitle="推送"
      @four="push"
      :handleWidth="160"
      :isDblclick="true"
      :isOrder="true"
    />
    <Pagination
      ref="pagination"

      @success="getList"
      v-show="!isAdd && !isDetail && !isTracking && !isDblclick"
    />
    <!-- 新增修改客户信息 -->
    <AddCustomer v-if="isAdd" @selectData="getList" />
    <CustomerDetail ref="CustomerDetail" @selectData="getList" />
    <CustomerTracking ref="CustomerTracking" @selectData="getList" />
    <!-- 双击弹窗 展示 客户资料+产品信息 -->
    <CustomerProduct ref="CustomerAndProduct" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.moneyTitle {
  font-size: 14px;
  font-weight: bold;
}

@import "@/assets/css/element/font-color.scss";
</style>