#erp,
#productErp {


  .addBtn {
    margin-left: 5px;
    padding: 5px 10px;
    height: 32px;
    border: 1px solid rgba(0, 150, 136, 0.6);
    border-radius: 3px;
    background: none;
    color: #009688;

    &:active {
      background: rgba(0, 150, 136, 0.05);
      border-color: rgba(0, 150, 136, 1);
    }
  }

  .warnBtn {
    margin-left: 5px;
    padding: 5px 10px;
    height: 32px;
    border: 1px solid rgba(183, 122, 51, 0.6);
    border-radius: 3px;
    background: none;
    color: #b77a33;

    &:active {
      background: rgba(183, 122, 51, 0.05);
      border-color: rgba(183, 122, 51, 1);
    }
  }

  .saveBtn {
    margin-left: 5px;
    padding: 5px 10px;
    height: 32px;
    border: 1px solid rgb(0, 147, 133);
    border-radius: 3px;
    background: #009688;
    color: #fff;

    &:active {
      background-color: rgba(0, 147, 133, 0.8);
    }
  }

  .darkBtn {
    margin-left: 5px;
    padding: 5px 10px;
    height: 32px;
    border: 1px solid rgb(51, 122, 183);
    border-radius: 3px;
    background: #337AB7;
    color: #fff;

    &:active {
      background-color: rgba(51, 122, 183, 0.8);
    }
  }

  .otherBtn {
    margin-left: 5px;
    padding: 5px 10px;
    height: 32px;
    border: 1px solid rgba(51, 122, 183, 0.6);
    border-radius: 3px;
    background: none;
    color: #337AB7;

    &:active {
      background-color: rgba(51, 122, 183, 0.05);
      border-color: rgba(51, 122, 183, 1);
    }
  }

  .delBtn {
    margin-left: 5px;
    padding: 5px 10px;
    height: 32px;
    border: 1px solid rgba(217, 83, 79, 0.6);
    border-radius: 3px;
    background: none;
    color: #D9534F;

    &:active {
      background: rgba(217, 83, 79, 0.05);
      border-color: rgba(217, 83, 79, 1);
      ;
    }

  }

  .deleteBtn {
    margin-left: 5px;
    padding: 5px 10px;
    height: 32px;
    border: 1px solid #D9534F;
    border-radius: 3px;
    background-color: rgb(217, 83, 79);
    color: #fff;

    &:active {
      background-color: rgba(217, 83, 79, 0.8);
    }
  }

  button {
    &:disabled {
      cursor: not-allowed;
    }
  }
}