<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-select v-model="formSearch.fk_financial_cost_project_id" placeholder="请选择费用项目" class="inputBox" filterable clearable>
          <el-option v-for="item in costProjectList" :key="item.financial_cost_project_id" :label="item.project_no+'-'+item.project_name"
            :value="item.financial_cost_project_id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button type="primary" @click="add"
          v-permit="'ADD_COSTDETAILS_NEW'">添加</el-button>
      </el-form-item>
    </el-form>
    <!-- 新增修改费用明细 -->
    <add-cost-details ref="AddCostDetails" @selectData="getList" />
    <table-view :tableList="tableList" :tableData="tableData" @modify="modify" @del="del"
      :isEdit="'UPDATE_COSTDETAILS_NEW'"
      :isDel="'DEL_COSTDETAILS_NEW'"></table-view>
    <Pagination ref="pagination" @success="getList" />
  </div>
</template>

<script src="./index.js"></script>