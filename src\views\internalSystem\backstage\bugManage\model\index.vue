<template>
  <div class="table-list-box">
    <div class="tableList congested flexRow">
      
      <div class="scroll-container leftJobTree">
        <jobTree ref="jobTree" @setJob="getTreeData" />
      </div>
      <!-- <div class="saveBtnBox">
         <el-button type="primary" circle @click="addJurisdiction" class="saveBtn" :loading="loading">保存</el-button>
        </div> -->
      <div class="scroll-container rightTree">
        <menuTree ref="menuTree" />
      </div>
    </div>
  </div>
</template>

<script src="./index.js">
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>