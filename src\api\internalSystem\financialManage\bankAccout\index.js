import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}bankAccout/query`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}bankAccout/add`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}bankAccout/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}bankAccout/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}bankAccout/getInfo`, params)
  },
  // 查询银行流水
  detailList: params => {
    return Axios.post(`${environment.internalSystemAPI}bankAccout/detailList`, params)
  },
  // 银行资金流转
  addDetail: params => {
    return Axios.post(`${environment.internalSystemAPI}bankAccout/addDetail`, params)
  }
};