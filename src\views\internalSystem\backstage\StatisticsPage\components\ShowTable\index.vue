<template>
  <el-table
    ref="elTable"
    height="100%"
    :data="tableData"
    style="width: 100%"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-table-column type="index" label="序号" width="48" v-if="showIndex"> </el-table-column>
    <el-table-column
      show-overflow-tooltip
      v-for="item in tableHeader"
      :key="item.prop"
      :prop="item.prop"
      :label="item.label"
      :width="item.width"
    >
    </el-table-column>
    <slot></slot>
  </el-table>
</template>

<script>
export default {
  props: {
    tableData: {
      type: Array,
      default() {
        return [];
      },
    },
    tableHeader: {
      type: Array,
      default() {
        return [];
      },
    },
    showIndex:{
      type:Boolean,
      default:false
    }
  },
  mounted() {

  },
  methods: {
    doLayout() {
      this.$refs.elTable.doLayout();
    },
  },
};
</script>

<style lang="scss" scoped></style>
