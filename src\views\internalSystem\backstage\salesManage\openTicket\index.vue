<template>
  <div class="body-p10">
    <el-form
      :inline="true"
      :model="formSearch"
      size="small"
      v-if="!isAdd && !isAudit"
    >
      <el-form-item label="查询条件">
        <el-input
          v-model="formSearch.customer_name"
          placeholder="请输入客户名称"
          clearable
          style="width: 160px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.sales_unit_id"
          placeholder="请选择销货单位"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in salesUnits"
            :key="item.sales_unit_id"
            :label="item.company_name"
            :value="item.sales_unit_id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="formSearch.invoice_number"
          placeholder="请输入发票号码"
          clearable
          style="width: 150px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.fk_sale_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
          :disabled="!['总经理','财务经理','财务专员'].includes(cookiesUserInfo.role_name)"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.audit_state"
          placeholder="请选择审核状态"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in audit_state"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.invoiceTaxRate"
          placeholder="请选择发票税率"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in quotationRateList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.startTime"
          hint="请选择时间"
          style="width: 140px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.endTime"
          hint="请选择时间"
          style="width: 140px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.time_type"
          placeholder="请选择时间类型"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in time_type_list"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button type="primary" @click="add" v-permit="'ADD_OPENTICKET_NEW'"
          >制单</el-button
        >
        <el-button type="success" @click="openInvoiceAttachmentBatch" v-permit="'permanent_button'"
          >发票附件批量发送</el-button
        >
        <el-dropdown @command="handleExcel" style="margin-left: 5px;">
          <el-button type="primary">
            更多导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="1" >本页导出(寄发票格式)</el-dropdown-item>
            <el-dropdown-item command="2" >全部导出(寄发票格式)</el-dropdown-item>
            <el-dropdown-item command="3" >本页导出(全部字段)</el-dropdown-item>
            <el-dropdown-item command="4" >全部导出(全部字段)</el-dropdown-item>

          </el-dropdown-menu>
        </el-dropdown>
        <!-- <el-button type="primary" @click="handleExcel(1)" 
          >本页导出</el-button
        >
        <el-button type="primary" @click="handleExcel(2)" 
          >全部导出</el-button
        > -->
      </el-form-item>
    </el-form>

    <table-view
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd && !isAudit"
      @del="del"
      @modify="modify"
      isEdit="permanent_button"
      :isDel="'DEL_OPENTICKET_NEW'"
      isThrid="AUDIT_OPENTICKET_NEW"
      :thridTitle="'审核'"
      @thrid="(item) => toAuditDet(item, '开票单审核', 'audit_state')"
      isFour="permanent_button"
      :fourTitle="'附件'"
      @four="openFileList"
      :handleWidth="139"
    ></table-view>
    <div class="mt10 moneyTitle" v-if="!isAdd && !isAudit">
      <el-row :gutter="20">
        <el-col :span="4">未税总金额：{{ allList.open_ticket_money }}元</el-col>
        <el-col :span="4">含税总金额：{{ allList.leved_total }}元</el-col>
        <el-col :span="4">税额：{{ allList.tax }}元</el-col>
      </el-row>
    </div>

    <Pagination
      ref="pagination"
      @success="getList"
      v-show="!isAdd && !isAudit"
    />
    <!-- 新增销售开票单 -->
    <AddOpenTicket
      ref="addOpenTicket"
      @selectData="getList"
      @del="del"
      @modify="modify"
    />
    <!-- 审核 -->
    <TicketAudit ref="ticketAudit" v-show="isAudit" @selectData="getList" />
    <!-- 附件列表 -->
    <FileList ref="fileList" />
    <!-- 发票附件批量发送 -->
    <InvoiceAttachmentBatch ref="invoiceAttachmentBatch" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.moneyTitle {
  font-size: 14px;
  font-weight: bold;
}
@import "@/assets/css/element/font-color.scss";
</style>
