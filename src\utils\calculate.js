
/**
 * 数字后面补0
 */
export function PrefixInteger(num, length) {
  const str = (num + "" + Array(length).join('0'))
  return str
}

/**
 * 获取主产品id
 * import { breakUpList } from "@/utils/calculate.js";
 */
export function getMainBrand(type = 'string') {
  if (type === 'list') {
    return [47, 48, 61]
  } 

}


/**
 * 求比分比  
 */
export function GetPercent(num, total) {
  num = parseFloat(num);
  total = parseFloat(total);
  if (isNaN(num) || isNaN(total)) {
    return 0;
  }
  return total <= 0 ? "0" : (Math.round(num / total * 10000) / 100.00);
}

/**
    * 加法运算，避免数据相加小数点后产生多位数和计算精度损失。
    * 
    
    */
export function numAdd(num1, num2) {
  let baseNum, baseNum1, baseNum2;
  try {
    baseNum1 = num1.toString().split(".")[1].length;
  } catch (e) {
    baseNum1 = 0;
  }
  try {
    baseNum2 = num2.toString().split(".")[1].length;
  } catch (e) {
    baseNum2 = 0;
  }
  baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
  return ((num1 * baseNum + num2 * baseNum) / baseNum).toFixed(2);
}

/**
 * 减法运算，避免数据相减小数点后产生多位数和计算精度损失。
 * 
 * @param num1被减数  |  num2减数
 */
export function numSub(num1, num2) {
  num1 = Number(num1).toFixed(2)
  num2 = Number(num2).toFixed(2)
  var baseNum, baseNum1, baseNum2;
  var precision; // 精度
  try {
    baseNum1 = num1.toString().split(".")[1].length;
  } catch (e) {
    baseNum1 = 0;
  }
  try {
    baseNum2 = num2.toString().split(".")[1].length;
  } catch (e) {
    baseNum2 = 0;
  }
  baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
  precision = (baseNum1 >= baseNum2) ? baseNum1 : baseNum2;
  return ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(2);
}
/**
 * 乘法运算，避免数据相乘小数点后产生多位数和计算精度损失。
 * 
 * @param num1被乘数 | num2乘数
 */
export function numMulti(num1, num2) {
  num1 = Number(num1).toFixed(2)
  num2 = Number(num2).toFixed(2)
  var baseNum = 0;

  try {
    baseNum += num1.toString().split(".")[1].length;
  } catch (e) {
  }
  try {
    baseNum += num2.toString().split(".")[1].length;
  } catch (e) {
  }
  return ((Number(num1.toString().replace(".", "")) * Number(num2.toString().replace(".", "")) / Math.pow(10, baseNum)) / 100.00).toFixed(2)

}

export function checkMaintenanceFee(row) {
  let money = 0
  if([1,5].includes(row.detail_sell_type.value)){
    if (
      Number(row.contract_amount.value) &&
      Number(row.contract_count.value)
    ) {
      money =  (Number(row.contract_amount.value) / Number(row.contract_count.value)  * Number(row.year_maintain_cost.value) / 100)
    }

  }else{
    if (
      Number(row.contract_amount.value) &&
      Number(row.year_maintain_cost.value)
    ) {
      money =  (
        (Number(row.contract_amount.value) *
          Number(row.year_maintain_cost.value)) /
        100
      )
    }
  }
  if (Number(money) > 0 && Number(money)  < 500) {
    return Number(500).toFixed(2);
  }
  return Number(money).toFixed(2) || 0;
}
