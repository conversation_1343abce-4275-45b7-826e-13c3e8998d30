<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="660px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
      <el-form-item label="收入编号" v-if="ruleForm.income_id">
        <el-input v-model="ruleForm.income_code" disabled clearable></el-input>
      </el-form-item>
      <el-form-item label="收入类型" prop="income_type">
        <el-select v-model="ruleForm.income_type" placeholder="请选择收入类型" class="inputBox" filterable clearable>
          <el-option v-for="item in income_type" :key="item.id" :label="item.label" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收入银行" prop="fk_bank_account_id">
        <el-select v-model="ruleForm.fk_bank_account_id" placeholder="请选择收入银行" filterable clearable>
          <el-option v-for="item in bankList" :key="item.financial_bank_accout_id" :label="item.bank_name"
            :value="item.financial_bank_accout_id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收入金额" prop="amount">
        <el-input v-model="ruleForm.amount"  clearable></el-input>
      </el-form-item>
      <el-form-item label="收入日期" prop="income_time">
        <my-date v-model="ruleForm.income_time" hint="请选择收入日期" :isAllDate="false"></my-date>
      </el-form-item>
      <el-form-item label="收入明细" prop="content">
        <el-input v-model="ruleForm.content" type="textarea" clearable></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="back"
        v-permit="'AUDIT_BACK_INCOME_NEW'"
        v-if="ruleForm.income_id&&ruleForm.audit_state!=3">
        回退审核</el-button>
      <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>