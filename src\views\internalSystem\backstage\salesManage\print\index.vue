<template>
  <div class="body-p10" style="height:100vh;overflow-y:auto;">
    <div :class="type==='update'?'update':'select'">
      <div ref="printDom" class="wrapper">
        <div id="tes" contenteditable="true" v-html="detail" @change="makeExpandingArea"></div>
        <div v-if="type!=='update'" class="imgDiv">
          
           <img :src="salesUnit.sealImg" :style="{'margin-bottom': (detailNum*25 + 1200) + 'px'}" v-if="contract_template_id === '116' || contract_template_id === '164'"/>
          <img :src="salesUnit.sealImg" />
        </div>
      </div>
      <div v-if="type==='update'" class="mt10 mb10 float-right flexRow button_fixed">
        <span>合同模板名称：</span>
        <el-input v-model="templateName" style="width:160px;margin-right: 10px;" clearable></el-input>
        <el-button type="primary" @click="add" v-if="isAdd">新增模板</el-button>
        <el-button type="primary" @click="update" v-if="isUpdate">保存模板</el-button>
        <el-button type="primary" @click="close">关闭</el-button>
      </div>
      <div v-else class="mt10 mb10 float-right button_fixed">
        <el-button type="primary" @click="print">打印合同</el-button>
        <el-button type="primary" @click="close">关闭打印</el-button>
      </div>

    </div>
  </div>
</template>
<script src="./index.js">

</script>
<style lang="scss" scoped>
  @import './index.css';
</style>