const state = {
	monitor_log_status: [], // 日志状态
	monitor_log_type: [], // 系统类型
	menu_type: [], // 菜单类型
	gender: [], // 性别
	marriage: [], // 婚姻状况
	nation: [], // 民族
	monitor_log_environment: [], //环境名称
	invoice_type: [], //发票类型
	invoice_status: [], //发票状态
	jq_system_order_state: [],//订单状态
	jq_system_order_status: [], //订单授权状态
	jq_system_order_type: [],  //支付类型
	sms_list_status: [], // 短信状态
	jq_order_main_tag: [], // 订单标记类型(在线商城需要用到)
	template_type:[] ,//推送模板类型，小程序商商城可能会用到
	// problem_type:[],//bug类型
	// operating_system:[],//操作系统
	// problem_browser:[], //浏览器
	// problem_state:[]//问题状态

}

const getters = {
	nation: state => state.nation,
	marriage: state => state.marriage,
	gender: state => state.gender,
	menu_type: state => state.menu_type,
	monitor_log_status: state => state.monitor_log_status,
	monitor_log_type: state => state.monitor_log_type,
	monitor_log_environment: state => state.monitor_log_environment,
	invoice_type: state => state.invoice_type,
	invoice_status: state => state.invoice_status,
	jq_system_order_state: state => state.jq_system_order_state,
	jq_system_order_status: state => state.jq_system_order_status,
	jq_system_order_type: state => state.jq_system_order_type,
	sms_list_status: state => state.sms_list_status,
	jq_order_main_tag: state => state.jq_order_main_tag,
	template_type: state => state.template_type,


}

export default {
	state,
	getters,
}
