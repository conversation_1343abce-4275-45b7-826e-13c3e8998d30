<template>
  <div class="container">
    <topTitle class="flex-fixed" />
    <div class="top-nav flex-fixed">
      <i class="iconfont iconleft" @click="$router.go(-1)"></i>
      <p>{{data.GID}}</p>
      <i class="iconfont iconsousuo" @click="$router.replace('/')"></i>
    </div>
    <div class="flex-roll">
      <div class="good-info">
        <!--        <div class="good-img">-->
        <!--          <i class="iconfont icontupian"></i>-->
        <!--        </div>-->
        <div class="good-det">
          <div>
            <span class="good-brand">{{data.GBrand}}</span>
            <span class="good-name">{{data.GName}}</span>
          </div>
          <div class="param-item" style="margin-top: 10px">
            <span class="param-label">
              面价：
            </span>
            <span class="price">￥{{data.GfCpMj}}</span>
          </div>
          <div class="param-item">
            <span class="param-label">
              型号：
            </span>
            {{data.GID}}
          </div>
          <div class="param-item">
            <span class="param-label">订货号：</span>
            {{data.GIndex}}
          </div>
          <div class="param-item">
            <span class="param-label">公司名称：</span>
            {{data.Caption}}
          </div>
          <div class="param-item">
            <span class="param-label">库存数量：</span>
            {{data.FNumb}}{{data.GUnit}}
          </div>
          <div class="param-item">
            <span class="param-label">所在城市：</span>
            {{data.BaCity}}
          </div>
          <div class="param-item">
            <span class="param-label">价格：</span>
            ￥{{data.BPrice || '询价'}}
          </div>
          <div class="param-item">
            <span class="param-label">联系人：</span>
            {{data.BusinessMan}}
          </div>
          <div class="param-item">
            <span class="param-label">联系方式：</span>
            {{data.BusinessTelphone}}
          </div>
        </div>
      </div>
      <div class="qr-container">
        <img :src="qrCode" alt="">
        <p class="qr-des">扫码关注公众号查询更多库存</p>
      </div>
    </div>
    <div class="technicalSupport">技术支持:福州吉勤信息科技有限公司</div>
  </div>
</template>
<script>
import Api from "@/api/wechat_search/index.js";
import topTitle from "../components/topNav";

export default {
  components: { topTitle },
  data() {
    return {
      data: {},
      qrCode: require("@/assets/images/stock/qr-code.png")
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      const ID = this.$route.query.ID;
      if (!ID) return this.$toast("数据异常，请重试");
      Api.details({
        ID
      })
        .then(res => {
          this.data = res.data;
        })
        .catch(() => {});
    }
  }
};
</script>
<style scoped lang="scss">
$searchColor: #00b38b;
$bgColor: #f5f5f5;
$bgColor: #f5f5f5;
.technicalSupport {
  width: 100%;
  height: 30px;
  flex: 0 0 30px;
  left: 0;
  position: absolute;
  text-align: center;
  font-size: 12px;
  line-height: 30px;
  color: #666;
  bottom: 0;
  background-color: $bgColor;
}
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: $bgColor;
}

.flex-fixed {
  flex: 0 0 auto;
}

.flex-roll {
  flex: 1;
  overflow-y: scroll;
}

.top-nav {
  height: 50px;
  padding: 0 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;

  .iconfont {
    font-size: 24px;
    color: $searchColor;
  }

  p {
    font-size: 16px;
    font-weight: 500;
  }
}

.good-info {
  margin-top: 20px;
  display: flex;
  padding: 10px;
  border-top: 0.5px solid #eee;
  border-bottom: 0.5px solid #eee;
  background-color: white;

  .good-img {
    width: 180px;
    height: 180px;
    background-color: #f7f7f7;
    display: flex;
    justify-content: center;
    align-items: center;

    .iconfont {
      font-size: 30px;
      color: #e5e5e5;
    }
  }

  .good-det {
    flex: 1;
    margin-left: 30px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .good-brand {
      font-size: 12px;
      background: $bgColor;
      display: inline-block;
      border-radius: 4rem;
      color: #999;
      padding: 4px 10px;
      margin-right: 10px;
    }

    .good-name {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    .price {
      color: #ff6600;
      margin-top: 10px;
      line-height: 24px;
      font-size: 14px;

      span {
        color: #999;
      }
    }

    .param-item {
      font-size: 12px;
      line-height: 22px;
      color: #333;

      .param-label {
        display: inline-block;
        width: 70px;
        color: #999;
      }
    }
  }
}

.qr-container{
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  img{
    width: 30%;
  }
  .qr-des{
    font-size: 12px;
    color: #999999;
  }
}
</style>
