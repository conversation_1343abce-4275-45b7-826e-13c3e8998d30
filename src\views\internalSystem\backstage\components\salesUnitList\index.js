import API from '@/api/internalSystem/basicManage/salesUnit'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import {
  mapGetters
} from "vuex";
export default {
  name: "salesUnitList",
  components: {
    TableView,
    Pagination
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        company_name: ""
      },
      tableList: [{
          name: "公司名称",
          value: "company_name"
        },
        {
          name: "公司简称",
          value: "company_short_name",
          width: 100
        },
        {
          name: "公司税号",
          value: "company_tax_number",
          width: 120
        },
        {
          name: "公司法人",
          value: "company_legal_person",
          width: 70
        },
        {
          name: "开户行",
          value: "opening_bank",
          width: 100
        },
        {
          name: "银行账号",
          value: "bank_account",
          width: 120
        },
        {
          name: "公司开票地址",
          value: "open_bill_address"
        },
        {
          name: "公司开票电话",
          value: "open_bill_phone",
          width: 100
        },
        {
          name: "公司经营地址",
          value: "operate_address"
        },
        {
          name: "传真号码",
          value: "fax",
          width: 110
        },
        {
          name: "邮政编码",
          value: "postal_code",
          width: 70
        }
      ]
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "销货单位列表"
    }
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.getList();
        });
      // }, 300);
    },
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.unit_pagination.obtain());
      if (f)
        param.pageNum = 1;
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.unit_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    //提交
    submitForm() {
      if (this.selectRecords.length != 1)
        return this.error("请选择一条记录");
      this.$emit("getInfo", this.selectRecords[0]);
      this.dialogCancel();
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.selectRecords = [];
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
    rowDblclick(row, column, event){
      this.selectRecords=[]
      this.selectRecords.push(row)
      this.submitForm()
    }
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};