import VXETable from "vxe-table";

VXETable.setup({
  // 默认表格参数
  size: "small",
  showOverflow: true,
  showHeaderOverflow: true,
  align: "center",
  headerAlign: "center",
  stripe: false,
  border: true,
  resizable: true,
  showHeader: true,
  autoResize:true,
  highlightCurrentRow: false,
  highlightHoverRow: false,
  highlightCurrentColumn: false,
  highlightHoverColumn: false,
  export: {}, // 导出默认参数
  import: {}, // 导入默认参数
  zIndex: 100, // 全局 zIndex 起始值
  rowId: "_XID",
  radioConfig: {
    trigger: "default"
  },
  checkboxConfig: {
    trigger: "default"
  },
  editConfig: {
    trigger: "click",
    mode: "cell",
    showStatus: false,
    icon: "default"
  },
  sortConfig: {
    remote: false,
    trigger: "default"
  },
  filterConfig: {
    remote: false
  },
  expandConfig: {
    trigger: "default"
  },
  treeConfig: {
    children: "children",
    hasChild: "hasChild",
    indent: 20
  },
  tooltipConfig: {
    theme: "dark"
  },
  validConfig: {
    message: "default"
  },
  // 版本号（对于某些带 Storage 数据储存的功能有用到，上升版本号可以用于重置 Storage 数据）
  version: 0,
  // 配置式表格的默认参数
  grid: {
    proxyConfig: {
      autoLoad: true,
      message: true,
      props: {
        list: null,
        result: "result",
        total: "page.total"
      }
    }
  },
  // 默认快捷菜单
  menu: {},
  // 默认 tooltip 主题样式
  tooltip: {
    trigger: "hover",
    theme: "dark"
  },
  // 默认分页参数
  pager: {
    perfect: true,
    pageSize: 10,
    pagerCount: 7,
    pageSizes: [10, 15, 20, 50, 100],
    layouts: [
      "PrevJump",
      "PrevPage",
      "Jump",
      "PageCount",
      "NextPage",
      "NextJump",
      "Sizes",
      "Total"
    ] // 非常灵活的分页布局，支持任意位置随意换
  },
  // 默认工具栏参数
  toolbar: {
    refresh: true,
    import: {
      mode: "covering"
    },
    zonm: true,
    export: {
      types: ["csv", "html", "xml", "txt"]
    },
    resizable: {
      storage: false
    },
    custom: {
      storage: false,
      isFooter: true
    },
    buttons: []
  },
  // 默认模态窗口参数
  modal: {
    minWidth: 340,
    minHeight: 200,
    lockView: true,
    mask: true,
    duration: 3000,
    marginSize: 8,
    dblclickZoom: true,
    remember: false,
    animat: true
  },
  // 默认优化配置项
  optimization: {
    animat: true,
    delayHover: 250,
    scrollX: {
      gt: 100
    },
    scrollY: {
      gt: 500
    }
  }
});

// 创建一个简单输入框渲染器
VXETable.renderer.add("MyInput", {
  // 可编辑激活模板
  renderEdit(h, editRender, { row, column }) {
    return [
      <input
        class="my-cell"
        text="text"
        value={row[column.property]}
        onInput={evnt => {
          row[column.property] = evnt.target.value;
        }}
      />
    ];
  },
  // 可编辑显示模板
  renderCell(h, editRender, { row, column }) {
    return [<span>{row[column.property]}</span>];
  }
});

import { getFixedDemical } from "@/common/trade_erp/common.js";

// 金额渲染器 :cell-render="{name: 'MyLink', events: {click: linkEvent}}"
VXETable.renderer.add("FixedDemical", {
  // 默认显示模板
  renderDefault(h, cellRender, params) {
    let { row, column } = params;
    // let { events } = cellRender
    return [<h1>{getFixedDemical(column.property, row[column.property])}</h1>];
  }
});


// function editDisabledEvent(){
//   this.$message({
//     type: "info",
//     message: "现在处于查看单据状态，如需修改请点击修改按钮!"
//   });
// }