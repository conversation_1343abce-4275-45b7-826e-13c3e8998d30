<template>
  <div class="table-list-box">
      <!--按钮组-->
    <ButtonGroup
    ref="buttonGroup"
      :isEdit="isEdit"
      :djType="djType"
      :isShowHeadForm="isShowHeadForm"
      @initPage="init"
      @submit="save"
      @allowEdit="modify"
      @selectClick="select"
      @delOrder="delOrder"
      @getOrderUpDown="getUpDown"
      @getDetail="getDetail"
      @hiddenHeadForm="hiddenHeadForm"
    />
      <el-collapse-transition>
    <div class="table-headform" v-if="isShowHeadForm">
        <!--头部表单-->
      <headForm ref="headForm mt10" :isEdit="isEdit" />
    </div>
      </el-collapse-transition>
    <div class="table-box-mainButton">
        <!--表单至表格之间的操纵表格的按钮组-->
      <el-button type="success" @click="addRow" :disabled="!isEdit">
        新增行
      </el-button>
      <el-button
        type="danger"
        @click="deleteRow"
        :disabled="!isEdit"
      >
        删除行
      </el-button>
      <div class="congested" />

      <el-button type="primary" :disabled="!isEdit">
        +附件
      </el-button>
    </div>
    <div class="tableList">
        <!-- 表格组件 -->
      <MainTable ref="mainTable" :isEdit="isEdit" />
    </div>
    <div class="table-statistic">
        <!-- 表尾合计组件 -->
      <StatisticBar :statisticBar="statisticBar"/>
    </div>
    <div class="table-remake">
        <!-- 整单备注 -->
      <el-input
        type="textarea"
        placeholder="整单备注"
        resize="none"
        size="medium"
        v-model="remarks"
        :rows="1"
        :autosize="false"
      >
      </el-input>
    </div>
  </div>
</template>

<script src="./index.js">
</script>

