<template>
  <div class="pagination">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageNum"
      :page-sizes="layouts"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalCount"
    />
  </div>
</template>

<script>
/* 需要父组件传的内容 @success：查询的方法 */
export default {
  name: "Pagination",
  data() {
    return {
      pageNum: 1,
      pageSize: 20,
      totalCount: 0,
      layouts: [10, 20, 50, 100]
    };
  },
  methods: {
    /* 父组件调用 获取页数和页码大小 */
    ObtainPagination() {
      return {
        pageNum: this.pageNum,
        pageNO: this.pageNum,
        pageSize: this.pageSize
      };
    },

    /* 父组件调用   给子组件传入条数 */
    SetTotal(count = 0) {
      this.totalCount = count;
      /* 判断条数是否大于页面大小乘页码的数量，用来解决新的搜索条件时，页码不重置导致的当前页无数据，其他页有数据的问题 */
      if (
        this.pageNum > 1 &&
        this.totalCount < (this.pageNum - 1) * this.pageSize
      ) {
        this.resetPageNum();
      }
    },
    /* 设置每页大小 */
    SetPageSize(size = 20) {
      this.pageNum = 1;
      this.pageSize = size;
    },
    /*  子组件自身方法 */
    handleCurrentChange(val) {
      this.pageNum = val;

      this.$emit("success");
    },
    /* 页数据大小改变 */
    handleSizeChange(val) {
      this.pageSize = val;
      this.$emit("success");
    },
    /* 重置页码 */
    resetPageNum() {
      this.pageNum = 1;
      this.$emit("success");
    }
  }
};
</script>

<style scoped lang="scss">
.pagination {
  width: 100%;
  height: 40px;
  flex: 0 0 40px;
}
</style>
