import API from '@/api/internalSystem/financialManage/invoice'
import costAPI from "@/api/internalSystem/basicManage/costDetails/index.js"
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddInvoice from "./components/addInvoice/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import AuditDetail from '@/mixins/auditDetail.js'
import {
  mapGetters
} from "vuex";
import {
  getOptions,
} from "@/common/internalSystem/common.js"
export default {
  name: "invoice",
  mixins: [AuditDetail],
  data() {
    return {
      title: "费用报销单",
      loading: false,
      tableData: [],
      formSearch: {
        add_user_id: "",
        employee_name:"",
        cost_detail_no:"",
        approval_state: "",
        startTime: "",
        endTime: ""
      },
      tableList: [{
          name: "审核状态",
          value: "approval_name"
        },
        {
          name: "单据编号",
          value: "invoice_no"
        },
        {
          name: "单据日期",
          value: "invoice_date"
        },
        {
          name: "报销金额",
          value: "bear_money"
        },
        {
          name: "明细名称",
          value: "cost_detail_name"
        },
        {
          name: "承担员工",
          value: "employee_name"
        },
        {
          name: "操作员工",
          value: "add_user_name"
        },
        {
          name: "操作部门",
          value: "department_name"
        },
        // {
        //   name: "购销单位",
        //   value: "sales_unit_id_format"
        // },
        {
          name: "审核人",
          value: "fk_employee_name"
        }
      ],
      approvalStateList: [],
      employeeList: [],
      isAdd: false,
      costDetailOption:[],
      totalMoney:0
    };
  },

  mounted() {
    this.$store.dispatch('getEmployee').then(res => {
      this.employeeList = res;
    });
    this.getList();
    this.getDict()
  },
  methods: {
    getList(f = false) {
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      this.approvalStateList = getOptions('t_financial_cost_invoice', 'approval_state');
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
          if (f)
            param.pageNum = 1;
          // let isJurisdiction = this.buttonPermissions.length ? (this.existence(this.buttonPermissions, 'ALL_INVOICE_NEW')) : false
          // param.isJurisdiction = isJurisdiction ? 1 : 0;
          param.isJurisdiction = this.permissionToCheck('ALL_INVOICE_NEW') ? 1 : 0;
          API.query(param).then(res => {
            this.tableData = res.data;
            this.$refs.pagination.setTotal(res.totalCount);
          }).finally(() => {
            this.loading = false;
          });
          API.queryAll(param).then(res => {
              console.log(res);
              this.totalMoney = res.data[0].bear_money
          }).finally(() => {

          });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addInvoice.Show();
    },
    //打开修改费用报销单会话框
    modify(item) {
      let params = {
        financial_cost_invoice_id: item.financial_cost_invoice_id
      };
      API.getInfo(params)
        .then(data => {
          this.isAdd = true;
          this.$refs.addInvoice.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if(item.data_state ===2){
        return this.error('该记录是旧数据，请上旧系统继续操作')
      }
      if (item.approval_state == 1)
        return this.error("该记录已审核通过，不允许删除")
      let params = {
        financial_cost_invoice_id: item.financial_cost_invoice_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    getDict(){
      costAPI.query({pageNum: 1,pageSize: 200}).then(res=>{
        this.costDetailOption = res.data.map(item=>{
          return {label:item['cost_detail_name'],value:item['cost_detail_no']}
        })
      })
      
    }
  },

  components: {
    AddInvoice,
    Pagination,
    TableView,
    MyDate
  },
  computed: {
    ...mapGetters(["buttonPermissions","financial_cost_invoice_audit"])
  }
};