<template>
  <div class="dailogContain">
    <el-dialog
      title="密码修改"
      :visible.sync="dailogVisible"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="true"
      @close="Cancel"
      width="400px"
      v-dialogDrag
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item
          label="原密码"
          :label-width="formLabelWidth"
          prop="password"
        >
          <el-input
            v-model="ruleForm.password"
            autocomplete="off"
            placeholder="请输入原始密码"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item
          label="新密码"
          :label-width="formLabelWidth"
          prop="newPassword"
        >
          <el-input
            v-model="ruleForm.newPassword"
            autocomplete="off"
            placeholder="请输入新密码"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item
          label="确认密码"
          :label-width="formLabelWidth"
          prop="checkPassword"
        >
          <el-input
            v-model="ruleForm.checkPassword"
            autocomplete="off"
            placeholder="请输入确认密码"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item>
          <div class="flexRow btn-block">
            <el-button @click="Cancel">取消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm')">
              提交
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import md5 from "js-md5";
export default {
  data() {
    return {
      formLabelWidth: "80px",
      dailogVisible: false,
      ruleForm: {
        password: "",
        newPassword: "",
        checkPassword: ""
      },
      rules: {
        password: [
          { required: true, message: "请输入原始密码", trigger: "blur" },
          { min: 6, message: "请输入至少6个字符", trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          { min: 6, message: "请输入至少6个字符", trigger: "blur" }
        ],
        checkPassword: [
          { required: true, message: "请输入确认密码", trigger: "blur" },
          { min: 6, message: "请输入至少6个字符", trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    Show() {
      this.dailogVisible = true;
    },
    Cancel() {
      this.$refs.ruleForm.resetFields();
      this.dailogVisible = false;
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.ruleForm.checkPassword !== this.ruleForm.newPassword) {
            this.error("新密码输入不一致");
            return;
          }
          let params = {
            password: md5(this.ruleForm.password.trim()),
            newPassword: md5(this.ruleForm.newPassword.trim())
          };
          this.$emit("submitChangePwd", params);
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.btn-block{
  justify-content: center;
}
</style>
