import API from "@/api/internalSystem/customerManage/article";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import AddArticle from "./components/addArticle/index.vue";
import AddCopyPush from "./components/addCopyPush/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import { getOptions, dateFormat } from "@/common/internalSystem/common.js";
// import ArticleAudit from ""
import AuditDetail from "@/mixins/auditDetail.js";
import { mapGetters } from "vuex";
export default {
  name: "articleInfo",
  data() {
    return {
      title: "文案管理",
      loading: false,
      tableData: [],
      formSearch: {
        publish_man: "",
        copy_state: "",
        keywords: "",
        title: "",
        type: "",
        default_flag: "",
      },
      tableList: [
        {
          name: "状态",
          value: "copy_state_name",
          width: 120,
        },
        {
          name: "主题",
          value: "title",
        },
        {
          name: "二级关键词",
          value: "keywords",
        },
        {
          name: "是否默认推送",
          value: "default_flag_label",
        },
        {
          name: "发布人",
          value: "publish_man_name",
          width: 120,
        },
        {
          name: "类型",
          value: "type_name",
          width: 120,
        },
        {
          name: "访问量",
          value: "traffic",
          width: 100,
        },
        {
          name: "部门",
          value: "department_name",
          width: 120,
        },
        {
          name: "审核备注",
          value: "audit_remark",
          width: 200,
        },
        {
          name: "创建时间",
          value: "add_time",
          width: 120,
        },
      ],
      auditStateList: [],
      employeeList: [],
      isAdd: false,
      isAudit: false,
      typeList: [],
    };
  },
  mixins: [AuditDetail],
  created() {
    if (!["总经理"].includes(this.cookiesUserInfo.role_name)) {
      this.formSearch.publish_man = this.cookiesUserInfo.userId;
    }
  },
  mounted() {
    this.getList();
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
    this.typeList = getOptions("t_copy", "type");
  },
  methods: {
    getList(f = false) {
      this.auditStateList = getOptions("t_copy", "copy_state");
      this.isAdd = false;

      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          if (f) param.pageNum = 1;
          // let isJurisdiction = this.buttonPermissions.length ? (this.existence(this.buttonPermissions, 'ALL_ARTICLE_NEW')) : false
          // param.isJurisdiction = isJurisdiction ? 1 : 0;
          param.isJurisdiction = this.permissionToCheck("ALL_ARTICLE_NEW")
            ? 1
            : 0;

          API.query(param)
            .then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
            })
            .finally(() => {
              this.loading = false;
            });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addArticle.Show();
    },
    modify(item) {
      let params = {
        copy_id: item.copy_id,
      };
      API.getInfo(params)
        .then((data) => {
          this.isAdd = true;
          this.$refs.addArticle.Show(data.data);
        })
        .catch(() => {});
    },
    //发出
    send(item) {
      if (item.copy_state !== "0")
        return this.error("该单据已发出，不允许重复推送");
      let params = {
        copy_id: item.copy_id,
        copy_state: 1,
      };
      API.updateAudit(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    del(item) {
      if (item.copy_state == 2) return this.error("该单据已审核，不允许删除");
      let params = {
        copy_id: item.copy_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    //推送
    push(item) {
      if (item.copy_state !== 1) {
        return this.error("该单据未审核通过，无法推送");
      }

      this.isAdd = true;
      this.$refs.addCopyPush.add(item);
      // this.$confirm('此操作将推送该文案至公众号, 是否继续?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   let params = {
      //     copy_id: item.copy_id,
      //     customerIds:[2638]
      //   };

      //   API.copy_push(params)
      //     .then(() => {
      //       this.getList();
      //     })
      //     .catch(() => {});
      // }).catch(() => {})
    },
        // 修改默认推送标志
    updateDefault(item) {
      if (item.copy_state !== 1) {
        return this.error("该单据未审核通过，无法修改默认推送标志");
      }
      let params = {
        copy_id: item.copy_id,
        default_flag: item.default_flag === 1 ? 2 : 1,
      };
      API.updateDefault(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
  },

  components: {
    AddArticle,
    Pagination,
    TableView,
    AddCopyPush,
  },
  computed: {
    ...mapGetters(["buttonPermissions", "copy_audit","cookiesUserInfo"]),
  },
};
