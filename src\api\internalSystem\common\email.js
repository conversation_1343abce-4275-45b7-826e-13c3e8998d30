import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 发送邮件
  sendEmail: params => {
    return Axios.post(`${environment.internalSystemAPI}email/send`, params)
  },
  
  // 发送带附件的邮件
  sendEmailWithAttachments: params => {
    return Axios.post(`${environment.internalSystemAPI}email/sendWithAttachments`, params)
  },
  
  // 获取邮件发送日志
  getEmailLogs: params => {
    return Axios.post(`${environment.internalSystemAPI}email/getLogs`, params)
  }
}; 