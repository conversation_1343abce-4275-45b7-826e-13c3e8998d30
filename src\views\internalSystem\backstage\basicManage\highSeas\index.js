import API from '@/api/internalSystem/basicManage/highSeas/index.js'

export default {
	data() {
		return {
			rules: {
				days: [{
					required: true,
					message: '请输入过期天数',
					trigger: 'blur'
				}],
				userId: [{
					required: true,
					message: '请选择转入公海账户',
					trigger: 'change'
				}],
			},
			ruleForm: {
				highSeasId: null,
				days: '',
				userId: ''
			},
			employeeList: []
		}
	},
	methods: {
		submitForm(formName) {
			this.$refs[formName].validate((valid) => {
				if (valid) {
					this.save()
				} else {

					return false;
				}
			});
		},
		resetForm(formName) {
			this.$refs[formName].resetFields();
		},
		save() {
			if (!Number.isInteger(this.ruleForm.days)) {
				return this.error('过期天数必须是数字')
			}
			// if(Number(this.ruleForm.days) < 60){
			// 	return this.error('过期天数最好大于60天')
			// }
			API.save(this.ruleForm).then(res=>{
				if(res.code === 1){
					this.success('保存成功')
					this.getInfo()
				}
			})
		},
		getInfo() {
			API.getInfo().then(res => {
				if (res.data) {
					this.ruleForm = res.data
				}
			})
		}


	},
	components: {

	},
	mounted() {

		this.$store.dispatch("getEmployee").then((res) => {
			this.employeeList = res;
		});
		this.getInfo()
	}
}