import API from '@/api/internalSystem/basicManage/brand'
import departmentAPI from "@/api/internalSystem/basicInfo/department/departmentApi.js";
export default {
  data() {
    return {
      ruleForm: {
        roleName: "",
        departmentId: ""
      },
      rules: {
        roleName: [{
          required: true,
          message: "请输入角色名称",
          trigger: "blur"
        }],
        departmentId: [{
          required: true,
          message: "请选择所属部门",
          trigger: "change"
        }]
      },
      dialogVisible: false,
      title: "新增",
      departmentList: []
    };
  },
  methods: {
    Show(jobObject = {}) {
      this.dialogVisible = true;
      if (!jobObject.roleId) {
        this.title = "新增";
      } else {
        this.title = "修改";
        this.getJob(jobObject.roleId)
      }
      if (this.dialogVisible)
        this.getDepartmentList();
    },
    //获取单个岗位
    getJob(id) {
      API.getInfo({
          roleId: id
        })
        .then(data => {
          this.ruleForm = data.data;
          if (data.data.superior == 0) this.ruleForm.superiorName = "无上级岗位";
        })
        .catch(() => {})
        .finally(() => {});
    },
    //获取部门列表
    getDepartmentList() {
      departmentAPI.query({})
        .then(data => {
          this.departmentList = data.data;
        })
        .catch(() => {})
        .finally(() => {});
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.ruleForm.roleId) {
            API.update(this.ruleForm)
              .then(() => {
                this.closeDialog();
              })
              .catch(() => {})
              .finally(() => {});
          } else {
            API.add(this.ruleForm)
              .then(() => {
                this.closeDialog();
                this.$emit("getJobTree");
              })
              .catch(() => {})
              .finally(() => {});
          }
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    closeDialog() {
      this.resetForm("ruleForm");
      this.clearData();
      this.dialogVisible = false;
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        jobsName: "",
        superior: "",
        superiorName: "",
        jobsDescribe: ""
      };
      this.jobId = "";
      this.jobParentId = "";
    }
  }
};