<template>
  <div class="orderH100">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <!-- <el-button type="primary" @click="dialogCancel(false)"
      v-permit="'ADD_CUSTOMER_BRAND_INFO_NEW'"
      >新增</el-button> -->
      <!-- <el-button
        type="primary"
        @click="submitForm('ruleForm')"
        v-permit="'ADD_CUSTOMER_BRAND_INFO_NEW'"
        :loading="loading"
        >保 存
      </el-button> -->

      <!-- <el-button type="primary" @click="chooseCustomer"
      v-permit="'ADD_CUSTOMER_BRAND_INFO_NEW'"
      >调入客户</el-button> -->

      <!-- <el-button
        type="danger"
        @click="del"
      >
        删除
      </el-button> -->
    </div>

    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="mt10"
    >
      <el-row :gutter="20">
        <el-col :span="6" class="ellipsis" >
          <el-form-item label="客户名称" >
            <el-input
              v-model="ruleForm.customer_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

        <!-- <div>
          <el-button
            type="primary"
            @click="add"
            v-permit="'ADD_CUSTOMER_BRAND_INFO_NEW'"
            >新增产品</el-button
          >
        </div> -->
    <TableView
      ref="proTableCustomerBrand"
      :tableList="tableList"
      :tableData="tableData"
      :isDblclick="true"
      class="mt10 tableContent"
      :isDel="isDel"
      :proList="proList"
      :yearsFeeList="yearsFeeList"
      :ruleForm="ruleForm"
    >
    </TableView>

    <CustomerList
      ref="customerAllList"
      dialogTitle="所有客户列表"
      :isJudge="true"
      @getInfo="getCustomerInfo"
      :isContract="true"
    />
  </div>
</template>
<script src="./index.js"></script>
<style lang="scss" scoped>
.w100 {
  width: 100% !important;
}

@import "@/assets/css/element/font-color.scss";
</style>
