<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      append-to-body
      @close="closeDialog"
      width="660px"
      :close-on-click-modal="false"
      v-dialogDrag
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="模块名称" prop="module_name">
          <el-input v-model="ruleForm.module_name" clearable></el-input>
        </el-form-item>

        <!-- <el-form-item label="菜单排序" prop="orderNum">
          <el-tooltip class="item" effect="dark" content="数值越小，排序越靠前" placement="right">
            <el-input  v-model.number="ruleForm.orderNum" clearable></el-input>
          </el-tooltip>
        </el-form-item> -->
        <el-form-item label="上级模块" prop="parent_name">
          <el-input
            v-model="ruleForm.parent_name"
            autocomplete="off"
            disabled
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('ruleForm')"
          >保 存</el-button
        >
        <el-button @click="closeDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script src="./index.js"></script>