
import menuAPI from '@/api/internalSystem/basicInfo/menu/menuApi'
import roleAPI from '@/api/internalSystem/basicInfo/role/roleApi'
import jobTree from "./components/jobTree/index.vue";
import menuTree from "./components/menuTree/index.vue";
export default {
  data() {
    return {
      djType: "GWXX",
      title: "岗位信息管理",
      search: "",
      proNumb: "",
      proName: "",
      ruleForm: [],
      selectRecords: [],
      loading: false,
      isCheck: true,
      tableData: [],
      jobIdSel:"",
      customColumns: [
        //隐藏指定列，需要在vxe-table添加:customs.sync="customColumns"
        {
          field: "proId",
          visible: false
        }
      ]
    };
  },
  methods: {
    //获取权限
    getPermissions() {
      menuAPI.queryAllRoleList({ roleId: this.jobIdSel })
        .then(data => {
          this.$refs.menuTree.SetCheckedKeys(data.data);
        })
        .catch(() => {})
        .finally(() => {});
    },
    //点击岗位触发
    getTreeData(params) {
      this.jobIdSel = params.roleId;
      this.getPermissions();
    },
    //保存权限
    addJurisdiction() {
      if(!this.jobIdSel){
        return this.$message("请先选择岗位")
      }
     let menuIds= this.$refs.menuTree.GetCheckedKeys();
     this.loading=true;
     roleAPI.jurisdiction({
        roleId: this.jobIdSel,
        menuArray: menuIds
      })
        .then(() => {
          this.getPermissions();
        })
        .catch(() => {})
        .finally(() => {
          this.loading=false;
        });
    }
  },
  components: {
    menuTree,
    jobTree
  }
};
