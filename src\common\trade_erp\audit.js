import store from "@/store/trade_erp/index.js";
import commonApi from "@/api/trade_erp/commonApi";
/* 系统设置参数流程制单保存后已知并且生效 */
async function auditState(proccessId, djState, orderId, djType, userId) {
	return new Promise((resolve, reject) => {
		/* 无流程 保存就生效  djState === 2  不显示按钮*/
		if (proccessId === -1) return resolve(NoProcessAudit(djState, orderId))

		/* 有流程 */
		/* 保存提交显示提交按钮 */
		let [showAuditBtn, auditBtnName, taskId, shrName] = [false, "提交", "", ""]
		let auditImg = "https://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/P3Y2yDE5_2658_system_%E5%88%B6%E5%8D%95%E4%B8%AD.png"
		let userInfo = store.state.environment.userInfo
		let loginUserId = userInfo.userId

		let retrunData = {
			showAuditBtn,
			auditBtnName,
			auditImg,
			taskId,
			shrName
		}

		/* 单据未保存生成  初始化 */
		if (!orderId) return resolve(retrunData)

		/* 单据保存成功  除制单人可提交审核*/
		if (djState === 0 && userId === loginUserId) {
			/* 审核按钮显示 */
			retrunData.showAuditBtn = true
			resolve(retrunData)
			return
		}

		let params = {
			djId:orderId,
			djType
		}
		commonApi.getTaskIdAndReceiveList(params).then(res => {
			/* 单据审核完成 除制单人/最后审批人可见审核按钮 */
			if (djState === 2) {

				/* 审核按钮显示 */
				retrunData.showAuditBtn = true

				/* 审核图标显示完成 */
				retrunData.auditImg = "http://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/SZNp4YWa_2741_system_%E5%B7%B2%E5%AE%8C%E6%88%90.png"

				/* 审核图标显示完成 */
				retrunData.auditBtnName = "回退"

				/* 最终审核人不是登陆人 需要给提示信息没有回退权限 请联系最终审批人XXX */
				if (res.lastAuditId !== loginUserId) retrunData.shrName = res.lastAuditName

				/* 最终审核人是登陆人*/
				if (userId === loginUserId || res.lastAuditId === loginUserId) retrunData.taskId = res.proccessFinallTaskId;

				resolve(retrunData)
				return
			}


			/* 单据审核中 多人审核 */
			if (djState === 1) {

				/* 审核图标显示完成 */
				retrunData.auditImg = "http://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/SeNTzQYQ_2678_system_%E5%AE%A1%E6%89%B9%E4%B8%AD.png"


				/* 当前登陆人为历史审批人 */
				if (res.historyAuditUserFlag || userId === loginUserId) {

					/* 审核按钮显示 */
					retrunData.showAuditBtn = true

					/* 审核按钮显示审核 */
					retrunData.auditBtnName = "审核"

					if (res.nowAuditUserFlag) {

						/* 当前审批人就是登陆人 需要显示可以流转的界面 */
						retrunData.taskId = res.nowProcessTaskId;

					} else {

						/* 当前审批人只是流程历史审批人 需要提示流程已流转给XXX审批 */
						retrunData.shrName = res.nowAuditUserNames;

					}
				}

				if (userId === loginUserId) retrunData.shrName = res.nowAuditUserNames /* 当前登陆人为制单人 */


				resolve(retrunData)
				return
			}
			
			resolve(retrunData)
		}).catch(e => {
			reject(e)
		})
	})


}

/**
 * 无流程状态单据
 * @param {*} djState 无流程为2或0
 */
function NoProcessAudit(djState, orderId) {
	let [showAuditBtn,
		auditImg
	] = [false, "https://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/P3Y2yDE5_2658_system_%E5%88%B6%E5%8D%95%E4%B8%AD.png"]
	/* 按钮不显示 保存直接通过审核 djState ===2 */
	showAuditBtn = false
	if (orderId && djState === 2)
		auditImg = "http://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/SZNp4YWa_2741_system_%E5%B7%B2%E5%AE%8C%E6%88%90.png"

	return {
		showAuditBtn,
		auditImg
	}
}


export default auditState