import API from '@/api/internalSystem/basicInfo/employee/employeeApi.js'

const state = {
	employeeData:[]
}
const getters = {
	employeeData: state => state.employeeData
}
const mutations = {
	GET_EMPLOYEE: (state, employeeData) => {
		state.employeeData = employeeData
	}
}
const actions = {
	getEmployee: (store) => {
		return new Promise ((resolve,reject)=>{
			if(store.state.employeeData.length){
				resolve(store.state.employeeData)
			}else{
				API.query({"in_position":"1"}).then(res => {
					let data = res.data ? res.data : []
					//store.state['employeeData'] = res.data || [];
					store.commit('GET_EMPLOYEE',data)
					resolve(data)
				}).catch((err) => {
					reject(err)
				});
			}
		})
	}
}
export default {
	state,
	getters,
	mutations,
	actions
}