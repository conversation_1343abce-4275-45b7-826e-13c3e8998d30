import API from '@/api/internalSystem/salesManage/quotation'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
export default {
  name: "quotationList",
  components: {
    TableView,
    Pagination,
    MyDate
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customer_name: ""
      },
      tableList: [{
          name: "编号",
          value: "quotation_no",
          width: 112
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "培训方式",
          value: "train_type_format",
          width: 70
        },
        {
          name: "付款方式",
          value: "pay_type_format",
          width: 106
        },
        {
          name: "销售类型",
          value: "sell_type_format",
          width: 82
        },
        {
          name: "单据备注",
          value: "remark",
          width: 82
        },
        {
          name: "操作员工",
          value: "add_user_name",
          width: 70
        },
        {
          name: "操作部门",
          value: "add_user_department_name",
          width: 70
        },
        {
          name: "销货单位",
          value: "sales_unit_id_format"
        },
        {
          name: "联系人",
          value: "link_man",
          width: 60
        },
        {
          name: "介绍人",
          value: "introducer_name"
        },
        {
          name: "介绍合同",
          value: "introducer_contract_id_format"
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 60
        },
        {
          name: "销售部门",
          value: "fk_sell_department_name",
          width: 70
        },
        {
          name: "手机",
          value: "phone",
          width: 100
        },
        {
          name: "客户传真",
          value: "fax",
          width: 82
        },
        {
          name: "联系人QQ号",
          value: "link_qq",
          width: 90
        },
        {
          name: "软件序列号",
          value: "software_no",
          width: 90
        }
      ],
      sellTypeList: [],
      employeeList: []
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "报价单列表"
    },
    customerId: {
      type: Number,
      default: 0
    }
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.getList();
        });
      // }, 300);
    },
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.quo_pagination.obtain());
      if (f)
        param.pageNum = 1;
      param.state = 0;
      param.isJurisdiction = 0;
      param.audit_state = 1;
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.quo_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    //提交
    submitForm() {
      if (this.selectRecords.length != 1)
        return this.error("请选择一条报价单");
      if (this.customerId && this.customerId != this.selectRecords[0].fk_customer_id)
        return this.error("请选择同一客户的报价单");
      this.$emit("getInfo", this.selectRecords[0]);
      this.dialogCancel();
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.selectRecords = [];
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
  }
};