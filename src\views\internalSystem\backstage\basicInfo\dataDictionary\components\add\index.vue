<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="660px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
      <el-form-item label="表名" prop="tbName">
        <el-input v-model="ruleForm.tbName" clearable></el-input>
      </el-form-item>
      <el-form-item label="表字段" prop="tbCode">
        <el-input v-model="ruleForm.tbCode" clearable></el-input>
      </el-form-item>
      <el-form-item label="字段名称" prop="sysName">
        <el-input v-model="ruleForm.sysName" clearable></el-input>
      </el-form-item>
      <el-form-item label="字段值" prop="sysValue">
        <el-input v-model="ruleForm.sysValue" clearable></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input v-model="ruleForm.sort"  clearable></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="ruleForm.remark" clearable></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>