import API from '@/api/internalSystem/customerManage/customerTracking'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import validationRules from "@/common/internalSystem/validationRules.js"
import BrandList from "@/views/internalSystem/backstage/components/brandList/index.vue";
import {
  getOptions
} from "@/common/internalSystem/common.js"
export default {
  name: "addTracking",
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_id: "",
        customer_track_stage: "",
        customer_name: "",
        link_man: "",
        phone: "",
        use_software: "",
        customer_tracking_content: "",
        customer_points: "",
        interest_product: ""
      },
      rules: {
        link_man: [{
          required: true,
          message: "请输入联系人",
          trigger: "blur"
        }],
        phone: [{
          required: true,
          validator: validationRules.checkPhone,
          trigger: "blur"
        }],
        use_software: [{
          required: true,
          message: "请输入使用软件",
          trigger: "blur"
        }],
        customer_tracking_content: [{
          required: true,
          message: "请输入跟踪内容",
          trigger: "blur"
        }],
        customer_points: [{
          required: true,
          message: "请输入客户痛点",
          trigger: "blur"
        }]
      },
      tableData: [],
      customerStageList: []
    };
  },
  components: {
    TableView,
    BrandList
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      if (!data.customer_tracking_id) {
        this.ruleForm.customer_id = data.customer_id;
        this.ruleForm.customer_name = data.customer_name;
        this.ruleForm.customer_track_stage = data.customer_stage;
        this.ruleForm.link_man = data.link_man;
      } else {
        this.ruleForm = data;
        let list = data.interest_product.split(",");
        list.forEach(item => {
          this.tableData.push({
            brand_type: item
          })
        });
      }
      this.customerStageList = getOptions('t_customer', 'customer_stage');
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.tableData=[];
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      let interest_product = [];
      this.tableData.forEach(item => {
        interest_product.push(item.brand_type);
      });
      params.interest_product = interest_product.join(",");
      if (params.customer_tracking_id) {
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      }
    },
    addBrand() {
      this.$refs.brandList.Show();
    },
    getInfo(info) {
      this.tableData = this.tableData.concat(info)
      let obj = {};
      //原理，判断obj中某个key是否存在，不存在就push进新数组，存在就不处理，
      //reduce第二个参数是当前item
      this.tableData = this.tableData.reduce((arr, item) => {
        obj[item.brand_type] ? "" : obj[item.brand_type] = true && arr.push(item);
        return arr;
      }, [])
    },
    del(info) {
      let delIndex = ""
      this.tableData.map((item, index) => {
        if (item.brand_type === info.brand_type) {
          delIndex = index
        }
      })
      this.tableData.splice(delIndex, 1)
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        tbName: "",
        tbCode: "",
        sysName: "",
        sysValue: "",
        sort: "",
        remark: ""
      }
    }
  }
};