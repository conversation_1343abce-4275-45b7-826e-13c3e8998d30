<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      append-to-body
      @close="closeDialog"
      width="660px"
      :close-on-click-modal="false"
      v-dialogDrag
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="目录名称" prop="directory_name">
          <el-input v-model="ruleForm.directory_name" clearable></el-input>
        </el-form-item>

        <el-form-item label="上级目录" prop="parent_name">
          <el-input
            v-model="ruleForm.parent_name"
            autocomplete="off"
            disabled
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('ruleForm')"
          >保 存</el-button
        >
        <el-button @click="closeDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script src="./index.js"></script>