import API from "@/api/internalSystem/videoManage/trainingVideo/index.js";
import environment from "@/api/environment";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import { mapGetters } from "vuex";
import ossApi from "@/api/aliyun/oss.js";
export default {
  name: "customerList",
  components: {
    TableView,
    Pagination,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableData: [],
      fileList: [],
      environment: environment,
      files: {},
 
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "附件列表",
    },
    fileType: {
      type: String,
    },
    fileId: {
      type: Number,
    },
    directory_id: {
      type: Number,
    },
  },
  methods: {
    ossFile(file, index) {
              const loading = this.$loading({
                lock: true,
                text: "正在上传...",
                background: "rgba(0, 0, 0, 0.7)",
                spinner: 'el-icon-loading',
        });
      ossApi
        .uploadFile(file.file)
        .then((res) => {
          let fileObj = {};
          fileObj.file_name = file.file.name;
          fileObj.file_url = res.url;
          fileObj.file_size = file.file.size;
          fileObj.file_suffix = file.file.name.substr(
            file.file.name.lastIndexOf(".") + 1
          );
          this.saveFile(fileObj,loading)

        })
        .catch(() => {});
    },

    // checkFile(file) {
    //   return true;
    // },

    saveFile(fileObj,loading) {
      if (!fileObj.file_url) {
        this.error("请先上传附件");
        return 
      }
      let list = []
      list.push(fileObj)

      API.add({
        list: list,
        file_type: 1,
        fk_directory_id: this.directory_id,
      }).then(res=>{
        this.$emit("getList",this.directory_id)
        loading.close();
      }).catch(()=>{
        loading.close();
      })
    },
    // handlePreview(res) {
    //   if (!res || res.code !== 1 || !res.data) {
    //     this.error(`${res.msg}`);
    //     this.recovery();
    //     return;
    //   }
    //   this.files = res.data;
    //   this.upLoadSuccess();
    // },
    // recovery() {
    //   this.$nextTick(() => {
    //     this.files = {};
    //   });
    // },
    // upLoadSuccess() {
    //   let url = this.fileType + "Add";
    //   let params = {
    //     file_url: this.files.file_url,
    //     file_name: this.files.raw_name,
    //     file_size: this.files.file_size,
    //     file_suffix: this.files.file_suffix,
    //     id: this.fileId,
    //   };
    //   API[url](params)
    //     .then(() => {
    //       this.recovery();
    //       this.getList();
    //     })
    //     .catch(() => {});
    // },
    //删除
    del(item) {
      let url = this.fileType + "Del";
      API[url]({
        anexo_id: item.anexo_id,
      })
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    //下载
    downloadFile(item) {

        window.location.href =
        item.file_url + "?response-content-type=application/octet-stream";
   
    },
    dialogCancel() {
      this.fileList = [];

      this.dialogVisible = false;
    },
  },
  computed: {
    ...mapGetters(["buttonPermissions"]),
  },
};
