<template>
  <div style="overflow-y: auto; overflow-x: hidden">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button
        type="primary"
        @click="submitForm('ruleForm')"
        :loading="loading"
        >保 存</el-button
      >
    </div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="90px"
      class="mt10"
      style="margin-left: 20px; margin-right: 30px"
      label-position="left"
    >
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="客户阶段" prop="customer_stage">
            <el-select
              v-model="ruleForm.customer_stage"
              placeholder="请选择客户阶段"
              disabled
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in params_constant_customer_stage"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售员">
            <el-input
              v-model="fkSaleEmployeeUserInfo"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="销售员">
            <el-input
              v-model="fkSaleEmployeeUserInfo"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row :gutter="30">
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销售员工号" prop="fk_sale_employee_number">
            <el-input v-model="ruleForm.fk_sale_employee_number" disabled clearable></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input v-model="ruleForm.customer_name" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="法定名称">
            <el-input
              v-model="ruleForm.customer_legal_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户法人" prop="customer_legal_person">
            <el-input
              v-model="ruleForm.customer_legal_person"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8" class="formItem2">
          <el-form-item label="联系人" prop="link_man">
            <el-input v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="客户别名" prop="customer_name_alias">
            <el-input
              v-model="ruleForm.customer_name_alias"
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="ruleForm.email" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户简介" prop="customer_synopsis">
            <el-input v-model="ruleForm.customer_synopsis" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8" class="formItem2">
          <el-form-item label="手机号" prop="telephone">
            <el-input v-model="ruleForm.telephone" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="QQ号码" prop="qq">
            <el-input v-model="ruleForm.qq" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户来源" prop="customer_source">
            <el-select
              style="width: 100%"
              v-model="ruleForm.customer_source"
              placeholder="请选择客户来源"
              filterable
              clearable
            >
              <el-option
                v-for="item in params_constant_customer_source"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8" class="formItem2">
          <el-form-item label="详细地址" prop="link_address">
            <el-input v-model="ruleForm.link_address" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="备案日期">
            <el-date-picker
              style="width: 100%"
              v-model="update_time"
              align="right"
              type="date"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="介绍人/公司" prop="introducerName">
            <el-input
              v-model="ruleForm.introducerName"
              placeholder="点击选择介绍人/公司"
              @focus="chooseIntroducer"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="ruleForm.phone" clearable></el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销售员姓名" prop="fk_sale_employee_name">
            <el-input v-model="ruleForm.fk_sale_employee_name" disabled clearable></el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销售员部门" prop="fk_sale_employee_depot">
            <el-input v-model="ruleForm.fk_sale_employee_depot" disabled clearable></el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="公司网站" prop="company_site">
            <el-input v-model="ruleForm.company_site" clearable></el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="人员规模">
            <el-input  v-model="ruleForm.personnel_scale_low" style="width:46%;"
              @input="(e)=>{ruleForm.personnel_scale_low=e.replace(/[^\d]/g,'')}" clearable></el-input>
            <el-button type="text" class="toLabel">-</el-button>
            <el-input  v-model="ruleForm.personnel_scale_high" style="width:46%;"
              @change="(e)=>{ruleForm.personnel_scale_high=e.replace(/[^\d]/g,'')>ruleForm.personnel_scale_low?e.replace(/[^\d]/g,''):ruleForm.personnel_scale_low}"
              clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="客户类型" prop="customer_type">
            <el-select v-model="ruleForm.customer_type" placeholder="请选择客户类型" filterable clearable>
              <el-option v-for="item in params_constant_customer_type" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="传真" prop="fax">
            <el-input v-model="ruleForm.fax" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="企业规模">
            <el-input  v-model="ruleForm.company_scale_low" style="width:46%;"
              @input="(e)=>{ruleForm.company_scale_low=e.replace(/[^\d]/g,'')}" clearable></el-input>
            <el-button type="text" class="toLabel">-</el-button>
            <el-input  v-model="ruleForm.company_scale_high" style="width:46%;"
              @change="(e)=>{ruleForm.company_scale_high=e.replace(/[^\d]/g,'')>ruleForm.company_scale_low?e.replace(/[^\d]/g,''):ruleForm.company_scale_low}"
              clearable></el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所属行业" prop="belong_industry">
            <el-select v-model="ruleForm.belong_industry" placeholder="请选择所属行业" filterable clearable>
              <el-option v-for="item in params_constant_belong_industry" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="邮政编码" prop="postal_code">
            <el-input  v-model="ruleForm.postal_code" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所在省份" prop="province">
            <el-select v-model="ruleForm.province" placeholder="请选择省份" @change="changeProvince" filterable clearable>
              <el-option v-for="item in provinceList" :key="item.province" :label="item.origin_place"
                :value="item.province">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="重要等级" prop="important_rank">
            <el-select v-model="ruleForm.important_rank" placeholder="请选择重要等级" filterable clearable>
              <el-option v-for="item in params_constant_important_rank" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所在城市" prop="city">
            <el-select v-model="ruleForm.city" placeholder="选择省份获取可选城市" filterable clearable>
              <el-option v-for="item in cityList" :key="item.city" :label="item.origin_place" :value="item.city">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-checkbox v-model="checked">是否新增客户财务资料</el-checkbox>
    </el-form>
    <el-form
      :model="financeForm"
      :rules="financeRules"
      ref="financeForm"
      label-width="90px"
      class="mt10"
      style="margin-left: 20px; margin-right: 30px"
      label-position="left"
    >
      <div v-if="checked">
        <div class="add_customer_finance">客户财务资料</div>
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="客户税号" prop="customer_tax_number">
              <el-input
                v-model="financeForm.customer_tax_number"
                clearable
                onkeyup="this.value=this.value.replace(/[, ]/g,'')" 
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户行" prop="opening_bank">
              <el-input v-model="financeForm.opening_bank" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收票人" prop="receive_ticket_person">
              <el-input
                v-model="financeForm.receive_ticket_person"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="开票地址" prop="open_ticket_address">
              <el-input
                v-model="financeForm.open_ticket_address"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" class="formItem2">
            <el-form-item label="银行账号" prop="customer_account">
              <el-input
                v-model="financeForm.customer_account"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="收票电话" prop="receive_ticket_phone">
              <el-input
                v-model="financeForm.receive_ticket_phone"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="开票电话" prop="open_ticket_phone">
              <el-input
                v-model="financeForm.open_ticket_phone"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" class="formItem2">
            <el-form-item label="收票地址" prop="receive_ticket_address">
              <el-input
                v-model="financeForm.receive_ticket_address"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <customer-list
      ref="customerList"
      dialogTitle="成交客户列表"
      :customerStage="3"
      @getInfo="getInfo"
    />
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
.toLabel {
  width: 8%;
  color: black;
  cursor: default;
}

.add_customer_finance {
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
  background: #70676733;
}

@import "@/assets/css/element/font-color.scss";
</style>