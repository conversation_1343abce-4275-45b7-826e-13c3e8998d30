import Router from "vue-router";
import login from "./login";
import print from "./print";
import backstage from "./backstage";
import outside from "./outside.js";
import _ from "lodash";
import store from "@/store/internalSystem/index.js";
import { Message } from "element-ui";

const routes = _.concat(login, print, outside, backstage);
const router = new Router({
  base: process.env.BASE_URL,
  routes,
});

// 路由白名单
const whiteRoute = [
  "/backstage/statistics/statisticsPage",
  "/",
  "/login",
  "/backstage/customerManage/auditCenter",
  "/backstage/salesManage/contractAdd",
  "/print",
  "/backstage/bugManage/bugContent/addBug",
  "/backstage/bugManage/bugContent/bugList",
  "/backstage/bugManage/bugContent/bugDetail",
];

router.beforeEach((to, from, next) => {

  if (to.path.includes("/outside")) {

    // 成功到目标路由，设置目标页面权限
    store.dispatch("setCurRoutePermissions", to.path);
    next();
    return;
  }

  if (!whiteRoute.includes(to.path)) {

    // 不在白名单内

    if (
      !store.getters.flatPermissions.some(
        (item) => item.type === 1 && item.menuUrl === to.path
      )
    ) {
      // 不在路由权限内
      return Message.error("没有页面权限，请联系管理员开启");
    }
  }

  // 成功到目标路由，设置目标页面权限
  store.dispatch("setCurRoutePermissions", to.path);
  next();
});

export default router;
