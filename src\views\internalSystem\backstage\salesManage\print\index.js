import API from "@/api/internalSystem/salesManage/contract";
import menuAPI from "@/api/internalSystem/basicInfo/menu/menuApi";
import { mapActions, mapGetters } from "vuex";
import { getHtmlStr} from "@/165.js"
export default {
  data() {
    return {
      detail: "",
      contract_template_id: "",
      customer_contract_id: "",
      type: "",
      contract: {},
      salesUnit: {
        sealImg: ''
      },
      contractDetail: {},
      templateName: "",
      isUpdate: false,
      isAdd: false,
      detailNum: 0,
      // imgUrl: "https://trade-erp.oss-cn-beijing.aliyuncs.com/2020_03_27/WechatIMG90.png"
      imgUrl:
        "https://trade-erp.oss-cn-beijing.aliyuncs.com/2021_09_01/E4SEmQKH_239116_system_%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20210901190509.png",
    };
  },
  mounted() {
    this.GET_USERINFO();
    this.contract_template_id = this.$route.query.contract_template_id;
    this.type = this.$route.query.type;
    this.customer_contract_id = this.$route.query.customer_contract_id;
    this.Show();
  },
  methods: {
    changText(event) {
      this.detail = this.detail.replace(
        event.target.defaultValue,
        event.target.value
      );
    },
    ...mapActions(["GET_USERINFO"]),
    judge() {
      let params = {
        roleId: this.userInfo.roleId,
        menu_describe: "ADD_CONTRACT_TEM_NEW",
      };
      menuAPI
        .getInfoRole(params)
        .then((res) => {
          this.isAdd = res.data.length ? true : false;
        })
        .catch(() => {})
        .finally(() => {});
      let params1 = {
        roleId: this.userInfo.roleId,
        menu_describe: "UPDATE_CONTRACT_TEM_NEW",
      };
      menuAPI
        .getInfoRole(params1)
        .then((res) => {
          this.isUpdate = res.data.length ? true : false;
        })
        .catch(() => {})
        .finally(() => {});
    },
    Show() {
      let params = {
        contract_template_id: this.contract_template_id,
        customer_contract_id: this.customer_contract_id,
        type: this.type,
      };
      console.log(getHtmlStr());
      API.getTemplate(params)
        .then((res) => {
          this.templateName = res.data.contract_template_name;
          this.detail = res.data.contract_template_html;
          // this.detail = getHtmlStr();
          this.contract = res.data.contract;
          this.salesUnit = res.data.salesUnit;
          this.contractDetail = res.data.contractDetail;
          this.detailNum = res.data.detailNum;
          if (this.type !== "update") {
            this.detail = this.detail
              .replace(new RegExp("{{", "g"), "")
              .replace(new RegExp("}}", "g"), "");
            for (let k in this.contract) {
              if (this.contract[k])
                this.detail = this.detail.replace(
                  new RegExp("contract." + k, "g"),
                  this.contract[k]
                );
              else
                this.detail = this.detail.replace(
                  new RegExp("contract." + k, "g"),
                  ""
                );
            }
            for (let k in this.salesUnit) {
              if (this.salesUnit[k])
                this.detail = this.detail.replace(
                  new RegExp("salesUnit." + k, "g"),
                  this.salesUnit[k]
                );
              else
                this.detail = this.detail.replace(
                  new RegExp("contract." + k, "g"),
                  ""
                );
            }
            for (let k in this.contractDetail) {
              if (this.contractDetail[k])
                this.detail = this.detail.replace(
                  new RegExp("contractDetail." + k, "g"),
                  this.contractDetail[k]
                );
              else
                this.detail = this.detail.replace(
                  new RegExp("contract." + k, "g"),
                  ""
                );
            }
          } else {
            this.judge();
          }

          // setTimeout(() => {
            //修改数据
            //DOM还没更新
            this.$nextTick(() => {
              //DOM现在更新了
              let textareas = document.querySelectorAll(".textareas");
              if (textareas.length > 0) {
                for (var i = 0; i < textareas.length; i++) {
                  this.adjustObjHeight(textareas.item(i));
                }
              }
            });
          // }, 300);
        })
        .catch(() => {})
        .finally(() => {});
    },
    adjustObjHeight(e) {
      // e.style.height = 'auto';
      e.style.height = e.scrollHeight + "px";
    },
    makeExpandingArea(e) {
      if (e.target.type === "textarea") {
        e.target.style.height = "auto";
        e.target.style.height = e.target.scrollHeight + "px";
      }
    },
    print() {
      // let div = document.createElement("div")
      // div.id = "printDiv"
      // if (!document.querySelector("#printDiv")) {
      //   document.body.appendChild(div)
      // }
      // div.innerHTML = document.getElementById("tt").innerHTML
      // div.classList.add('www')
      // window.print();
      this.$print(this.$refs.printDom);
    },
    add() {
      let listEle = document.querySelectorAll("textarea");
      if (listEle.length > 0) {
        for (let i = 0; i < listEle.length; i++) {
          let item = listEle.item(i);
          item.defaultValue = item.value;
        }
      }
      let htmlStr = document.querySelector("#tes").innerHTML;
      this.$confirm("是否新增合同模板?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            contract_template_name: this.templateName,
            contract_template_html: htmlStr,
          };
          API.addTemplate(params)
            .then(() => {})
            .catch(() => {})
            .finally(() => {});
        })
        .catch(() => {});
    },
    update() {
      let listEle = document.querySelectorAll("textarea");
      if (listEle.length > 0) {
        for (let i = 0; i < listEle.length; i++) {
          let item = listEle.item(i);
          item.defaultValue = item.value;
        }
      }
      let htmlStr = document.querySelector("#tes").innerHTML;

      this.$confirm("是否保存合同模板的修改?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            contract_template_id: this.contract_template_id,
            contract_template_name: this.templateName,
            contract_template_html: htmlStr,
          };
          API.updateTemplate(params)
            .then(() => {})
            .catch(() => {})
            .finally(() => {});
        })
        .catch(() => {});
    },
    close() {
      window.open("about:blank", "_self").close();
    },
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
};
