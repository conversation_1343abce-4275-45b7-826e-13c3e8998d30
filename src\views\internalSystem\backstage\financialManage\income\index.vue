<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small" v-if="!isAudit">
      <el-form-item label="查询条件">
        <my-date v-model="formSearch.startTime" hint="请选择开始时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.endTime" hint="请选择结束时间"></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button
          type="primary"
          @click="add"
          v-permit="'ADD_INCOME_NEW'"
        >添加</el-button>
      </el-form-item>
    </el-form>
    <table-view
      v-if="!isAudit"
      :tableList="tableList"
      :tableData="tableData"
      :isEdit="'UPDATE_INCOME_NEW'"
      :isDel="'DEL_INCOME_NEW'"
      @modify="modify"
      @del="del"
      isThrid="AUDIT_INCOME_NEW"
      :thridTitle="'审核'"
      @thrid="item => toAuditDet(item, '收入单审核','audit_state')"
    ></table-view>
    <Pagination ref="pagination" @success="getList" v-show="!isAudit" />
    <!-- 新增修改其他收入单信息 -->
    <add-income ref="addIncome" @selectData="getList" />
    <!-- 审核 -->
    <IncomeAudit ref="incomeAudit" v-show="isAudit" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>