import API from "@/api/internalSystem/basicInfo/menu/menuApi.js";
import {mapGetters} from 'vuex'
export default {
  data() {
    return {
      dialogVisible: false,
      title: "新增",
      ruleForm: {
        menuName: "",
        iconUrl: "",
        menuUrl: "",
        menuDescribe: "",
        level: "",
        orderNum: "",
        parentId: "",
        parentName: ""
      },
      rules: {
        menuName: [{
          required: true,
          message: "请输入菜单名称",
          trigger: "blur"
        }],
        menuUrl: [{
          required: true,
          message: "请输入菜单路径",
          trigger: "blur"
        }],
        orderNum: [{
          type: "number",
          required: true,
          message: "请输入菜单排序",
          trigger: "blur"
        }],
        menuDescribe: [{
          required: true,
          message: "请输入菜单编码",
          trigger: "blur"
        }],
        parentId: [{
          required: true,
          message: "请选择上级菜单",
          trigger: "blur"
        }],
        parentName: [{
          required: true,
          message: "请选择上级菜单",
          trigger: "blur"
        }],
        type: [{
          required: true,
          message: "请选择菜单类型",
          trigger: "blur"
        }]
      }
    };
  },
  computed: {
    ...mapGetters(['params_constant_menu_type'])
  },
  methods: {
    //获取单个菜单
    getMenu(id) {
      API.getInfo({
          menuId: id
        })
        .then(data => {
          this.ruleForm = data.data;
          if (data.data.parentId == 0) this.ruleForm.parentName = "无上级菜单";
        })
        .catch(() => {})
        .finally(() => {});
    },
    Show(menuObject = {
      parentId: "",
      parentName: ""
    }) {
      //弹窗显示
      this.dialogVisible = true;
      this.ruleForm.parentId = menuObject.parentId;
      this.ruleForm.parentName = menuObject.parentName;
      this.ruleForm.level = menuObject.level;
      if (!menuObject.menuId) {
        this.title = "新增";
      } else {
        this.title = "修改";
        this.getMenu(menuObject.menuId);
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.ruleForm.menuId) {
            API.update(this.ruleForm)
              .then(() => {
                this.closeDialog();
              })
              .catch(() => {})
              .finally(() => {});
          } else {
            API.add(this.ruleForm)
              .then(() => {
                this.closeDialog();
              })
              .catch(() => {})
              .finally(() => {});
          }
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    closeDialog() {
      this.dialogVisible = false
      this.resetForm("ruleForm");
      this.clearData();
      this.$emit("getMenuTree");
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        menuName: "",
        iconUrl: "",
        menuUrl: "",
        menuDescribe: "",
        level: "",
        orderNum: "",
        parentId: "",
        parentName: ""
      };
    }
  }
};