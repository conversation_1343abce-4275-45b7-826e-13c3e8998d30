# FileList 组件使用说明

## 功能介绍
FileList 组件提供附件列表显示、上传、预览、下载和邮件发送功能。

## 使用方法

```vue
<template>
  <div>
    <el-button @click="showFileList">查看附件</el-button>
    
    <file-list 
      ref="fileList"
      :file-type="'contract'"
      :file-id="contractId"
      :customer-info="customerInfo"
      dialog-title="合同附件"
    />
  </div>
</template>

<script>
import FileList from '@/views/internalSystem/backstage/components/fileList/index.vue'

export default {
  components: {
    FileList
  },
  data() {
    return {
      contractId: 123,
      customerInfo: {
        email: '<EMAIL>',
        customer_name: '客户名称',
        phone: '13800138000'
      }
    }
  },
  methods: {
    showFileList() {
      this.$refs.fileList.Show();
    }
  }
}
</script>

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|-------|------|
| dialogTitle | String | "附件列表" | 弹窗标题 |
| fileType | String | - | 文件类型，用于区分不同业务的附件 |
| fileId | Number | - | 业务ID，关联的业务数据ID |
| customerInfo | Object | {} | 客户信息，包含邮箱等信息 |

## customerInfo 对象结构

```javascript
{
  email: '<EMAIL>',  // 客户邮箱（发送邮件时的默认收件人）
  customer_name: '客户名称',       // 客户名称
  phone: '13800138000'           // 客户电话
}
```

## 邮件发送功能

1. 点击"发送邮件"按钮
2. 自动填充客户邮箱作为默认收件人
3. 可以修改收件人邮箱、邮件主题和内容
4. 点击"发送"按钮将附件通过邮件发送给客户

## 方法

| 方法名 | 说明 |
|--------|------|
| Show() | 显示附件列表弹窗 |

## 事件

暂无自定义事件。

## 注意事项

1. 使用邮件发送功能需要后端提供相应的邮件发送接口
2. 附件文件会作为邮件附件一同发送
3. 确保传入的 customerInfo 包含有效的邮箱地址 