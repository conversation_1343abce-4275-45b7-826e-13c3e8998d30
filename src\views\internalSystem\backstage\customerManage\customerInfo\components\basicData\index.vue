<template>
  <div style="margin: 0 6px 0 6px">
    <el-form
      :model="ruleForm"
      ref="ruleForm"
      label-width="84px"
      class="mt10"
      label-position="left"
    >
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="客户阶段" prop="customer_stage">
            <el-select
              v-model="ruleForm.customer_stage"
              placeholder="请选择客户阶段"
              disabled
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in params_constant_customer_stage"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户编码" prop="customer_no">
            <el-input
              v-model="ruleForm.customer_no"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售员">
            <el-input
              v-model="fkSaleEmployeeUserInfo"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input
              v-model="ruleForm.customer_name"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="法定名称" prop="customer_legal_name">
            <el-input
              v-model="ruleForm.customer_legal_name"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="维护人员" prop="fk_maintain_employee_id_str">
            <el-input
              v-model="ruleForm.fk_maintain_employee_id_str"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8" class="formItem2">
          <el-form-item label="联系人" prop="link_man">
            <el-input
              v-model="ruleForm.link_man"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="ruleForm.email" :disabled="isEdit" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户法人" prop="customer_legal_person">
            <el-input
              v-model="ruleForm.customer_legal_person"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8" class="formItem2">
          <el-form-item label="联系人手机" prop="telephone">
            <el-input
              v-model="ruleForm.telephone"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="QQ号码" prop="qq">
            <el-input v-model="ruleForm.qq" :disabled="isEdit" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户简介" prop="customer_synopsis">
            <el-input
              v-model="ruleForm.customer_synopsis"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8" class="formItem2">
          <!-- <el-row>
                   <el-col :span="8"> -->

          <el-form-item label="详细地址" prop="link_address">
            <el-input
              v-model="ruleForm.link_address"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="备案日期">
            <el-date-picker
              style="width: 100%"
              v-model="ruleForm.update_time"
              align="right"
              type="date"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <!-- </el-row> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户来源" prop="customer_source">
            <el-select
              v-model="ruleForm.customer_source"
              placeholder="请选择客户来源"
              :disabled="isEdit"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in params_constant_customer_source"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销售员工号" prop="fk_sale_employee_id_number">
            <el-input v-model="ruleForm.fk_sale_employee_id_number" disabled clearable />
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
            <el-form-item label="电话" prop="phone">
              <el-input v-model="ruleForm.phone" :disabled="isEdit" clearable />
            </el-form-item>
          </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销售员姓名" prop="fk_sale_employee_id_name">
            <el-input v-model="ruleForm.fk_sale_employee_id_name" disabled clearable />
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销售员部门" prop="department_name">
            <el-input v-model="ruleForm.department_name" disabled clearable />
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="公司网站" prop="company_site">
            <el-input v-model="ruleForm.company_site" :disabled="isEdit" clearable />
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="人员规模">
            <el-input  v-model="ruleForm.personnel_scale_low" style="width:46%;" :disabled="isEdit"
              clearable />
            <el-button type="text" class="toLabel">-</el-button>
            <el-input  v-model="ruleForm.personnel_scale_high" style="width:46%;" :disabled="isEdit"
              clearable />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="客户类型" prop="customer_type">
            <el-select v-model="ruleForm.customer_type" placeholder="请选择客户类型" :disabled="isEdit" filterable clearable>
				<el-option
					v-for="item in params_constant_customer_type"
					:key="item.id"
					:label="item.label"
					:value="item.value"
				/>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="传真" prop="fax">
            <el-input v-model="ruleForm.fax" :disabled="isEdit" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="企业规模">
            <el-input  v-model="ruleForm.company_scale_low" style="width:46%;" :disabled="isEdit"
              clearable></el-input>
            <el-button type="text" class="toLabel">-</el-button>
            <el-input  v-model="ruleForm.company_scale_high" style="width:46%;" :disabled="isEdit"
              clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所属行业" prop="belong_industry">
            <el-select v-model="ruleForm.belong_industry" placeholder="请选择所属行业" :disabled="isEdit" filterable clearable>
              <el-option v-for="item in params_constant_belong_industry" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="邮政编码" prop="postal_code">
            <el-input  v-model="ruleForm.postal_code" :disabled="isEdit" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所在省份" prop="province_name">
            <el-input v-model="ruleForm.province_name" :disabled="isEdit" clearable></el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="重要等级" prop="important_rank">
            <el-select v-model="ruleForm.important_rank" placeholder="" :disabled="isEdit" filterable clearable>
              <el-option v-for="item in params_constant_important_rank" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所在城市" prop="city_name">
            <el-input v-model="ruleForm.city_name" :disabled="isEdit" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="介绍人/公司" prop="introducer_name">
            <el-input v-model="ruleForm.introducer_name" disabled clearable></el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8" class="formItem2">
          <el-form-item label="成交日期">
            <el-date-picker
              style="width: 100%"
              v-model="ruleForm.deal_time"
              align="right"
              type="date"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户授权码">
            <el-input v-model="ruleForm.software_no" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="介绍公司">
            <el-input v-model="ruleForm.introducer_name" disabled></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="辅维护员" prop="fk_auxtain_employee_id_str">
            <el-input v-model="ruleForm.fk_auxtain_employee_id_str" :disabled="isEdit" clearable></el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row :gutter="30">
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="原有端口数" prop="port_number">
            <el-input v-model="ruleForm.port_number" disabled></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="软件端口" prop="sure_port_number">
            <el-input v-model="ruleForm.sure_port_number" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="阶段备注" prop="remark">
            <el-input v-model="ruleForm.customer_stage_remark" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="软件到期日">
            <el-date-picker
              style="width: 100%"
              v-model="ruleForm.maintain_stop_time"
              align="right"
              type="date"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-checkbox v-model="checked" :disabled="isEdit"
        >是否新增客户财务资料</el-checkbox
      >
      <div v-if="checked">
        <div class="add_customer_finance">客户财务资料</div>
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="客户税号" prop="customer_tax_number">
              <el-input
                v-model="ruleForm.customer_tax_number"
                :disabled="isEdit"
                clearable
               onkeyup="this.value=this.value.replace(/[, ]/g,'')" 

              ></el-input>
              <!--              onkeyup="this.value=this.value.replace(/[, ]/g,'')" -->
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户行" prop="opening_bank">
              <el-input
                v-model="ruleForm.opening_bank"
                :disabled="isEdit"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="收票人" prop="receive_ticket_person">
              <el-input
                v-model="ruleForm.receive_ticket_person"
                :disabled="isEdit"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" class="formItem2">
            <el-form-item label="开票地址" prop="open_ticket_address">
              <el-input
                v-model="ruleForm.open_ticket_address"
                :disabled="isEdit"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="银行账号" prop="customer_account">
              <el-input
                v-model="ruleForm.customer_account"
                :disabled="isEdit"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" class="formItem2">
            <el-form-item label="收票电话" prop="receive_ticket_phone">
              <el-input
                v-model="ruleForm.receive_ticket_phone"
                :disabled="isEdit"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="开票电话" prop="open_ticket_phone">
              <el-input
                v-model="ruleForm.open_ticket_phone"
                :disabled="isEdit"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="收票地址" prop="receive_ticket_address">
              <el-input
                v-model="ruleForm.receive_ticket_address"
                :disabled="isEdit"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="客户填写" prop="in_customer_add">
              <el-select
                v-model="ruleForm.in_customer_add"
                disabled
                filterable
                clearable
                style="width: 100%"
              >
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
.toLabel {
  width: 8%;
  color: black;
  cursor: default;
}

.add_customer_finance {
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
  background: #70676733;
}

@import "@/assets/css/element/font-color.scss";
</style>


