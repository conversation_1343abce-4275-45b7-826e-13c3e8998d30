import API from "@/api/internalSystem/customerManage/customerQuestionCollect";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";

import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";

import { mapGetters } from "vuex";
export default {
  name: "customerQuestionCollect",
  data() {
    return {
      title: "客户文案搜索管理",
      loading: false,
      tableData: [],
      formSearch: {
        questionTitle: "",
        startTime: "",
        endTime: "",
      },
      tableList: [
        {
          name: "搜索关键词",
          value: "question_title",
        },
        {
          name: "搜索时间",
          value: "create_time",
        },
        {
          name: "搜索结果",
          value: "search_results",
        },
        {
          name: "解决时间",
          value: "solve_time",
        },
        {
          name: "解决人",
          value: "employee_name",
        },
        {
          name: "备注",
          value: "remark",
        },
      ],
      templateOption: [],
      isAdd: false,
      isAudit: false,
      hotQueryTableData:[],
      dialogVisible:false
    };
  },

  mounted() {
    this.getList();
  },
  created() {},
  methods: {
    getList(f = false) {
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          // let param ={}
          if (f) param.pageNum = 1;
          param.fk_template_id = 1;
          API.query(param)
            .then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
            })
            .finally(() => {
              this.loading = false;
            });
        });
      // }, 300);
    },
    hotQuery() {
      API.hotQuery().then((res) => {
        this.hotQueryTableData = res.data
        this.dialogVisible =true
      });
    },
    // add() {
    // this.isAdd = true;
    // this.$refs.addCustomerPush.Show();
    // },
    // modify(item) {
    // this.isAdd = true;
    // this.$refs.addCustomerPush.Show(item);
    // },
    // del(item) {

    // let params = {
    //   sales_visiting_id: item.sales_visiting_id,
    // };
    // API.remove(params)
    //   .then(() => {
    //     this.getList();
    //   })
    //   .catch(() => {});
    // },
  },
  components: {
    Pagination,
    TableView,
    MyDate,
  },
  computed: {
    ...mapGetters([""]),
  },
};
