<template>
  <div>
    <el-form :inline="true" :model="formSearch" size="small" v-if="!isAudit">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.tareas_no" placeholder="请输入单据编号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="formSearch.tareas_type" placeholder="请选择审核类型" class="inputBox" filterable clearable>
          <el-option v-for="item in tareasTypeList" :key="item.name" :label="item.name"
            :value="item.name">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="formSearch.customer_name" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
      </el-form-item>
    </el-form>
    <table-view :tableList="tableList" :tableData="tableData" v-if="!isAudit" @thrid="open" 
      isThrid="permanent_button" thridTitle="审核"
      style="font-size: 14px;   "
      >
    </table-view>
    <Pagination ref="pagination" @success="getList" v-show="!isAudit" />
    <!-- 客户意向单审核 -->
    <intention-audit ref="intentionAudit" @selectData="getList" />
    <!-- 客户回访单审核 -->
    <visiting-audit ref="visitingAudit" @selectData="getList" />
    <!-- 客户修改单审核 -->
    <update-audit ref="updateAudit" @selectData="getList" />
    <!-- 客户实施单审核 -->
    <imple-audit ref="impleAudit" @selectData="getList" />
    <!-- 文案审核 -->
    <article-audit ref="articleAudit" @selectData="getList" />
    <!-- 销售报价单审核 -->
    <quotation-audit ref="quotationAudit" @selectData="getList" />
    <!-- 销售合同单审核 -->
    <contract-audit ref="contractAudit" @selectData="getList" />
    <!-- 销售开票单审核 -->
    <ticket-audit ref="ticketAudit" @selectData="getList" />
    <!-- 其他收入单审核 -->
    <income-audit ref="incomeAudit" @selectData="getList" />
    <!-- 发票进项单审核 -->
    <invoice-income-audit ref="invoiceIncomeAudit" @selectData="getList" />
    <!-- 费用报销单审核 -->
    <invoice-audit ref="invoiceAudit" @selectData="getList" />
    <!-- 新版合同单审核 -->
    <new-contract-audit  ref="newContractAudit" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>


  	@import "@/assets/css/element/font-color.scss";
</style>