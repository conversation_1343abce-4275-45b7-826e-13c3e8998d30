import API from "@/api/internalSystem/basicInfo/menu/menuApi.js";
import AddMenu from "./../addMenu/index.vue";
export default {
  data() {
    return {
      filterText: ``,
      menuTreeList: [],
      checkedKey: [],
      defaultProps: {
        children: "children",
        label: "menuName",
        disabled: true
      },
      showCheckbox: false,
      menuId: "", //菜单Id
      parentId: "", //菜单父Id
      parentName: "" //菜单父名称

    };
  },

  mounted() {
    this.getMenuTree();
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    GetCheckedKeys() {
      //获取菜单树选中的节点
      return this.$refs.tree.getCheckedKeys();
    },
    checkFid(data = [], menuId = 0) {
      for (let i = 0; i < data.length; i++) {
        let list = data[i];
        if (list.menuId === menuId) return true;
        if (list.children && list.children.length)
          return this.checkFid(list.children, menuId);
        return false;
      }
    },
    //设置选中菜单树
    SetCheckedKeys(data = []) {
      this.showCheckbox = true;
      this.$refs.tree.setCheckedKeys(data);

    },
    getMenuTree() {
      // 获取菜单列表
      API.queryAll({})
        .then(data => {
          this.menuTreeList = this.forInt(data.data);
        })
        .catch(() => {});

    },
    forInt(data = []) {
      // 遍历把id转为Int类型，后面要求后端直接返回int类型
      let _this = this;
      data.forEach(list => {
        list.menuId = _this.stringToInt(list.menuId);
        if (list.children && list.children.length) _this.forInt(list.children);
      });
      return data;
    },
    stringToInt(id = "0") {
      return parseInt(id);
    },
    filterNode(value, data) {
      // 筛选查询
      if (!value) return true;
      return data.menuName.indexOf(value) !== -1;
    },
    //打开添加菜单会话框
    addMenu(params) {
      if (!params) {
        params = {
          menuId: 0,
          menuName: "无上级菜单",
          level: 0
        };
      }
      let menuObject = {
        parentId: params.menuId,
        parentName: params.menuName,
        menuId: "",
        level: params.level + 1
      };
      this.$refs.AddMenu.Show(menuObject);
    },
    //打开编辑菜单会话框
    editMenu(params) {
      let menuObject = {
        parentId: "",
        parentName: "",
        menuId: params.menuId,
        level: ""
      };
      this.$refs.AddMenu.Show(menuObject);
    },

    //删除菜单
    delMenu(params) {
      this.$confirm("此操作将删除该菜单, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          let delIds = [];
          delIds.push(params.menuId);
          API.remove({
              menuId: delIds
            })
            .then(() => {
              this.getMenuTree();
            })
            .catch(() => {})
            .finally(() => {});
        })
        .catch(() => {});
    },
    clickDeal(currentObj, treeStatus) {
      // 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。
      if (treeStatus) {
        // 子节点只要被选中父节点就被选中
        this.selectedParent(currentObj);
      } else {
        // 未选中 处理子节点全部未选中
        if (currentObj.children.length !== 0) {
          this.sameChildrenNode(currentObj, false);
        }
      }
    },
    // 统一处理子节点为相同的勾选状态
    sameChildrenNode(currentObj, treeStatus) {
      this.$refs.tree.setChecked(currentObj.menuId, treeStatus);
      for (let i = 0; i < currentObj.children.length; i++) {
        this.sameChildrenNode(currentObj.children[i], treeStatus);
      }
    },
    // 统一处理父节点为选中
    selectedParent(currentObj) {
      let currentNode = this.$refs.tree.getNode(currentObj);
      if (currentNode.parent.key !== undefined) {
        this.$refs.tree.setChecked(currentNode.parent, true);
        this.selectedParent(currentNode.parent);
      }
    },
    //全选
    chooseAll(data, treeStatus) {
      this.sameChildrenNode(data, treeStatus);
    }
  },
  components: {
    AddMenu
  }
};