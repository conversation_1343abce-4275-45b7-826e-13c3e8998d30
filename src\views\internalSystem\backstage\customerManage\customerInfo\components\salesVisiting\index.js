import API from '@/api/internalSystem/customerManage/salesVisiting'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddVisiting from "@/views/internalSystem/backstage/customerManage/salesVisiting/components/addVisiting/index.vue";
export default {
  name: "salesTransferList",
  data() {
    return {
      loading: false,
      tableData: [],
      tableList: [
        {
          name: "单据编号",
          value: "sales_visiting_no",
          width: 160
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "联系人",
          value: "link_man",
          width: 120
        },
        {
          name: "联系电话",
          value: "phone",
          width: 120
        },
        {
          name: "联系时间",
          value: "contact_time",
          width: 120
        },
        {
          name: "回访方式",
          value: "visiting_form_format",
          width: 120
        },
        {
          name: "录入日期",
          value: "updateTime",
          width: 120
        },
        {
          name: "跟进内容",
          value: "follow_content",
          width: 200
        },
        {
          name: "跟进内容",
          value: "customer_demand",
          width: 200
        }
      ],
      isAdd: false
    };
  },
  props: {
    customer_id: {
      type: Number
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.isAdd = false;
      let param = Object.assign(this.$refs.sales_pagination.obtain());
      param.customer_id = this.customer_id;
      API.query(param).then(res => {
        this.tableData = res.data
        this.$refs.sales_pagination.setTotal(res.totalCount)
      }).finally(() => {
        this.loading = false
      })
    },
    modify(item) {
      let params = {
        sales_visiting_id: item.sales_visiting_id
      };
      API.getInfo(params)
        .then(data => {
          this.isAdd = true;
          this.$refs.addVisiting.Show(data.data);
        })
        .catch(() => {});
    },
  },
  components: {
    TableView,
    Pagination,
    AddVisiting
  }
};
