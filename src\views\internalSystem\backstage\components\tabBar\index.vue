<template>
  <div>
    <el-tabs
      v-model="current"
      type="card"
      @tab-remove="removeTab"
      @tab-click="clickTab"
    >
      <el-tab-pane
        v-for="item in tabList"
        :key="item.path"
        :label="item.meta.title"
        :name="item.path"
        :closable="!item.meta.closable"
      >
        <slot></slot>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { mapGetters, mapMutations } from "vuex";
export default {
  name: "tabBar",
  data() {
    return {
      tabList: [
        {
          fullPath: "/backstage/statistics/statisticsPage",
          hash: "",
          meta: {
            closable: true,
            keepAlive: false,
            title: "首页",
          },
          name: "statisticsPage",
          path: "/backstage/statistics/statisticsPage",
          params: {},
          query: {},
        },
      ],
      current: "/backstage/statistics/statisticsPage",
    };
  },
  watch: {
    $route: {
      handler(route) {
        this.addTab(route);
        this.current = route.path;
      },
      immediate: true,
      deep: true,
    },
    closeRoute: function(newVal) {
      if (newVal) {
        this.removeTab(newVal);
      }
    },
  },
  methods: {
    removeTab(e) {

      let index = this.getTabIndex(e);
      if (!index && index !== 0) {
        console.error("index异常：" + index);
      }
      if (e === this.current) {
        let len = this.tabList.length;
        let next = "/";
        if (index === len - 1) {
          next = this.tabList[index - 1];
        } else {
          next = this.tabList[index + 1];
        }
        this.$router.push({
          path: next.path,
        });
      }
     
      this.tabList.splice(index, 1);
      let routeList = [];
      this.tabList.forEach((element) => {
        routeList.push(element.name);
      });
      this.SET_ROUTELIST(routeList);
      this.SET_CLOSEROUTER("");
    },
    getTabIndex(e) {
      let i;
      this.tabList.map((item, index) => {
        if (item.path === e) {
          i = index;
        }
      });
      return i;
    },
    addTab(val) {
      if (this.tabList.every((ele) => ele.path != val.path)) {
        this.tabList.push(val);
        let routeList = [];
        this.tabList.forEach((element) => {
          routeList.push(element.name);
        });
        this.SET_ROUTELIST(routeList);
      }
    },
    clickTab(e) {
      this.$router.push({
        path: e.name,
      });
    },
    ...mapMutations(["SET_ROUTELIST", "SET_CLOSEROUTER"]),
  },
  computed: {
    ...mapGetters(["closeRoute"]),
  },
};
</script>

<style lang="scss" scoped>
/deep/.el-tabs--card>.el-tabs__header .el-tabs__item .el-icon-close{
  font-size: 16px;
}
/deep/ .el-tabs--card>.el-tabs__header .el-tabs__item.is-active.is-closable .el-icon-close{
  width: 16px;
}
</style>
