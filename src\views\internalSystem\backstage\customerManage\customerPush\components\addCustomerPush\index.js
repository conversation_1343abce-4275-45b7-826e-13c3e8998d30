import API from "@/api/internalSystem/customerManage/customerPushLog/index.js";
import customerAPI from "@/api/internalSystem/customerManage/customerInfo";
import ContractList from "@/views/internalSystem/backstage/components/contractList/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import MyDate2 from "@/views/internalSystem/backstage/components/myDate/index2.vue";
import CustomerList from "@/views/internalSystem/backstage/components/customerList/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import { concat } from "lodash";
import { mapGetters } from "vuex";
import { dateFormat } from "@/common/internalSystem/common.js";
import moment from "moment";
export default {
  name: "addCustomerPush",
  components: {
    ContractList,
    MyDate,
    MyDate2,
    TableView,
    CustomerList,
  },
  props: {
    templateOption: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {},
      loading: false,
      tableData: [],
      tableList: [
        {
          name: "客户编码",
          value: "customer_no",
        },
        {
          name: "客户名称",
          value: "customer_name",
        },
        {
          name: "维护结束时间",
          value: "maintain_stop_time",
        },
        {
          name: "客户阶段",
          value: "customer_stage_format",
        },
        {
          name: "端口数",
          value: "sure_port_number",
        },
        {
          name: "手机",
          value: "telephone",
        },
        {
          name: "销售员姓名",
          value: "fk_sale_employee_id_name",
        },
      ],
      proObj: {},
      isDel: true,
      rules: {
        fk_template_id: [
          {
            required: true,
            message: "请选择推送模板",
            trigger: "change",
          },
        ],
        // plan_send_time: [
        //   {
        //     required: true,
        //     message: "请选择推送模板",
        //     trigger: "change",
        //   },
        // ],
      },
    };
  },
  mounted: {},
  methods: {
    chooseCustomer() {
      this.$refs.customerAllList.Show();
    },
    //客户列表选择
    async getCustomerInfo(info = []) {
      this.tableData = concat(this.tableData, info);
      let setList = [];
      let tmpList = [];
      if (this.tableData && this.tableData.length > 0) {
        this.tableData.map((item) => {
          if (!setList.includes(item["customer_id"])) {
            setList.push(item["customer_id"]);
            tmpList.push(item);
          }
        });
        this.tableData = tmpList;
      }
    },
    /**
     *
     */
    async Show(data = null) {
      // this.proObjRest();
      this.dialogVisible = true;
      this.isDel = true;

      if (data) {
        this.ruleForm = data;
        this.ruleForm.fk_template_id = Number(this.ruleForm.fk_template_id);
        let res = await API.getCustomerPushDetail({
          fk_customer_push_log_id: data.customer_push_log_id,
        });
        let push_customer_ids = [];
        let detailMap = {};
        res.data.map((item) => {
          push_customer_ids.push(item["id"]);
          detailMap[item["id"]] = item;
        });
        let res2 = await customerAPI.query({
          pageNum: 1,
          pageSize: push_customer_ids.length,
          push_customer_ids: push_customer_ids,
        });

        this.tableData = res2.data;
        this.tableData.map((item) => {
          if (detailMap[item["customer_id"]]["send_state"] === 1) {
            item["send_state"] = "推送成功";
          } else if (detailMap[item["customer_id"]]["send_state"] === 2) {
            item["send_state"] = "推送失败";
          } else {
            item["send_state"] = "未推送";
          }

          item["send_time"] = detailMap[item["customer_id"]]["send_time"];
          item["send_result"] = detailMap[item["customer_id"]]["send_result"];
        });
        this.tableList.push({ name: "推送状态", value: "send_state" });
        this.tableList.push({ name: "推送时间", value: "send_time" });
        this.tableList.push({ name: "推送结果", value: "send_result" });

        this.isDel = false;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel(flag = true) {
      //保存之后，不退出当前页面
      if (flag) {
        this.dialogVisible = false;
        this.$emit("selectData");
      }
      this.resetForm("ruleForm");
      this.clearData();
    },
    async save() {
      let params = this.ruleForm;

      if (!this.tableData || this.tableData.length === 0) {
        return this.error("请至少选择一个推送客户");
      }
      //如果不选择，默认11分钟之后
      if (!params.plan_send_time) {
        params.plan_send_time = dateFormat(
          "yyyy-MM-dd HH:mm:ss",
          new Date(new Date().getTime() + 1000 * 60 * 11)
        );
      }

      if (
        new Date(params.plan_send_time).getTime() - new Date().getTime() <
        1000 * 60 * 10
      ) {
        return this.error("计划推送时间至少要大于当前10分钟");
      }

      params.fk_template_code = this.templateOption.filter(
        (item) => item.value === params.fk_template_id
      )[0]["code"];

      // this.loading = true;
      let userList = [];
      this.tableData.map((item) => {
        userList.push({
          user_id: item["customer_id"],
          phone: item["telephone"],
        });
      });
      params.detailList = userList;
      params.customer_number = userList.length;
      params.send_type = 3;


      API.save(params)
        .then((res) => {
          this.dialogCancel(false);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {};
      this.tableData = [];
      this.tableList = this.tableList.splice(0, 7);
    },
    del(item) {
      let index = 0;
      for (let i = 0; i < this.tableData.length; i++) {
        if (item["customer_id"] === this.tableData[i]["customer_id"]) {
          index = i;
        }
      }

      this.tableData.splice(index, 1);
    },
  },
  computed: {
    ...mapGetters(["buttonPermissions", "userInfo"]),
  },
};
