import Axios from "@/api";
import environment from "@/api/environment";

export default {
  //bug 列表
  getBugList: (params) => {
    return Axios.post(`${environment.internalSystemAPI}problem/query`, params);
  },

  //新增
  addBug: (params) => {
    return Axios.post(`${environment.internalSystemAPI}problem/add`, params);
  },

  //删除
  removeBug: (params) => {
    return Axios.post(`${environment.internalSystemAPI}problem/remove`, params);
  },

  //更新
  updateBug: (params) => {
    return Axios.post(`${environment.internalSystemAPI}problem/update`, params);
  },
  //获取单条
  getBugInfo: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}problem/getInfo`,
      params
    );
  },

  //指派
  assignedBug: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}problem/assigned`,
      params
    );
  },

  //更改确定状态
  updateConfirm: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}problem/updateConfirm`,
      params
    );
  },
  // 更改问题状态
  updateProblemState: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}problem/updateProblemState`,
      params
    );
  },
};
