<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>更新日志</title>
  </head>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      list-style: none;
    }
    body {
      max-width: 1200px;
      margin: 0 auto;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }
    
    .logList {
      margin: 20px 0;
    }
    .log-item {
      width: 100%;

      margin-bottom: 20px;
      padding: 0 20px;
      border: solid 1px #ededed;
      box-sizing: border-box;
    }
    .log-item-head {
      line-height: 58px;
      cursor: pointer;
      width: 100%;
    }
    .log-item-head .side-left {
      float: left;
      font-size: 18px;
      color: #3d3d3d;
    }
    .log-item-head .side-right {
      float: right;
      font-size: 14px;
      color: #9b9b9b;
    }
    .side-right-img {
      width: 14px;
      height: 14px;
      margin-left: 20px;
    }
    /* .active .side-right-img {
      transform: rotateX(180deg);
    } */

    .active .log-item-head {
      border-bottom: solid 1px #ededed;
    }
    .clearfix:after {
      content: ""; /*设置内容为空*/
      height: 0; /*高度为0*/
      line-height: 0; /*行高为0*/
      display: block; /*将文本转为块级元素*/
      visibility: hidden; /*将元素隐藏*/
      clear: both; /*清除浮动*/
    }
    .clearfix {
      zoom: 1; /*为了兼容IE*/
    }

    .log-item-body {
      padding-bottom: 20px;
      width: 100%;
      box-sizing: border-box;
      transition: 1s;
    }
    .log-item-body ul {
      padding-left: 15px;
      box-sizing: border-box;
      line-height: 2;
      color: #9b9b9b;
    }

    .row {
      margin-right: -15px;
      margin-left: -15px;
      box-sizing: border-box;
      display: block;
    }
    @media screen and (max-width: 768px) {
      /* .log-item-body .row {
        display: block;
      } */
      .log-item-body .row .col-6 {
        padding-left: 15px;
        width: 100%;
      }
    }
    .col-6 {
      width: 43%;
      float: left;
      /* box-sizing: border-box; */
      /* display: inline-block; */
      padding-left: 55px;
      padding-right: 15px;
      box-sizing: border-box;
      vertical-align: top;
    }
    .log-item-tag {
      display: inline-block;
      margin-top: 15px;
      margin-bottom: 15px;
      padding: 0 10px;
      font-size: 15px;
      line-height: 30px;
      color: #ffffff;
      border-radius: 2px;
      width: 100%;
      max-width: 60px;
      text-align: center;
    }
    .log-item-tag--new {
      background-color: #4fc6bd;
    }
    .log-item-tag--fixed {
      background-color: #d84535;
    }
  </style>
  <body>
    <div class="logList" id="logList">
      <script type="text/html" id="logitem">
            {{each data as value }}
        <div
          class="log-item"

        >
          <div class="log-item-head clearfix" >
            <div class="side-left">{{value.name}}</div>
            <div class="side-right" >
              {{value.time}}
              <img class="side-right-img" src="https://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/SFPZ2N38_3157_system_bottom.png" alt="" />
            </div>
          </div>

          <div class="log-item-body" style="display:none" data-open="false">
            <div class="row clearfix">
              <div class="col-6">
                <div class="log-item-tag log-item-tag--new ">New</div>
                <ul>
                    {{each value.newList as nItem }}
                  <li

                  >

                    {{nItem}}
                  </li>
                  {{/each}}
                </ul>
              </div>
              <div class="col-6 clearfix">
                <div class="log-item-tag log-item-tag--fixed "  >
                  Fixed
                </div>
                <ul>
                    {{each value.flxedList as fItem }}

                  <li

                  >
                    {{fItem}}
                  </li>
                  {{/each}}
                </ul>
              </div>
            </div>
          </div>
        </div>
        {{/each}}
      </script>
    </div>
  </body>
  <script src="https://cdn.bootcdn.net/ajax/libs/jquery/1.12.4/jquery.js"></script>
  <script src="https://jiqin-assets.oss-cn-qingdao.aliyuncs.com/template/index.js"></script>

  <script>
    var logList = {
      data: [
        {
          open: true,
          name: "BusinessV1.0.0.1.0053.2",
          time: "2020-07-17",
          newList: [],
          flxedList: ["1.修复了出库单检查信用额度不准确的问题"]
        },
        {
          name: "BusinessV1.0.0.1.0053.1",
          time: "2020-07-14",
          newList: ["1.优化共享库存功能"],
          flxedList: ["1.修复了共享库存不准确的问题"]
        },
        {
          name: "BusinessV1.0.0.1.0053",
          time: "2020-04-27",
          newList: [
            "1.新增供应商预付款按单位核算",
            "2.新增客户未分配到款按单位核算",
            "3.新增合同允许输入重复型号",
            "4.新增备货取消单,用于取消不需采购的备货单",
            "5.新增坏账冲销单,用于核销客户欠款",
            "6.新增返利确认单,用于做供应商返利金额",
            "7.销售业绩统计和客户应收款统计增加未税销售金额和未税毛利",
            "8.销售合同单、销售出库单、直发出库单、销售退货单增加未税毛利",
            "9.合同改价单增加修改客户合同号及合同整单备注功能",
            "10.增加库存借货单及库存还货单,用于简单的客户借货操作",
            "11.增加实时库存查询,较库存查询相比隐藏了采购进价和采购金额",
            "12.增加承兑汇票登记单和承兑汇票支出单,用于简单的承兑汇票管理",
            "13.将产品分类设置、产品信息设置、产品导入单、产品库查询统一移动到产品管理,位于进销存管理模块目录下",
            "14.增加产品禁用单,用于快速禁用产品",
            "15.成套生产模块增加生产取消单,作用和请购取消单类似",
            "16.增加供应商借货管理",
            "17.优化组装拆卸单",
            "18.取消销售锁库单强制锁库,请购在途可以通过预约取消单取消",
            "19.增加销售退票单和采购退票单",
            "20.取消系统参数金额小数点的调整选项",
            "21.优化销售合同单、销售出库单、销售开票单界面",
            "22.直发出库单增加快递单号的修改",
            "23.优化了首页导航栏界面",
          ],
          flxedList: [
            "1.修复了当销售端和采购端都是不含税的时候,自动锁库会导致合同毛利为空的问题",
            "2.修复了合同修改单修改客户库存显示的客户不变的问题",
            "3.修复了采购合同重新选择仓库,入库时仓库不变的问题",
            "4.修复了采购修改单修改了价格,销售出库单毛利率不变的问题",
            "5.修复了快递单号长度过短的问题",
            "6.修复了系统导航栏未读邮件数量未更新的问题",
            "7.修复了企业经营情况税金及附加重复显示的问题",
            "8.修复了销售合同部分请购,销售锁库单依旧能锁定全部合同数量的问题",
            "9.修复了采购员导航栏显示超期未收票和采购合同仅选未收票数据不匹配的问题",
            "10.修复了产品导入单产品描述之类的信息没填会覆盖回产品信息的问题",
            "11.修复了采购付款单的小数点问题",
          ]
        }
      ]
    };
    var OpenImg =
      "https://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/Rnjhf4cx_4353_system_top.png";
    var CloseImg =
      "https://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/SFPZ2N38_3157_system_bottom.png";
    var result = template("logitem", logList);
  

    var id=getUrlParam('id')||0
  

    $("#logList").html(result);
    //默认展开第一个
    $(".log-item-body:first").show();
    $(".log-item-body:first").parent().addClass("active");
    $(".log-item-body:first")
      .parent()
      .find(".side-right-img")
      .attr("src", OpenImg);
    $(".log-item-body:first").attr("data-open", true);
    //点击触发展开关闭操作
    $(".log-item-head").click(function () {
      var open = false;
      var img = "";
      //根据获得的属性的值去判断
      var flag =
        $(this).parent().find(".log-item-body").attr("data-open") == "false";
      if (flag) {
        open = true;
        $(this).parent().find(".log-item-body").show();
        $(this).parent().addClass("active");

        img = OpenImg;
      } else {
        open = false;
        img = CloseImg;
        $(this).parent().find(".log-item-body").hide();
        $(this).parent().removeClass("active");
      }
      $(this).find(".side-right-img").attr("src", img);
      $(this).parent().find(".log-item-body").attr("data-open", open);
    });


    function getUrlParam(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
  var r = window.location.search.substr(1).match(reg); //匹配目标参数
  if (r != null) return unescape(r[2]);
  return null; //返回参数值
}
  </script>
</html>
