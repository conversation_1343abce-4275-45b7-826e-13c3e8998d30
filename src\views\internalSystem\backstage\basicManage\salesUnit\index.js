import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue"
import update from './update.vue'
import API from '@/api/internalSystem/basicManage/salesUnit'

export default {
	data() {
		return {
			loading: false,
			tableData: [],
			tableList: [{
				name: "公司名称",
				value: "company_name"
			}, {
				name: "公司简称",
				value: "company_short_name"
			}, {
				name: "公司税号",
				value: "company_tax_number"
			}, {
				name: "公司法人",
				value: "company_legal_person"
			}, {
				name: "开户行",
				value: "opening_bank"
			}, {
				name: "银行账号",
				value: "bank_account"
			}, {
				name: "公司开票城市",
				value: "operate_city_name"
			}],
			formSearch: {
				company_name: ``
			}
		}
	},
	methods: {
		del(row) {
			API.remove(row).then(() => {
				this.getList()
			}).catch(() => {})
		},
		getList(f = false) {
			this.loading = true
			let param = Object.assign(this.formSearch, this.$refs.pagination.obtain())
			if (f) param.pageNum = 1
			API.query(param).then(res => {
				this.tableData = res.data
				this.$refs.pagination.setTotal(res.totalCount)
			}).catch(() => {
			}).finally(() => {
				this.loading = false
			})
		}
	},
	components: {
		Pagination,
		update,
		TableView
	},
	mounted() {
		this.getList()
	}
}
