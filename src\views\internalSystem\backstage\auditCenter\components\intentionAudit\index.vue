<template>
  <div v-if="dialogVisible" style="overflow-y:auto;overflow-x: hidden;">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form :model="ruleForm" ref="ruleForm" label-width="160px" class="mt10">
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input
              disabled
              v-model="ruleForm.customer_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系人" prop="link_man">
            <el-input disabled v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户类型" prop="customer_type">
            <el-select
              disabled
              v-model="ruleForm.customer_type"
              placeholder="请选择客户类型"
              filterable
              clearable
            >
              <el-option
                v-for="item in params_constant_customer_type"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input disabled v-model="ruleForm.phone" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="主营品牌" prop="brand">
            <el-input disabled v-model="ruleForm.brand" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系时间" prop="contact_time">
            <el-input
              disabled
              v-model="ruleForm.contact_time"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户地区" prop="province">
            <el-input
              disabled
              v-model="ruleForm.province_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户阶段" prop="customer_stage">
            <el-select
              disabled
              v-model="ruleForm.customer_stage"
              placeholder="请选择客户阶段"
              filterable
              clearable
            >
              <el-option
                v-for="item in params_constant_customer_stage"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="所在城市" prop="city">
            <el-input
              disabled
              v-model="ruleForm.city_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户行业" prop="belong_industry">
            <el-select
              disabled
              v-model="ruleForm.belong_industry"
              placeholder="请选择客户行业"
              filterable
              clearable
            >
              <el-option
                v-for="item in params_constant_belong_industry"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户来源" prop="customer_source">
            <el-select
              disabled
              v-model="ruleForm.customer_source"
              placeholder="请选择客户来源"
              filterable
              clearable
            >
              <el-option
                v-for="item in params_constant_customer_source"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="16">
          <el-form-item label="是否使用过erp软件" prop="is_software">
            <el-radio disabled v-model="ruleForm.is_software" label="0"
              >否</el-radio
            >
            <el-radio disabled v-model="ruleForm.is_software" label="1"
              >是</el-radio
            >
          </el-form-item>
        </el-col>
        <el-col :xl="16">
          <el-form-item
            v-if="ruleForm.is_software == 1"
            label=""
            prop="use_describe"
          >
            <el-input
              disabled
              type="textarea"
              placeholder="请输入使用描述"
              v-model="ruleForm.use_describe"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="16">
          <el-form-item label="报价方案" prop="quotation_scheme">
            <el-input
              disabled
              type="textarea"
              placeholder="请输入报价方案"
              v-model="ruleForm.quotation_scheme"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="16">
          <el-form-item label="跟进记录" prop="follow_records">
            <el-input
              disabled
              type="textarea"
              placeholder="请输入跟进记录"
              v-model="ruleForm.follow_records"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="16">
          <el-form-item label="企业痛点/软件需求" prop="software_demand">
            <el-input
              disabled
              type="textarea"
              placeholder="请输入企业痛点/软件需求"
              v-model="ruleForm.software_demand"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisibleAudit"
      append-to-body
      @close="dialogCancelAudit"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="auditForm"
        :rules="rules"
        ref="auditForm"
        label-width="100px"
      >
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select
            v-model="auditForm.auditState"
            placeholder="请选择审核状态"
            filterable
            clearable
          >
            <template v-for="item in contract_auditStateList">
              <el-option :key="item.id" :label="item.label" :value="item.value">
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->

        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="auditRemark">
          <el-input
            v-model="auditForm.auditRemark"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('auditForm')"
          :loading="loading"
          >保 存</el-button
        >
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js"></script>

<style lang="scss" scoped>
@import "@/assets/css/element/font-color.scss";
</style>
