<template>
  <div class="index" :class="[homeIndent ? 'index-indent' : '']">
    <index-header class="header" />
    <index-aside class="aside" />
    <tab-bar class="tab"></tab-bar>
    <keep-alive>
      <router-view class="main loading-area" />
    </keep-alive>
  </div>
</template>

<script>
import IndexHeader from "./components/IndexHeader/IndexHeader";
import IndexAside from "./components/IndexAside/IndexAside";
import TabBar from "./components/tabBar";
import API from "@/api/internalSystem/common/index";
import { mapActions, mapGetters } from "vuex";

export default {
  components: {
    IndexAside,
    IndexHeader,
    TabBar,
  },
  data() {
    return {};
  },
  methods: {

    loginOut() {

      API.loginOut({})
        .then(() => { })
        .catch(() => { });
    },
    ...mapActions([
      "GET_USERINFO",
      "GET_PROVINCES",
      "GET_INFO",
      "GET_CITY",
      "INIT_WS",
      "getConstant",
    ]),
  },

  mounted() {
    this.GET_USERINFO();

    this.GET_INFO();
    // 获取省份
    // this.GET_PROVINCES();
    // 获取城市列表
    // this.GET_CITY();
    this.getConstant();
    this.INIT_WS();

  },
  computed: {
    ...mapGetters(["homeIndent"]),
  },
};
</script>
<style lang="scss" scoped>
.index {
  width: 100%;
  height: 100%;
  position: relative;
  transition: all 0.2s ease;
  .header {
    position: fixed;
    left: 0;
    top: 0;
  }

  .aside {
    // transition: all 0.3s ease;
    width: 200px;
    height: 100%;
    position: fixed;
    left: 0;
    top: 60px;
    overflow: auto;
  }

  .tab {
    // transition: all 0.3s ease;
    display: block;
    position: absolute;
    // width: calc(100% - 200px);
    width: calc(100% - 70px);
    top: 15px;
    left: 200px;
    height: calc(100% - 60px);
    box-sizing: border-box;
    padding: 15px;
  }

  .main {
    transition: width 0.1s ease;
    display: block;
    position: absolute;
    width: calc(100% - 200px);
    top: 72px;
    left: 200px;
    // margin-left: 200px;
    height: calc(100% - 80px);
    box-sizing: border-box;
    // padding: 15px;
    padding: 7px 10px 7px 7px;
    background-color: #f5f6fa;
  }
}
$indentWidth: 90px;
.index-indent {
  .aside {
    width: $indentWidth;
  }
  .tab {
    left: $indentWidth;
    // width: calc(100% - $indentWidth);
  }
  .main {
    left: $indentWidth;
    width: calc(100% - 90px);
  }
}
</style>
