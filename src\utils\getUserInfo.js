import API from '@/api/user/login'
import environment from '@/store/environment'
import {Message} from "element-ui";
import Cookies from "js-cookie";
export function getUserInfo() {
	// 部分平台获取用户信息需要调用不同的接口，因此要区分
	let key = `getUserInfoByToken`
	switch (environment.state.env) {
		case 'erp':
		case 'product':
			key = `refreshLogin`
			break
		case 'internalSystem':
			key=`internalGetInfo`;
			break
		default:
			key = `getUserInfoByToken`
	}
	API[key]().then(res => {
		let user = res.data || res
		if (!user.userId) {
			Message.error(`未获取到用户id，即将跳转到首页`)
			setTimeout(()=>{
				let defaultConfig = environment.state.defaultConfig[environment.state.env];
				Cookies.remove(defaultConfig.cookiesKey);
				window.location.replace(`/#/login`)
			},500)
			
			return
		}
		// 保存用户信息
		environment.state.userInfo = user
	}).catch(() => {
	})
}
