import API from "@/api/internalSystem/bugManage/module/index.js";

export default {
  props:{
    leftIndent:{
      type:Boolean,
      require:true
    }
  },
  data() {
    return {
      filterText: ``,
      menuTreeList: [],
      checkedKey: [],
      defaultProps: {
        children: "children",
        label: "module_name",
        disabled: true,
      },
      showCheckbox: false,
      currentId:"",//当前模块id
      brand_id: "",
      title:""
    };
  },

  mounted() {
    // this.getMenuTree()
  },
  methods: {
    indentClick(){
      this.$emit("indent")
    },
    GetCheckedKeys() {
      //获取模块树选中的节点
      return this.$refs.tree.getCheckedKeys();
    },
    clickDeal(){

    },
    nodeClick(data){
       
        this.currentId=data.module_id;
        this.$emit("search",data)
    },

    allSearch(){
      this.currentId='';
      
      this.$emit("search",{})
    },
    getMenuTree({brand_type='',brand_id=''}) {
      // 获取模块列表
      if (!brand_id) return;
      this.brand_id = brand_id;
      this.currentId=''
      this.title=brand_type
      API.query({
        brand_id,
      })
        .then((data) => {
          this.menuTreeList = this.forInt(data.data);
        })
        .catch(() => {});
    },
   
    forInt(data = []) {
      // 遍历把id转为Int类型，后面要求后端直接返回int类型
      let _this = this;
      data.forEach((list) => {
        list.module_id = _this.stringToInt(list.module_id);
        if (list.children && list.children.length) _this.forInt(list.children);
      });
      return data;
    },
    stringToInt(id = "0") {
      return parseInt(id);
    },



 
    
  },
  
};
