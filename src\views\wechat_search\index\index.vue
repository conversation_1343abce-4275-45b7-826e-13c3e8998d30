<template>
  <div class="container">
    <topNav class="top-nav" />
    <div class="search-container">
      <div class="search-input-group">
        <input type="text" class="search-input" v-model="searchInput" placeholder="请输入型号/订货号" />
      </div>
      <div class="search-btn" @click="getData">搜索</div>
    </div>
    <div id="scroll-container" class="good-list">
      <div
        class="good-item"
        v-for="(item,index) in list"
        :key="index"
        @click="$router.push(`/detail?ID=${item.ID}`)"
      >
        <!--				<div class="good-img">-->
        <!--					<i class="iconfont icontupian"></i>-->
        <!--				</div>-->
        <div class="good-det">
          <div>
            <span class="good-brand">{{item.GBrand}}</span>
            <span class="good-name">{{item.GName}}</span>
          </div>
          <div class="param-item">
            <span class="param-label">
              型<span style="visibility:hidden;">货</span>号：
            </span>
            {{item.GID}}
          </div>
          <div class="param-item">
            <span class="param-label">订货号：</span>
            {{item.GIndex}}
          </div>
          <div class="param-item">
            <span class="param-label">
              面<span style="visibility:hidden;">货</span>价：
            </span>
            ¥{{item.GfCpMj}}
          </div>
          <div class="param-item">
            <span class="param-label">
              货<span style="visibility:hidden;">货</span>期：
            </span>
            {{item.GGhHq}}
          </div>
          <div class="param-item">
            <span class="param-label">
              库<span style="visibility:hidden;">货</span>存：
            </span>
            {{item.FNumb}}{{item.GUnit}}
          </div>
        </div>
				<div class="more-icon">
					详情<i class="iconfont iconleft"></i>
				</div>
				
      </div>
      <div class="empty-container" v-if="list.length === 0">
        <van-empty image="search" description="无数据" />
      </div>
    </div>
    <div class="technicalSupport">技术支持:福州吉勤信息科技有限公司</div>
  </div>
</template>

<script>
import Api from "@/api/wechat_search/index.js";
import topNav from "../components/topNav";

export default {
  name: "search_index",
  components: { topNav },
  data() {
    return {
      refreshing: false,
      loading: false,
      finished: false,
      searchInput: "",
      totalCount: 0,
      pageNum: 1,
      pageSize: 10,
      list: [],
    };
  },
  mounted() {
    const gid = this.$route.query.gid;
    if (gid) this.searchInput = gid;
    this.getData();
  },
  activated() {
    const scrollTop = this.$route.meta.scrollTop;
    const $content = document.querySelector("#scroll-container");
    if (scrollTop && $content) {
      $content.scrollTop = scrollTop;
    }
  },
  methods: {
    // 下拉刷新
    onRefresh() {
      this.totalCount = 0;
      this.pageNum = 1;
      this.getData();
    },
    onLoad() {
      if (this.pageNum >= Math.ceil(this.totalCount / this.pageSize)) {
        // 加载完了
        this.finished = true;
        return;
      }
      this.pageNum += 1;
      this.getData();
    },
    getData() {
      if (!this.searchInput) return this.$toast("请输入关键词");
      this.$toast.loading({
        duration: 10,
        message: "加载中...",
        forbidClick: true,
      });
      Api.query({
        keyword: this.searchInput,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      })
        .then((res) => {
          this.list = res.data;
          if (this.pageNum > 1) this.list = this.list.concat(res.data);
          this.totalCount = res.totalCount;
        })
        .catch((err) => {})
        .finally(() => {
          this.loading = false;
          this.refreshing = false;
          this.$toast.clear();
        });
    },
  },
};
</script>

<style scoped lang="scss">
@import "index";
</style>
