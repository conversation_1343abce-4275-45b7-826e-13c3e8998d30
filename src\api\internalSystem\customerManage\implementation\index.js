import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/query`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/add`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/getInfo`, params)
  },
  // 审核
  updateAudit: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/updateAudit`, params)
  },
  // 上传附件
  addFile: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/addFile`, params)
  },
  // 附件列表
  fileList: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/fileList`, params)
  },
  // 删除附件
  delFile: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/delFile`, params)
  },
  // 合同列表
  contractList: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/contractList`, params)
  }
};