.logincontainer {
  width: 100%;
  height: 100vh;
  background-image: url("https://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/da6kicFw_170807_system_%E9%A6%96%E9%A1%B5%E5%9B%BE%E7%89%87.png");
  background-size: 100% 100%;

  .loginBox {
    width: 691px;
    height: 412px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    overflow: hidden;
    border-radius: 5px;
    box-shadow: 0px 6px 30px 0px rgba(0, 0, 0, 0.6);
    display: flex;

    .leftImg {
      width: 264px;
      flex: 0 0 264px;
      height: 100%;
      background-image: url("https://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/sXfM63fB_103630_system_tree.png");

      .logo-box {
        width: 20%;
        height: 20%;
        margin: 0 auto;
        margin-top: 70px;
      }

      .erpText-box {
        width: 50%;
        height: 50%;
        margin: 0 auto;
        text-align: center;
        color: #fff;
        font-weight: 600;
        font-size: 22px;
        letter-spacing: 2px;
      }
    }

    .rightBox {
      width: 100%;
      height: 100%;
      background-color: #fff;

      .mainContent {
        width: 80%;
        height: 100%;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        vertical-align: top;
        .check-Box{
          height: 30px;
          flex: 0 0 30px;
          border-bottom: 0;
        }
        .input-box {
          height: 40px;
          flex: 0 0 40px;
          border-bottom: 1px solid #c6c6c6;
          display: flex;
         
          &.loginButton {
            border-bottom: 0;
          }

          .icon-box {
            width: 40px;
            height: 40px;
            flex: 0 0 40px;

            i {
              font-size: 26px;
              color: #4ac2ee;
              line-height: 40px;
            }
          }

          .labelName {
            font-size: 16px;
            width: auto;
            flex: 0 0 auto;
            line-height: 40px;
            color: #606266;
          }

          input {
            width: 100%;
            height: 100%;
            line-height: 40px;
            font-size: 16px;
            flex: 1;
            background-color: transparent;
            border: 0;
          }
        }
        .register{
          text-align: center;
          font-size: 12px;
          color: #409EFF;
          cursor: pointer;
          user-select: none;
          &:hover{
            color:#3B78DD;
          }
        }
      }
    }
  }

  .cName {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translate(-50%, -50%);

    .gsName {
      color: #fff;
      font-size: 16px;
    }
  }
}