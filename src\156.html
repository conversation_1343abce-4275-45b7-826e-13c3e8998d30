<textarea onmousedown="rightEvent(this,event)" class="textareas_td" onchange="makeExpandingArea(this)" style="font-weight: 700;font-size: 28px;text-align: center;height: 32px;">软 件 服 务 协 议</textarea>
<table width="100%" class="name-box" border="1" cellspacing="0" cellpadding="0" style="border: 0">
    <tbody>
    <tr style="height: 25px;">
        <td width="100px;" style="border: 0" height="25px"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px;font-weight: 400;text-decoration: none solid rgb(103, 106, 108);">甲方：</textarea></td>
        <td style="border: 0" height="25px"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px;height: 29px;">{{contract.customerName}} </textarea></td>
        <td style="border: 0" width="100px;" height="25px"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="
font-size: 15px;
">乙方:</textarea></td>
        <td style="border: 0" height="25px"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="
font-size: 15px;
">{{salesUnit.companyName}}</textarea></td>
    </tr>
    </tbody></table>
<textarea onmousedown="rightEvent(this,event)" class="textareas" rows="1" onchange="makeExpandingArea(this)" style="height: 18px; font-size: 15px; margin-top: 0px; margin-bottom: 0px;">甲乙双方经友好协商就甲方"{{contractDetail.brandName}}”的服务达成以下协议,并由双方共同恪守。条款如下：</textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" style="font-weight: 700; font-size: 15px; height: 18px;" rows="1" onchange="makeExpandingArea(this)">一、为确保软件实施顺利进行、数据的安全及乙方维护时准确判断问题之所在，甲方应提供以下配合：</textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="height: 36px; font-size: 15px; margin-top: 0px; margin-bottom: 0px;">        1. 为{{contractDetail.brandName}}提供所需的运行环境包含硬件及系统软件，建议服务器电脑配置双硬盘且操作系统为：双硬盘且操作系统为：Windows SERVER 2008 R2或 Windows 7/10 32位或64位。
2、如需通过互联网远程访问则建议网络宽带为电信或联通，因部分第三方宽带运营商网络问题无法实现互联网访问。																						
3、做好服务器的管理工作，避免使用来历不明的光盘或U盘，以免感病毒。																						
4、在实施过程中,安排专人配合乙方工作。																						
</textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" style="font-weight: 700;font-size: 15px;height: 20px;" rows="1" onchange="makeExpandingArea(this)">二、乙方为甲方提供以下售后服务：</textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="height: 20px;font-size: 15px;">      1.运行维护明细</textarea>
<table width="100%" border="1" cellspacing="0" cellpadding="0" style="border-collapse: collapse;text-align: right">
    <tbody><tr style="height: 25px;">
        <th width="12%"></th>
        <th><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center" onchange="makeExpandingArea(this)">服务内容</textarea></th>
        <th width="17%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center" onchange="makeExpandingArea(this)">服务质量标准和规范</textarea></th>
        <th width="14%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center" onchange="makeExpandingArea(this)">服务价格</textarea></th>
    </tr>
    <tr height="80px;">
        <td width="12%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="height: 64px; font-size: 15px; margin: 0px; width: 89px;"> 热线咨询:
0591-87893728</textarea><br></td>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" style="text-align: left; height: 96px; font-size: 15px; margin: 0px; line-height: 24px; width: 447px;" onchange="makeExpandingArea(this)">1.通过服务热线诊断并解决日常使用过程中的各类问题。
2.每次电话问题请求保存到客服系统。
3.提交问题解决进度查询。
4.服务质量意见反馈。 </textarea></td>
        <td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="height: 90px; font-size: 15px; margin: 0px; width: 126px;">1.服务时间:工作日
2.响应时间:8工作小时
</textarea></td>
        <td width="14%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px;height: 39px;margin: 0px;width: 93px;">含在标准年服务费中</textarea></td>
    </tr>
    <tr>
        <td width="12%" rowspan="13"><textarea onmousedown="rightEvent(this,event)" onchange="editTextarea(this)" class="textareas_td" style="font-size: 15px;line-height: 26px;">远程运维</textarea></td>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; height: 48px; margin: 0px; line-height: 24px; width: 449px;" rows="1">1.每年提供5次业务单据调入时找不到数据（如出库调不到库存 
等）、查询数据等相关问题的处理。 </textarea></td>
        <td width="16%" rowspan="14"><textarea onmousedown="rightEvent(this,event)" onchange="editTextarea(this)" class="textareas" style="height: 133px; line-height: 26px; font-size: 15px; margin: 0px; width: 117px;">1.服务时间:工作日
2.响应时间:8工作小时
</textarea></td>
        <td width="14%" rowspan="14"><textarea onmousedown="rightEvent(this,event)" onchange="editTextarea(this)" class="textareas_td" style="font-size: 15px; line-height: 26px; margin: 0px; width: 112px; height: 66px;">含在标准年服务费中</textarea></td>
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 453px;">2.协助设置外网，如：路由器转发规则、花生壳、金万维开通等 </textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; line-height: 26px; margin: 0px; width: 444px; height: 26px;">3.设置服务器固定IP。 </textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; line-height: 26px; font-size: 15px; height: 52px; margin: 0px; width: 446px;">4.远程协助次数（次,如：数据备份检查、客户端安装）。 </textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 452px;">5.服务器配置选型（次）。 </textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 455px;">6.SQL数据库重新启动服务。  </textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="line-height: 26px; text-align: left; font-size: 15px; height: 26px; margin: 0px; width: 456px;">7.增加双硬盘备份路径、数据库硬盘备份。</textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; line-height: 26px; height: 52px; margin: 0px; width: 458px;">8.登录问题（次，如：内网登入，外网登入，设置固定IP，数据库未启动，中间层未启动）。</textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; line-height: 26px; height: 52px; margin: 0px; width: 450px;">9.操作错误修正 （次，如：业务单据调入时之前不到数据（例如销售合同做好了，库存有货为什么出不了等问题））。</textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 459px;">10.设置岗位过滤条件。</textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 456px;">11.SQL数据库重新启动服务（次） </textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 458px;">12.期初应收款、期初库存导入。</textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: left; font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 450px;">13.设置导出导入。</textarea></td>
        
    </tr>
    <tr>
        <td><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: center;font-size: 15px;height: 20px;">产品升级服务</textarea></td>
        <td width="16%"><textarea onmousedown="rightEvent(this,event)" onchange="editTextarea(this)" class="textareas" style="height: 44px; line-height: 22px; font-size: 15px; margin: 0px; width: 451px;">1、提供远程升级服务，确保安全成功升级。
2、提供软件大众功能免费升级。</textarea></td>
    </tr>
    <tr>
        <td rowspan="5"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="editTextarea(this)" style="text-align: center; font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 88px;">增值服务</textarea></td>
        <td width="16%"><textarea onmousedown="rightEvent(this,event)" onchange="editTextarea(this)" class="textareas" style="font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 453px;"> 数据迁移按次收费</textarea></td>


<td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px; line-height: 26px; text-align: center; height: 26px; margin: 0px; width: 132px;">按次</textarea></td><td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px; text-align: center; line-height: 26px; height: 26px; margin: 0px; width: 119px;">500/次</textarea></td>
    </tr>
<tr>
        
        <td width="16%"><textarea onmousedown="rightEvent(this,event)" onchange="editTextarea(this)" class="textareas" style="font-size: 15px; line-height: 26px; height: 26px; margin-left: 0px; margin-right: 0px; width: 457px;">二次软件培训</textarea></td>


<td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px;line-height: 26px;text-align: center;height: 26px;">按次</textarea></td><td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px;text-align: center;line-height: 26px;height: 26px;">500/次</textarea></td>
    </tr>
    
    
    <tr>
        
        <td width="16%"><textarea onmousedown="rightEvent(this,event)" onchange="editTextarea(this)" class="textareas" style="font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 456px;">二次设计报表</textarea></td>


<td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px; line-height: 26px; text-align: center; height: 26px; margin: 0px; width: 145px;">按次</textarea></td><td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px; text-align: center; line-height: 26px; height: 26px; margin-top: 0px; margin-bottom: 0px;">50/个</textarea></td>
    </tr>
<tr>
        
        <td width="16%"><textarea onmousedown="rightEvent(this,event)" onchange="editTextarea(this)" class="textareas" style="font-size: 15px; line-height: 26px; height: 26px; margin: 0px; width: 460px;"> 数据库质疑、修复数据（可修复情况下）</textarea></td>


<td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px; line-height: 26px; text-align: center; height: 26px; margin-top: 0px; margin-bottom: 0px;">按次</textarea></td><td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px; text-align: center; line-height: 26px; height: 26px; margin-top: 0px; margin-bottom: 0px;">100/次</textarea></td>
    </tr>
<tr>
        
        <td width="16%"><textarea onmousedown="rightEvent(this,event)" onchange="editTextarea(this)" class="textareas" style="font-size: 15px; line-height: 26px; height: 26px; margin-top: 0px; margin-bottom: 0px;"> 前后台配置</textarea></td>


<td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px; line-height: 26px; text-align: center; height: 26px; margin: 0px; width: 141px;">按次</textarea></td><td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="font-size: 15px; text-align: center; line-height: 26px; height: 26px; margin: 0px; width: 123px;">50/个</textarea></td>
    </tr>
    
    
        
    
    
    
    </tbody></table>
<textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)" style="height: 104px; font-size: 15px; line-height: 26px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108); margin: 0px; width: 850px;"> 2、维护费用
(1)乙方对系统提供终身技术支持，维护费金额{{contract.contractAmount}}元,收到服务费后提供相应的增值功能，（航天开票接口、合同改价单、新阳工控通、产品面价更新等)
(2)维护时间：按次 </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; height: 18px; margin: 0px; width: 841px;">付款款资料如下:</textarea>
<table width="100%" border="1" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
    <tbody><tr style="height: 25px;">
        <td width="100px;" height="25px"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; margin: 0px; height: 20px; width: 99px;">开 户 名：</textarea></td>
        <td colspan="3" height="25px"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; height: 20px; margin: 0px; width: 714px;">{{salesUnit.companyName}} </textarea></td>
        <td style="display: none;width:100px;height: 25px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_td_little" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
        <td style="display: none;width:100px;height: 25px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_td_little" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
    </tr>
    <tr style="height: 25px;">
        <td width="100px;" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; margin-top: 0px; margin-bottom: 0px; height: 22px;">开 户 行：</textarea></td>
        <td colspan="3" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; height: 19px; margin: 0px; width: 708px;">{{salesUnit.openingBank}} </textarea></td>
        <td style="display: none;width:100px;height: 25px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_td_little" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
        <td style="display: none;width:100px;height: 25px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_td_little" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
    </tr>
    <tr style="height: 25px;">
        <td width="100px;" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; height: 20px; margin-top: 0px; margin-bottom: 0px;">帐    户：</textarea></td>
        <td colspan="3" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; height: 20px; margin: 0px; width: 713px;">{{salesUnit.bankAccount}} </textarea></td>
        <td style="display: none;width:100px;height: 25px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_td_little" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
        <td style="display: none;width:100px;height: 25px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_td_little" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
    </tr>
    </tbody></table>
<table width="100%" border="1" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
    <tbody><tr style="height: 25px;">
        <td width="100px;" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; margin: 0px; width: 91px; height: 21px;">甲方盖章：</textarea></td>
        <td height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; margin-left: 0px; margin-right: 0px; width: 223px;">{{contract.customerName}}</textarea></td>
        <td width="100px;" height="10px;" style="
font-size: 15px;
"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; margin: 0px; height: 19px; width: 97px;">乙方盖章：</textarea></td>
        <td height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="height: 21px; font-size: 15px; margin: 0px; width: 358px;">{{salesUnit.companyName}} </textarea></td>
    </tr>
    <tr style="height: 25px;">
        <td width="100px;" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; margin: 0px; width: 95px; height: 21px;">代 理 人：</textarea></td>
        <td height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; margin: 0px; width: 214px; height: 20px;">{{contract.linkMan}}</textarea></td>
        <td width="100px;" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; margin: 0px; height: 20px; width: 97px;">代 理 人：</textarea></td>
        <td height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; height: 22px; margin: 0px; width: 362px;">{{contract.operatorEmployeeName}} </textarea></td>
    </tr>
    <tr style="height: 25px;">
        <td width="100px;" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; height: 20px; margin: 0px; width: 91px;">时    间：</textarea></td>
        <td height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; margin-left: 0px; margin-right: 0px; width: 208px;">{{contract.newTime}}</textarea></td>
        <td width="100px;" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; height: 18px; margin: 0px; width: 97px;">时    间：</textarea></td>
        <td height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="height: 24px; font-size: 15px; margin: 0px; width: 360px;">{{contract.newTime}} </textarea></td>
    </tr>
    <tr style="height: 25px;">
        <td width="100px;" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="height: 18px; font-size: 15px; margin: 0px; width: 91px;">传    真：</textarea></td>
        <td height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; margin: 0px; width: 204px; height: 21px;">{{contract.fax}}</textarea></td>
        <td width="100px;" height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="font-size: 15px; height: 18px; margin: 0px; width: 97px;">传    真：</textarea></td>
        <td height="10px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center textareas_td_little" rows="1" onchange="makeExpandingArea(this)" style="height: 21px; font-size: 15px; margin: 0px; width: 366px;">{{salesUnit.fax}} </textarea></td>
    </tr>
    </tbody></table>