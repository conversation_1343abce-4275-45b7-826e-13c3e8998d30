export default [{
    path: 'contractManage',
    name: 'contractManage',
    title: '合同管理',
    icon: 'home',
    show: true,
    meta: {
      keepAlive: false
    },
    component: resolve =>
      require([
        "@/views/internalSystem/backstage/components/view/view.vue"
      ], resolve),
    children: [{
        path: 'authorizationCode',
        name: 'authorizationCode',
        title: '授权码合同',
        meta: {
          title: "授权码合同"
        },
        component: () => import('@/views/internalSystem/backstage/contractManage/authorizationCode/index.vue')
      },
      {
        path: 'implementation',
        name: 'implementation',
        title: '实施合同',
        meta: {
          title: "实施合同"
        },
        component: () => import('@/views/internalSystem/backstage/contractManage/implementation/index.vue')
      },
      {
        path: 'train',
        name: 'train',
        title: '培训合同',
        meta: {
          title: "培训合同"
        },
        component: () => import('@/views/internalSystem/backstage/contractManage/train/index.vue')
      },
      {
        path: 'dueSoon',
        name: 'dueSoon',
        title: '快到期合同',
        meta: {
          title: "快到期合同"
        },
        component: () => import('@/views/internalSystem/backstage/contractManage/dueSoon/index.vue')
      },
      {
        path: 'overdue',
        name: 'overdue',
        title: '过期合同',
        meta: {
          title: "过期合同"
        },
        component: () => import('@/views/internalSystem/backstage/contractManage/overdue/index.vue')
      },
      
    ]
  }]