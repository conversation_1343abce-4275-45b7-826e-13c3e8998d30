<template>
  <div class="body-p10" style="overflow-y:auto;overflow-x: hidden;" v-if="dialogVisible">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button v-if="(isEdit&&ruleForm.sales_visiting_id)||!ruleForm.sales_visiting_id" type="primary"
        @click="submitForm('ruleForm')" :loading="loading">保 存</el-button>
    </div>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="136px" class="mt10">
      <span class="visitingTitle">一、客户基础信息</span>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input :disabled="!isEdit" v-model="ruleForm.customer_name" placeholder="请点击选择客户" @focus="chooseCustomer"
              readonly clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="销售员">
            <el-input v-model="ruleForm.fk_sale_employee_name" disabled clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="回访方式" prop="visiting_form">
            <el-select :disabled="!isEdit" v-model="ruleForm.visiting_form" placeholder="请选择回访方式" filterable clearable>
              <el-option v-for="item in sales_visiting_form" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系人" prop="link_man">
            <el-input :disabled="!isEdit" v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系时间" prop="contact_time">
            <my-date :disabled="!isEdit" v-model="ruleForm.contact_time" :isAllDate="false" hint="请选择联系时间"></my-date>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="QQ号码" prop="qq">
            <el-input :disabled="!isEdit" v-model="ruleForm.qq"  clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input :disabled="!isEdit" v-model="ruleForm.phone"  clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="微信号码" prop="we_chat">
            <el-input :disabled="!isEdit" v-model="ruleForm.we_chat" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <span class="visitingTitle">二、系统运行情况</span>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="数据备份是否完整" prop="dateBackupState">
            <el-select :disabled="!isEdit" v-model="ruleForm.dateBackupState" placeholder="请选择数据备份是否完整" filterable
              clearable>
              <el-option v-for="item in confirmNormal" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="是否续交维护费" prop="isHandMaintenance">
            <el-select :disabled="!isEdit" v-model="ruleForm.isHandMaintenance" placeholder="请选择是否续交维护费" filterable
              clearable>
              <el-option v-for="item in whether" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="应收数据是否准确" prop="receiveDataState">
            <el-select :disabled="!isEdit" v-model="ruleForm.receiveDataState" placeholder="请选择应收数据是否准确" filterable
              clearable>
              <el-option v-for="item in accurate" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="库存是否准确" prop="inventoryState">
            <el-select :disabled="!isEdit" v-model="ruleForm.inventoryState" placeholder="请选择库存是否准确" filterable
              clearable>
              <el-option v-for="item in accurate" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="应付数据是否准确" prop="payDataState">
            <el-select :disabled="!isEdit" v-model="ruleForm.payDataState" placeholder="请选择应付数据是否准确" filterable
              clearable>
              <el-option v-for="item in accurate" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="航天开票接口" prop="invoicePortState">
            <el-select :disabled="!isEdit" v-model="ruleForm.invoicePortState" placeholder="请选择航天开票接口" filterable
              clearable>
              <el-option v-for="item in normal" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <span class="visitingTitle">三、本次跟进内容</span>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="16">
          <el-form-item label="" prop="follow_content">
            <el-input :disabled="!isEdit" type="textarea" placeholder="请输入跟进内容" v-model="ruleForm.follow_content"
              clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <span class="visitingTitle">四、客户现有功能模块</span>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="16">
          <el-form-item label="" prop="use_describe">
            <el-checkbox-group :disabled="!isEdit" v-model="checkSoftware">
              <el-checkbox v-for="item in softwareList" :key="item.value" :label="item.value" name="type">{{item.name}}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <span class="visitingTitle">五、客户提的需求/bug</span>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="16">
          <el-form-item label="" prop="customer_demand">
            <el-input :disabled="!isEdit" type="textarea" placeholder="请输入客户提的需求/bug" v-model="ruleForm.customer_demand"
              clearable>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <customer-list ref="customerList" @getInfo="getInfo" />
  </div>
</template>
<script src="./index.js">

</script>

<style lang="scss" scoped>
  .visitingTitle {
    font-size: 18px;
    font-weight: bold;
    margin-top: 20px;
  }
</style>