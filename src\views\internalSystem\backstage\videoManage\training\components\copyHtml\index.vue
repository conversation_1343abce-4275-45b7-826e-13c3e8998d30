<template>
  <div id="app">
    <div v-if="!showDetail">
      <div style="height: 600px">
        {{ "noticeList === " }}
        <br />

        <div v-for="(item, index) in noticeList" :key="index">
          <div v-if="item.type === 1" style="margin-top: 10px">
            {{ item }}

            <hr />
          </div>

          <div v-else style="margin-top: 10px">
            {{ item }}
            <br />
            <el-button class="btn" @click="next('02', index - 1)"
              >换一波{{
                "当前关键词：" + noticeList[index - 1]["title"] + "    " + 
                "当前页：" + noticeList[index - 1]["pageNum"]+ "    " + 
                "总记录数：" + noticeList[index - 1]["totalCount"]

              }}</el-button
            >
            <hr />
          </div>
        </div>
        <hr />

        <hr />
      </div>
      <!-- 输入框 -->
      <div>
        <el-input
          class="input-notice"
          v-model="keywords"
          placeholder="请输入关键词"
        />
        <el-button class="btn" @click="onSubmit('01')">发送</el-button>
      </div>
    </div>
    <div v-else>
      <div class="header">
        <div class="title">详情</div>
        <div class="iconfont icon-back" @click="onBack"></div>
      </div>
      <div class="detail" v-html="detail"></div>
    </div>
  </div>
</template>

<script src="./index.js">
</script>

<style lang="scss" scoped>
#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #efefee;
}

.content {
  flex: 1;
  overflow: scroll;
}

.content-item {
  margin-bottom: 0.44rem;
}

.content-empty {
  font-size: 0.28rem;
  color: #999;
  text-align: center;
}

.content-item-time {
  padding: 0.32rem 0;
  font-size: 0.28rem;
  color: #999;
  text-align: center;
}

.content-item-notice {
  display: flex;
  align-items: flex-start;
}

.content-item-notice-right {
  justify-content: flex-end;
}

.content-item-notice .text {
  display: flex;
  align-items: center;
  min-height: 0.8rem;
  max-width: 5rem;
  line-height: 0.48rem;
  font-size: 0.32rem;
  margin: 0 0.16rem;
  padding: 0.1rem 0.25rem;
  background-color: #fff;
  border-radius: 0.16rem;
  box-sizing: border-box;
  white-space: normal;
  word-break: break-all;
}

.content-item-notice-right .text {
  background-color: #c5ebfe;
}

.tip {
  min-width: 3rem;
  max-width: 5rem;
  padding: 0.3rem 0.25rem;
  font-size: 0.28rem;
  list-style: none;
  background-color: greenyellow;
  border-radius: 0.16rem;
  margin-left: 0.1rem;
}

.tip-list {
  margin: 0;
  padding: 0;
}

.tip-item {
  border-bottom: 1px solid #ccc;
  padding: 0.2rem 0;
  line-height: 0.4rem;
  list-style: none;
  color: #999;
}

/* 底部输入框 */
.input {
  display: flex;
  align-items: center;
  height: 1rem;
  padding: 0 0.4rem;

  background-color: #fff;
  /* border-radius: 5rem; */
}

.input-notice {
  flex: 1;
  font-size: 0.32rem;
  line-height: 1rem;
  outline: none;
  border: none;
  border-radius: 5rem;
}

.input-notice::-webkit-input-placeholder {
  color: #ccc;
}

input::-moz-input-placeholder {
  color: #ccc;
}

input::-ms-input-placeholder {
  color: #ccc;
}

.icon-smile,
.icon-add {
  font-size: 0.6rem;
  color: #999;
}

.icon-smile {
  margin-right: 0.5rem;
}

.btn {
  font-size: 0.4rem;
  border-radius: 0.1rem;
  background-color: rgb(255, 255, 255);
}

/* 详情 */
.header {
  /* position: relative; */
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 1rem;
  width: 100%;
  background-color: #fff;
  font-size: 0.5rem;
}

.detail {
  height: 30px;
  /* display: flex; */
  padding: 0.3rem;
  font-size: 0.32rem;
}

.icon-back {
  position: absolute;
  left: 0.2rem;
  font-size: 0.4rem;
}

.icon-back2 {
  position: absolute;
  left: 0rem;
  font-size: 0rem;
}

@font-face {
  font-family: "iconfont";
  /* Project id 2816575 */
  src: url("//at.alicdn.com/t/font_2816575_o27ozlqrcs.woff2?t=1631780648397")
      format("woff2"),
    url("//at.alicdn.com/t/font_2816575_o27ozlqrcs.woff?t=1631780648397")
      format("woff"),
    url("//at.alicdn.com/t/font_2816575_o27ozlqrcs.ttf?t=1631780648397")
      format("truetype");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-back:before {
  content: "\e602";
}

.icon-smile:before {
  content: "\e737";
}

.icon-add:before {
  content: "\e633";
}

.tip-bottom {
  text-align: center;
}
</style>