import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}invoice/query`, params)
  },
  // 查询
  queryAll: params => {
    return Axios.post(`${environment.internalSystemAPI}invoice/queryAll`, params)
  },
  // 查询明细
  detailList: params => {
    return Axios.post(`${environment.internalSystemAPI}invoice/detailList`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}invoice/add`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}invoice/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}invoice/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}invoice/getInfo`, params)
  },
  // 审核
  updateAudit: params => {
    return Axios.post(`${environment.internalSystemAPI}invoice/updateAudit`, params)
  }
};