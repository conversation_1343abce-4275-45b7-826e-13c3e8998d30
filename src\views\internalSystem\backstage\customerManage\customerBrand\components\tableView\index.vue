<template>
  <div style="flex: 1">
    <vxe-table
      :id="tableId"
      column-key
      border
      resizable
      show-overflow
      show-header-overflow
      highlight-hover-row
      highlight-current-row
      auto-resize
      ref="xTable"
      height="100%"
      :loading="loading"
      :default-expand-all="true"
      align="center"
      :checkbox-config="{
        reserve: true,
        showHeader: true,
        trigger: 'row',
      }"
      :custom-config="{
        storage: {
          visible: true,
        },
      }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        autoClear: false, //是否立即释放焦点
        showStatus: false,
      }"
      :mouse-config="{ selected: true }"
      :edit-rules="editRules"
      :data="tableData"
      :expand-config="{
        expandAll: true,
      }"
      :keyboard-config="{
        isArrow: true,
        isDel: true,
        isEnter: true,
        isTab: true,

        isChecked: true,
        enterToTab: true,
      }"
      @keydown="keyDownMethods"
      :context-menu="{ header: { options: headerMenus } }"
      @context-menu-click="contextMenuClickEvent"
    >
      <template>
        <vxe-table-column
          title="产品服务"
          field="fk_brand_id"
          width="300"
          :edit-render="{ name: 'input', enabled: true }"
        >
          <template slot-scope="scope">
            <div>
              {{ getSelectShowData(proList, scope.row.fk_brand_id) }}
            </div>
          </template>
          <template v-slot:edit="scope">
            <el-select
              v-model="scope.row.fk_brand_id"
              filterable
              clearable
              :disabled="!!scope.row.customer_brand_id"
              @change="changeSelect(proList, scope.row.fk_brand_id, scope.row)"
            >
              <el-option
                v-for="(item2, index) in proList"
                :key="index"
                :label="item2.label"
                :value="item2.value"
              >
              </el-option>
            </el-select>
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column
          title="原销售类型"
          field="detail_sell_type"
          width="150"
        >
          <template slot-scope="scope">
            <div>
              {{ getSelectShowData(sell_type, scope.row.detail_sell_type_old) }}
            </div>
          </template>
          <template v-slot:edit="scope">
            <el-select
              v-model="scope.row.detail_sell_type_old"
              filterable
              clearable
              @change="changeSelect2(scope.row.detail_sell_type_old, scope.row)"
            >
              <el-option
                v-for="(item2, index) in sell_type"
                :key="index"
                :label="item2.label"
                :value="item2.id"
              >
                {{ item2.label }}
              </el-option>
            </el-select>
          </template>
        </vxe-table-column> -->
        <!-- <vxe-table-column
          title="新销售类型"
          field="detail_sell_type_new"
          width="150"
          :edit-render="{ name: 'input', enabled: true }"
        >
          <template slot-scope="scope">
            <div>
              {{ getSelectShowData(sell_type, scope.row.detail_sell_type_new) }}
            </div>
          </template>
          <template v-slot:edit="scope">
            <el-select
              v-model="scope.row.detail_sell_type_new"
              filterable
              clearable
              @change="changeSelect2(scope.row.detail_sell_type_new, scope.row)"
            >
              <el-option
                v-for="(item2, index) in sell_type"
                :key="index"
                :label="item2.label"
                :value="item2.id"
              >
                {{ item2.label }}
              </el-option>
            </el-select>
          </template>
        </vxe-table-column> -->
        <vxe-table-column
          title="原合同金额"
          field="contract_amount_old"
          width="120"
        >
          <template v-slot:edit="scope">
            <el-input v-model="scope.row.contract_amount_old"> </el-input>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新合同金额"
          field="contract_amount_new"
          width="120"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template v-slot:edit="scope">
            <el-input
              v-model="scope.row.contract_amount_new"
              @blur="changeAmount(scope.row)"
              @focus="selectValue($event)"
            >
            </el-input>
          </template>
        </vxe-table-column>

        <vxe-table-column
          title="原端口数量"
          field="original_port_count_old"
          width="90"
        >
          <template v-slot:edit="scope">
            <el-input v-model="scope.row.original_port_count_old"> </el-input>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新端口数量"
          field="original_port_count_new"
          :width="110"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template v-slot:edit="scope">
            <el-input
              v-model="scope.row.original_port_count_new"
              @focus="selectValue($event)"
            >
            </el-input>
          </template>
        </vxe-table-column>

        <vxe-table-column
          title="原年维护费比例"
          field="year_maintain_cost_old"
          width="120"
        >
          <template slot-scope="scope">
            <div>
              {{
                scope.row.year_maintain_cost_old
                  ? getSelectShowData(
                      yearsFeeList,
                      scope.row.year_maintain_cost_old
                    ) + "%"
                  : ""
              }}
            </div>
          </template>
          <template v-slot:edit="scope">
            <el-select
              v-model="scope.row.year_maintain_cost_old"
              filterable
              clearable

            >
              <el-option
                v-for="(item2, index) in yearsFeeList"
                :key="index"
                :label="item2.label + '%'"
                :value="item2.value"
              >
              </el-option>
            </el-select>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新年维护费比例"
          field="year_maintain_cost_new"
          width="130"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template slot-scope="scope">
            <div>
              {{
                scope.row.year_maintain_cost_new
                  ? getSelectShowData(
                      yearsFeeList,
                      scope.row.year_maintain_cost_new
                    ) + "%"
                  : ""
              }}
            </div>
          </template>
          <template v-slot:edit="scope">
            <el-select
              v-model="scope.row.year_maintain_cost_new"
              filterable
              clearable
              @change="
                changeYearMaintailCost(scope.row.year_maintain_cost_new, scope.row)
              "
            >
              <el-option
                v-for="(item2, index) in yearsFeeList"
                :key="index"
                :label="item2.label + '%'"
                :value="item2.value"
              >
              </el-option>
            </el-select>
          </template>
        </vxe-table-column>

        <vxe-table-column
          title="原维护费"
          field="maintenance_fee_old"
          width="90"
        >
          <template v-slot:edit="scope">
            <el-input v-model="scope.row.maintenance_fee_old"> </el-input>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新维护费"
          field="maintenance_fee_new"
          width="100"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template v-slot:edit="scope">
            <el-input
              v-model="scope.row.maintenance_fee_new"
              @focus="selectValue($event)"
            >
            </el-input>
          </template>
        </vxe-table-column>

        <vxe-table-column
          title="原维护起始时间"
          field="maintain_start_time_old"
          width="120"
        >
          <template v-slot:edit="scope">
            <my-date
              v-model="scope.row.maintain_start_time_old"
              :isAllDate="true"
              hint="请选择原维护起始时间"
            >
            </my-date>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新维护起始时间"
          field="maintain_start_time_new"
          width="130"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template v-slot:edit="scope">
            <my-date
              v-model="scope.row.maintain_start_time_new"
              :isAllDate="true"
              hint="请选择新维护起始时间"
            >
            </my-date>
          </template>
        </vxe-table-column>

        <vxe-table-column
          title="原维护结束时间"
          field="new_maintain_stop_time_old"
          width="120"
        >
          <template v-slot:edit="scope">
            <my-date
              v-model="scope.row.new_maintain_stop_time_old"
              :isAllDate="true"
              hint="请选择原维护结束时间"
            >
            </my-date>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新维护结束时间"
          field="new_maintain_stop_time_new"
          width="130"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template v-slot:edit="scope">
            <my-date
              v-model="scope.row.new_maintain_stop_time_new"
              :isAllDate="true"
              hint="请选择新维护结束时间"
            >
            </my-date>
          </template>
        </vxe-table-column>

        <vxe-table-column
          title="原软件序列号"
          field="software_no_old"
          width="150"
        >
          <template v-slot:edit="scope">
            <el-input v-model="scope.row.software_no_old"> </el-input>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新软件序列号"
          field="software_no_new"
          width="150"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template v-slot:edit="scope">
            <el-input
              v-model="scope.row.software_no_new"
              @focus="selectValue($event)"
            >
            </el-input>
          </template>
        </vxe-table-column>
        <vxe-table-column title="原产品备注" field="remark_old" width="150">
          <template v-slot:edit="scope">
            <el-input v-model="scope.row.remark_old"> </el-input>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新产品备注"
          field="remark_new"
          width="150"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template v-slot:edit="scope">
            <el-input
              v-model="scope.row.remark_new"
              @focus="selectValue($event)"
            >
            </el-input>
          </template>
        </vxe-table-column>
      </template>

      <vxe-table-column
        fixed="right"
        title="操作"
        :width="handleWidth"
        v-if="isDel && isOperation"
      >
        <template slot-scope="scope">
          <el-button
            @click="del(scope.row, scope.index)"
            type="text"
            size="small"
            class="danger ml30"
            >删除</el-button
          >
        </template>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>
<script src="./index.js"></script>
<style scoped lang="scss">
.danger {
  color: #f56c6c;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
@import "@/assets/css/element/font-color.scss";
</style>
