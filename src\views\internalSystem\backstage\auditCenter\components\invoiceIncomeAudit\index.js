import API from '@/api/internalSystem/financialManage/invoiceIncome'
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  getOptions,
} from "@/common/internalSystem/common.js"
import { mapGetters } from 'vuex';
export default {
  name: "invoiceIncomeAudit",
  components: {
    MyDate
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        invoice_unit: "",
        invoice_detail: "",
        invoice_number: "",
        invoice_money: "",
        invoice_time: ""
      },
      loading: false,
      dialogVisibleAudit: false,
      auditForm: {
        auditState:1,
        auditRemark: ""
      },
      rules: {
        auditState: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }]
      },
      auditStateList: []
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.auditStateList = getOptions('t_invoice_income', 'audit_state');
      this.ruleForm = data;
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
    },
    save() {
      let params = this.auditForm;
      params.invoice_income_id = this.ruleForm.invoice_income_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        invoice_unit: "",
        invoice_detail: "",
        invoice_number: "",
        invoice_money: "",
        invoice_time: ""
      }
    }
  },
  computed: {
    ...mapGetters(["contract_auditStateList"])
  },
};