import API from '@/api/internalSystem/customerManage/customerInfo'
import {mapGetters} from 'vuex'
export default {
  name: "updateAudit",
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_id: "",
        customer_no: "",
        fk_sale_employee_id: "",
        link_man: "",
        fk_sale_employee_id_number: "",
        customer_name: "",
        phone: "",
        fk_sale_employee_id_name: "",
        customer_synopsis: "",
        telephone: "",
        department_name: "",
        customer_legal_name: "",
        email: "",
        company_site: "",
        customer_legal_person: "",
        qq: "",
        personnel_scale_low: "",
        personnel_scale_high: "",
        customer_type: "",
        fax: "",
        company_scale_low: "",
        company_scale_high: "",
        belong_industry: "",
        postal_code: "",
        province_name: "",
        customer_source: "",
        important_rank: "",
        city_name: "",
        introducer: "",
        introducer_name: "",
        link_address: "",
        customer_stage: ""
      },
      financeForm: {
        opening_bank: "",
        customer_account: "",
        customer_tax_number: "",
        open_ticket_address: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: ""
      },
      loading: false,
      checked: true,
      dialogVisibleAudit: false,
      auditForm: {
        auditState: 1,
        auditRemark: ""
      },
      rules: {
        auditState: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }]
      },
      auditStateList: []
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      if (data) {
        this.getCustomerInfo(data);
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.resetForm('financeForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.auditForm;
      params.customer_temp_id = this.ruleForm.customer_temp_id;
      this.loading = true;
      API.updateCheckAgree(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    getCustomerInfo(data = {}) {
      this.ruleForm = data;
      if (!data.customer_finance_info_temp_id) {
        this.checked = false;
      } else {
        this.checked = true;
        this.financeForm.opening_bank = data.opening_bank;
        this.financeForm.customer_account = data.customer_account;
        this.financeForm.customer_tax_number = data.customer_tax_number;
        this.financeForm.open_ticket_address = data.open_ticket_address;
        this.financeForm.open_ticket_phone = data.open_ticket_phone;
        this.financeForm.receive_ticket_address = data.receive_ticket_address;
        this.financeForm.receive_ticket_person = data.receive_ticket_person;
        this.financeForm.receive_ticket_phone = data.receive_ticket_phone;
      }
      if (data.personnel_scale) {
        this.ruleForm.personnel_scale_low = data.personnel_scale.split("-")[0];
        this.ruleForm.personnel_scale_high = data.personnel_scale.split("-")[1];
      }
      if (data.company_scale) {
        this.ruleForm.company_scale_low = data.company_scale.split("-")[0];
        this.ruleForm.company_scale_high = data.company_scale.split("-")[1];
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_id: "",
        customer_no: "",
        fk_sale_employee_id: "",
        link_man: "",
        fk_sale_employee_id_number: "",
        customer_name: "",
        phone: "",
        fk_sale_employee_id_name: "",
        customer_synopsis: "",
        telephone: "",
        department_name: "",
        customer_legal_name: "",
        email: "",
        company_site: "",
        customer_legal_person: "",
        qq: "",
        personnel_scale_low: "",
        personnel_scale_high: "",
        customer_type: "",
        fax: "",
        company_scale_low: "",
        company_scale_high: "",
        belong_industry: "",
        postal_code: "",
        province_name: "",
        customer_source: "",
        important_rank: "",
        city_name: "",
        introducer: "",
        introducer_name: "",
        link_address: "",
        customer_stage: ""
      }
      this.financeForm = {
        opening_bank: "",
        customer_account: "",
        customer_tax_number: "",
        open_ticket_address: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: ""
      }
    }
  },
  computed: {
    ...mapGetters([
      'params_constant_customer_stage',
      'params_constant_customer_type',
      'params_constant_belong_industry',
      'params_constant_customer_source',
      'params_constant_important_rank',
      'contract_auditStateList'
    ]),
    fkSaleEmployeeUserInfo() {

      let name = ``
      if(this.ruleForm.fk_sale_employee_id_name && 
      this.ruleForm.fk_sale_employee_id_number && 
       this.ruleForm.department_name ){
         name =  this.ruleForm.fk_sale_employee_id_name + '-' +
        this.ruleForm.fk_sale_employee_id_number + '-' + 
         this.ruleForm.department_name 
      }
    
       return name
      }
  },
};