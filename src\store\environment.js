import Cookies from "js-cookie";
import {
  forEachRouterPath
} from "@/utils/router.js";


import internalSystemRouterList from "@/router/internalSystem/backstage/index.js";
// 设置平台环境参数
const state = {
  userInfo: {}, // 用户信息
  fieldPermissions: [], // 当前页面的字段权限
  buttonPermissions: [], // 当前页面的按钮权限
  pathPermissions: [], //当前页面的可跳转菜单栏权限
  asideMenuConfig: [], //控制台侧边栏显示数据，即菜单栏显示数据
  region: `oss-cn-beijing`,
  accessKeyId: "LTAI4Fsre7DUpitEiRzoSjEA",
  accessKeySecret: "******************************",
  bucket: "trade-erp",
  defaultConfig: {
    internalSystem: {
      cookiesKey: "internalSystem_token",
      userLoginInfoKey: "internalSystem_login_info",
      systemId: 8,
      tabsKey: "internalSystem_tabs",
      routerList: internalSystemRouterList
    },
  },
  env: "basic", // 当前系统环境
  menuTemp: {
    pathPermissions: [],
    buttonPermissions: [],
    fieldPermissions: []
  } //临时变量，用来存递归得到的可跳转菜单
};
const getters = {
  bucket: state => state.defaultConfig[state.env].bucket || "trade-erp",
  accessKeySecret: state => state.accessKeySecret,
  accessKeyId: state => state.accessKeyId,
  menuTemp: state => state.menuTemp,
  fieldPermissions: state => state.fieldPermissions,
  buttonPermissions: state => state.buttonPermissions,
  pathPermissions: state => state.pathPermissions,
  asideMenuConfig: state => state.asideMenuConfig,
  userInfo: state => state.userInfo, // 用户信息
  cookiesUserInfo: () => JSON.parse(Cookies.get("cookiesUserInfo")), // 从cookies取
  env: state => state.env, // 系统环境
  token: state => {
    // token
    let token = Cookies.get(state.defaultConfig[state.env].cookiesKey);
    return token;
  },
  defaultConfig: state => {
    // 默认系统环境配置
    return state.defaultConfig[state.env] || state.defaultConfig["basic"];
  },
  userLoginInfo: state => {
    // 用户登录信息
    let defaultConfig = state.defaultConfig[state.env];

    return JSON.parse(localStorage.getItem(defaultConfig.userLoginInfoKey));
  },
  tabsList: state => {
    // 获取tabs
    let defaultConfig = state.defaultConfig[state.env];
    let tabs = sessionStorage.getItem(defaultConfig.tabsKey);
    return JSON.parse(tabs) || [];
  },
  tabsUpdateFlag: state => {
    // 判断tabs是否被更改
    let defaultConfig = state.defaultConfig[state.env];
    let tabs = sessionStorage.getItem(defaultConfig.tabsKey);
    return JSON.parse(tabs) || [];
  },
  routerList: state => {
    // 获取路由列表
    let defaultConfig = state.defaultConfig[state.env];
    return defaultConfig.routerList;
  }
};
const mutations = {
  SET_FIELDPERMISSIONS(state, permissions = []) {
    //保存当前页面的字段权限
    state.fieldPermissions = permissions;
  },
  SET_BUTTONPERMISSIONS(state, permissions = []) {
    state.buttonPermissions = permissions;
  },
  SET_PATHPERMISSIONS(state, permissions = []) {
    state.pathPermissions = permissions;
  },
  SET_ASIDEMENUCONFIG(state, asideMenuConfig = []) {
    state.asideMenuConfig = asideMenuConfig;
  },
  SET_ROUTERLIST(state, routerList = []) {
    // 保存路由
    state.defaultConfig[state.env].routerList = routerList;
  },
  SET_TABS(state, tabs = []) {
    // 保存tabs信息
    let defaultConfig = state.defaultConfig[state.env];
    sessionStorage.setItem(defaultConfig.tabsKey, JSON.stringify(tabs));
  },
  SET_USER_LOGIN_INFO(state, parameter = null) {
    // 保存用户登录信息
    if (!parameter) return;
    let userLoginInfo = JSON.stringify(parameter);
    let defaultConfig = state.defaultConfig[state.env];


    localStorage.removeItem(defaultConfig.userLoginInfoKey);
    localStorage.setItem(defaultConfig.userLoginInfoKey, userLoginInfo);

  },
  REMOVE_USER_LOGIN_INFO(state) {
    // 删除记住的用户账号，密码
    let defaultConfig = state.defaultConfig[state.env];
    localStorage.removeItem(defaultConfig.userLoginInfoKey);
  },
  SET_USERINFO(state, data = null) {
    // 保存用户信息
    if (!data) return;
    state.userInfo = data;
    Cookies.set("cookiesUserInfo", data, {
      expires: 0.5
    });
  },
  SET_ENV(state, env = "basic_token") {
    // 设置系统环境
    // console.log(`当前系统环境:${env}`);
    state.env = env;
    state.bucket = state.defaultConfig[env].bucket || "trade-erp";
  },
  SET_TOKEN(state, token = "") {
    // 保存token
    let defaultConfig = state.defaultConfig[state.env];
    Cookies.set(defaultConfig.cookiesKey, token, {
      expires: 0.5
    });
  },
  REMOVE_TOKEN(state) {
    // 删除token
    let defaultConfig = state.defaultConfig[state.env];
    Cookies.remove(defaultConfig.cookiesKey);

    let token = Cookies.get(state.defaultConfig[state.env].cookiesKey);
    setTimeout(() => {
      let url = `/#/login`;
      window.location.href = url;
    }, 500)
  },
  SET_MENUTEMP(
    state,
    data = {
      pathPermissions: [],
      buttonPermissions: [],
      fieldPermissions: []
    }
  ) {
    state.menuTemp = data;
  },
  JUDGEPATH(state, menuList) {
    //把可跳转路由保存到store
    this.commit("SET_PATHPERMISSIONS", []);
    this.commit("SET_MENUTEMP", );
    this.commit("GETLISTPATH", menuList);
    this.commit("SET_PATHPERMISSIONS", state.menuTemp.pathPermissions);
    this.commit("SET_FIELDPERMISSIONS", state.menuTemp.fieldPermissions);
    this.commit("SET_BUTTONPERMISSIONS", state.menuTemp.buttonPermissions);
    this.commit("SET_MENUTEMP", );
  },
  GETLISTPATH(state, data) {
    //同步遍历获取可跳转路由
    data.forEach(item => {
      switch (item.menuType) {
        case 4:
        case 1:
          if (item.path) state.menuTemp.pathPermissions.push(item.path)
          break
        case 2:
          if (item.buttonJump) state.menuTemp.pathPermissions.push(item.buttonJump);
          if (item.path) state.menuTemp.buttonPermissions.push(item.path);
          break;
        case 3:
          if (item.path) state.menuTemp.buttonPermissions.push(item.fieldPermissions);
          break
      }
      if (item.children.length) this.commit("GETLISTPATH", item.children);
    });
  },
  GETROUTERLIST(state) {
    // 云平台环境下如果没有分权限的系统调这个方法可以从路由文件拿到菜单列表
    var routerList = state.defaultConfig[state.env].routerList[0];

    if (routerList && routerList.children) {
      state.asideMenuConfig = forEachRouterPath(
        routerList.children,
        "/admin"
      ).filter(item => item.show);
      return;
    }
    state.asideMenuConfig = [];
  },
  GETERPROUTERLIST(state) {
    // erp环境下如果没有分权限的系统调这个方法可以从路由文件拿到菜单列表
    var routerList = state.defaultConfig[state.env].routerList[0];
    if (routerList && routerList.children.length) {
      state.asideMenuConfig = forEachRouterPath(
        routerList.children,
        "/backstage"
      ).filter(item => item.show);
      return;
    }
    state.asideMenuConfig = [];
  }
};

export default {
  state,
  getters,
  mutations
};
