import API from '@/api/internalSystem/customerManage/satisfaction'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import SatisfactionInfo from "./components/satisfactionInfo/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue"
export default {
  name: "satisfaction",
  data() {
    return {
      title: "满意度调查表",
      loading: false,
      tableData: [],
      formSearch: {
        customer_name: ""
      },
      tableList: [{
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "联系人",
          value: "link_man",
          width: 80
        },
        {
          name: "联系电话",
          value: "telephone",
          width: 90
        },
        {
          name: "销售员",
          value: "fk_sale_employee_name",
          width: 90
        },
        {
          name: "客户评分",
          value: "customer_rating_str"
        },
        {
          name: "响应速度满意度",
          value: "response_speed_str"
        },
        {
          name: "问题解答满意度",
          value: "problem_olving_str"
        },
        {
          name: "客服服务态度满意度",
          value: "service_attitude_str",
          width: 130
        },
        {
          name: "软件试用满意度",
          value: "software_trial_str"
        },
        {
          name: "软件使用熟练度",
          value: "software_is_str"
        },
        {
          name: "调查时间",
          value: "update_time",
          width: 90
        }
      ],
      isAdd: false
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    getList(f = false) {
      this.isAdd = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
          if (f)
            param.pageNum = 1;
          API.query(param).then(res => {
            this.tableData = res.data;
            this.$refs.pagination.setTotal(res.totalCount);
          }).finally(() => {
            this.loading = false;
          });
        });
      // }, 300);
    },
    modify(item) {
      let params = {
        satisfaction_id: item.satisfaction_id
      };
      API.getInfo(params)
        .then(data => {
          this.isAdd = true;
          this.$refs.satisfactionInfo.Show(data.data);
        })
        .catch(() => {});
    }
  },
  components: {
    SatisfactionInfo,
    Pagination,
    TableView
  }
};