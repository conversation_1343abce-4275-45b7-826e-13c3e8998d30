export default [
  {
    path: 'basicInfo',
    name: 'basicInfo',
    title: '系统管理',
    icon: 'home',
    show: true,
    meta: {
      keepAlive: false
    },
    component: resolve =>
      require([
        "@/views/internalSystem/backstage/components/view/view.vue"
      ], resolve),
    children: [
      {
        path: 'job',
        name: 'job',
        title: '角色配置',
        meta: {
          title: "角色配置"
        },
        component: () => import('@/views/internalSystem/backstage/basicInfo/job/index.vue')
      },
      {
        path: 'department',
        name: 'department',
        title: '部门管理',
        meta: {
          title: "部门管理"
        },
        component: () => import('@/views/internalSystem/backstage/basicInfo/department/index.vue')
      },
      {
        path: 'employee',
        name: 'employee',
        title: '员工管理',
        meta: {
          title: "员工管理"
        },
        component: () => import('@/views/internalSystem/backstage/basicInfo/employee/index.vue')
      },
      // {
      //   path: 'dataDictionary',
      //   name: 'dataDictionary',
      //   title: '数据字典',
      //   meta: {
      //     title: "数据字典"
      //   },
      //   component: () => import('@/views/internalSystem/backstage/basicInfo/dataDictionary/index.vue')
      // },
    ]
  }
]
