import API from '@/api/internalSystem/salesManage/quotation'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddQuotation from "./components/addQuotation/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import AuditDetail from '@/mixins/auditDetail.js'
// import {
//   getOptions,
// } from "@/common/internalSystem/common.js"
import {
  mapGetters
} from "vuex";
export default {
  name: "quotation",
  mixins: [AuditDetail],
  data() {
    return {
      title: "销售报价单",
      loading: false,
      tableData: [],
      formSearch: {
        customer_name: "",
        fk_sell_employee_id: "",
        startTime: "",
        endTime: ""
      },
      tableList: [{
          name: "审核状态",
          value: "audit_state_format",
          width: 70
        },
        {
          name: "编号",
          value: "quotation_no",
          width: 110
        },
        {
          name: "客户名称",
          value: "customer_name",
          width: 200
        },
        {
          name: "培训方式",
          value: "train_type_format",
          width: 70
        },
        {
          name: "付款方式",
          value: "pay_type_format",
          width: 110
        },
        {
          name: "销售类型",
          value: "sell_type_format",
          width: 82
        },
        {
          name: "单据备注",
          value: "remark"
        },
        {
          name: "操作员工",
          value: "add_user_name",
          width: 70
        },
        {
          name: "操作员部门",
          value: "add_user_department_name",
          width: 82
        },
        {
          name: "客户地址",
          value: "customer_address"
        },
        {
          name: "销货单位",
          value: "sales_unit_id_format"
        },
        {
          name: "联系人",
          value: "link_man",
          width: 70
        },
        {
          name: "介绍合同",
          value: "introducer_name"
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 70
        },
        {
          name: "销售员部门",
          value: "fk_sell_department_name",
          width: 82
        },
        {
          name: "客户传真",
          value: "fax",
          width: 106
        },
        {
          name: "手机",
          value: "phone",
          width: 106
        },
        {
          name: "联系人QQ",
          value: "link_qq",
          width: 100
        },
        {
          name: "软件序列号",
          value: "software_no",
          width: 100
        }
      ],
      // auditStateList: [],
      employeeList: [],
      isAdd: false
    };
  },

  mounted() {
    this.getList();
    this.$store.dispatch('getEmployee').then(res => {
      this.employeeList = res;
    });
  },
  methods: {
    getList(f = false) {
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
          if (f)
            param.pageNum = 1;
          param.isJurisdiction = this.permissionToCheck('all') ? 1 : 0;
          API.query(param).then(res => {
            this.tableData = res.data;
            this.$refs.pagination.setTotal(res.totalCount);
          }).finally(() => {
            this.loading = false;
          });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addQuotation.Show();
    },
    modify(item) {
      let params = {
        quotation_id: item.quotation_id
      };
      API.getInfo(params)
        .then(data => {
          this.isAdd = true;
          this.$refs.addQuotation.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if (item.audit_state == 1 || item.audit_state == 0)
        return this.error("该单据已发出审核，不允许删除");
      let params = {
        quotation_id: item.quotation_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },

  components: {
    AddQuotation,
    Pagination,
    TableView,
    MyDate
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};