import API from "@/api/internalSystem/salesManage/performance";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index3.vue";
export default {
  name: "performanceAnalysis",
  data() {
    return {
      title: "销售业绩统计",
      loading: false,
      tableData: [],
      formSearch: {
        employeeId: "",
        timeList: [],
        groupingType: ["按月份"],
        queryType: "",
      },
      tableList: [],
      tableList1: [
        {
          name: "员工姓名",
          value: "employee_name",
        },
        {
          name: "月份",
          value: "mon_1",
        },
        {
          name: "合同金额",
          value: "contract_amount",
        },
        {
          name: "到账金额",
          value: "receivables_amount",
        },
        {
          name: "合同未到账金额",
          value: "no_contract_receivables_amount",
        },
      ],
      tableList2: [
        {
          name: "月份",
          value: "mon_1",
        },
        {
          name: "产品归属",
          value: "product_attribution_name",
        },
        {
          name: "合同金额",
          value: "contract_amount",
        },
        {
          name: "到账金额",
          value: "receivables_amount",
        },
        {
          name: "合同未到账金额",
          value: "no_contract_receivables_amount",
        },
      ],
      tableList3: [
        {
          name: "月份",
          value: "mon_1",
        },
        {
          name: "合同金额",
          value: "contract_amount",
        },
        {
          name: "到账金额",
          value: "receivables_amount",
        },
        {
          name: "合同未到账金额",
          value: "no_contract_receivables_amount",
        },
      ],
      // chartData: {
      // columns: [],
      // rows: [
      // { 日期: "1/1", 访问用户: 1393, 下单用户: 1093, 下单率: 0.32 },
      // { 日期: "1/2", 访问用户: 3530, 下单用户: 3230, 下单率: 0.26 },
      // { 日期: "1/3", 访问用户: 2923, 下单用户: 2623, 下单率: 0.76 },
      // { 日期: "1/4", 访问用户: 1723, 下单用户: 1423, 下单率: 0.49 },
      // { 日期: "1/5", 访问用户: 3792, 下单用户: 3492, 下单率: 0.323 },
      // { 日期: "1/6", 访问用户: 4593, 下单用户: 4293, 下单率: 0.78 },
      // ],
      // },
      employeeList: [],
      allList: {},
      // dialogVisible: false,
      // dialogVisibleVChart: false,
    };
  },

  mounted() {
    this.getList(true);
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
  },
  methods: {
    getList(f = false) {
      this.loading = true;
      // setTimeout(() => {
      //修改数据
      //DOM还没更新
      this.$nextTick(() => {
        //DOM现在更新了
        let param = Object.assign(
          this.formSearch,
          this.$refs.pagination.obtain()
        );
        if (f) param.pageNum = 1;
        this.tableList = this.tableList3;

        if (param.queryType === "按销售员") {
          this.tableList = this.tableList1;
        }else if(param.queryType === "按产品归属"){
          this.tableList = this.tableList2;
        }
        if(param.queryType === "按产品归属"){
          API.query3({
            startTime: param.timeList.length > 0 ? param.timeList[0] : "",
            endTime: param.timeList.length > 0 ? param.timeList[1] : "",
            ...param,
          })
            .then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
            })
            .finally(() => {
              this.loading = false;
            });
        }else{
          API.query2({
            startTime: param.timeList.length > 0 ? param.timeList[0] : "",
            endTime: param.timeList.length > 0 ? param.timeList[1] : "",
            ...param,
          })
            .then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
            })
            .finally(() => {
              this.loading = false;
            });
        }
        API.query2({ firstQuery: true, ...param }).then((res) => {
          this.allList = res.data;
        });

      });
      // }, 300);
    },
    // dialogVisibleClick() {
    // this.dialogVisible = true;
    // },
    // dialogVisibleQuery() {
    // this.dialogVisible = false;
    // this.getList(true);
    // },
    // showVChart() {

    //   this.dialogVisibleVChart = true;
    //   if(this.formSearch.groupingType[0] === '按销售员'){
    //     this.chartData.columns = [
    //       "月份",
    //       "合同金额",
    //       "到账金额",
    //       "合同为到账金额",
    //     ];
    //     this.chartData.rows = [];
    //     let item = {};
    //     for (let index = this.tableData.length - 1; index >= 0; index--) {
    //       item = this.tableData[index];
    //       this.chartData.rows.push({
    //         月份: item["mon_1"],
    //         合同金额: item["contract_amount"],
    //         到账金额: item["receivables_amount"],
    //         合同为到账金额: item["no_contract_receivables_amount"],
    //       });
    //     }
    //   }

    // },
  },

  components: {
    Pagination,
    TableView,
    MyDate,
  },
};
