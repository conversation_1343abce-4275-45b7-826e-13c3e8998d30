import Vue from "vue";
import router from '@/router/internalSystem'
import store from '@/store/internalSystem'
import "@/utils/global"
import App from "./App.vue";
import Icon from "vue-svg-icon/Icon.vue";
import VCharts from 'v-charts'
Vue.use(VCharts)
//打印
import print from '@/common/internalSystem/print'
Vue.use(print)

//引入弹窗可拖动
import "@/common/global/directives.js";
// 引入样式
import ElementUI from "element-ui";
import "@/assets/css/trade_erp/index.scss";
import "@/assets/css/element/el-dialog.scss";
Vue.use(ElementUI, {
    size: "small",
});

//引入vxeTable的全局配置
import "./VxeTable.js"

// 按钮权限自定义指令
import { directive } from '@/utils/directive.js'
directive()



import VueClipboard from 'vue-clipboard2'

Vue.use(VueClipboard);

Vue.directive('focus', {
    // 当被绑定的元素插入到 DOM 中时……
    inserted: function (el) {
      // 聚焦元素
      el.focus()
    }
  })
Vue.component("icon", Icon);
Vue.config.productionTip = false;
if (process.env.NODE_ENV == 'development') {
    Vue.config.devtools = true;
} else {
    Vue.config.devtools = false;
}
new Vue({
    router,
    store,
    render: h => h(App)
}).$mount("#App");
