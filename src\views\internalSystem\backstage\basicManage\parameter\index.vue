<template>
	<div class="body-p10">
		<el-form :inline="true" :model="formSearch" size="small">
			<el-form-item label="入税率比例">
				<el-input v-model="formSearch.content" placeholder="请输入税率比例"></el-input>
			</el-form-item>
			<el-form-item label="发票类型">
				<el-select v-model="formSearch.parameter_type" placeholder="请选择" filterable clearable>
					<el-option v-for="item in params_constant_parameter_type" :key="item.id" :label="item.label"
					           :value="item.id">
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
				<el-button type="success" @click="add"
				           v-permit="'ADD_PARAMETER_NEW'">添加
				</el-button>
			</el-form-item>
		</el-form>
		<!-- 新增修改税率比例参数 -->
		<add-parameter ref="AddParameter" @selectData="getList"/>
		<table-view :tableList="tableList" :tableData="tableData" @modify="modify" @del="del"
		            :isEdit="'UPDATE_PARAMETER_NEW'"
		            :isDel="'DEL_PARAMETER_NEW'"></table-view>
		<Pagination ref="pagination" @success="getList"/>
	</div>
</template>

<script src="./index.js"></script>
