<template>
  <div
    class="body-p10 orderH100"
    style="overflow-y: auto; overflow-x: hidden"
    v-if="dialogVisible"
  >
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="addNew">新增</el-button>
      <!-- <el-button
        :disabled="!!ruleForm.outbound_id"
        type="primary"
        @click="callIn"
        >调入合同明细
      </el-button> -->

      <el-button
        v-if="tableFlag === 1"
        :disabled="!!ruleForm.outbound_id"
        type="primary"
        @click="submitForm('ruleForm')"
        :loading="loading"
        >保 存
      </el-button>
      <el-button
        v-if="tableFlag === 2"
        :disabled="!!ruleForm.outbound_id"
        type="success"
        @click="batchSave"
        :loading="loading"
        >批量保存
      </el-button>
      <el-button
        :disabled="!!ruleForm.outbound_id || !!ruleForm.customer_contract_id"
        type="primary"
        @click="chooseContract"
        >调入合同
      </el-button>
      <el-button
        type="success"
        @click="chooseContract2"
        :disabled="!!ruleForm.outbound_id || !!ruleForm.customer_contract_id"
        >批量调用合同
      </el-button>
      <!-- <el-button
        :disabled="ruleForm.end_state === 1 || !ruleForm.outbound_id"
        type="primary"
        @click="check()"
        >同步端口数
      </el-button> -->
      <!-- <el-dropdown @command="handleCommand" class="ml5" v-if="ruleForm.outbound_id">
        <el-button type="primary"> 打印合同</el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="update">修改模板</el-dropdown-item>
          <el-dropdown-item command="select">选择模板</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
    </div>
    <el-form
      :model="ruleForm"
      ref="ruleForm"
      label-width="100px"
      class="flexAndFlexColumn mt10"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单据编号" prop="outbound_no">
            <el-input
              v-model="ruleForm.outbound_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据日期" prop="add_time">
            <el-input
              v-model="ruleForm.update_time"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="操作员" prop="add_user_name">
            <el-input
              v-model="ruleForm.add_user_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input disabled v-model="ruleForm.customer_name" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户编号" prop="customer_no">
            <el-input
              v-model="ruleForm.customer_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="单据备注" prop="remark">
            <el-input
              :disabled="!!ruleForm.outbound_id || tableFlag === 2"
              v-model="ruleForm.remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider></el-divider>

      <TableView
        v-if="tableFlag === 1"
        :tableList="tableList"
        :tableData="tableData"
        :isDblclick="true"
        ref="tableView"
        class="mt10 flexAndFlexColumn"
        :isOperation="!!ruleForm.outbound_no ? false : true"
      >
      </TableView>
      <TableView2
        v-if="tableFlag === 2"
        :tableList="tableList2"
        :tableData="tableData2"
        :isDblclick="true"
        ref="tableView2"
        class="mt10 flexAndFlexColumn"
        :isOperation="!!ruleForm.outbound_no ? false : true"
      >
      </TableView2>
    </el-form>

    <contract-detail-list
      ref="contractDetailList"
      dialogTitle="合同明细列表"
      @getInfo="getContract"
      :detail_type="1"
      :outboundState="0"
      :customer_no="this.ruleForm.customer_no"
      :contract_detail_ids="contract_detail_ids"
      @del="del"
    />
    <ContractList
      ref="contractList"
      :isReceive="true"
      :isAllReceive="false"
      :is_not_outbound_sing="true"
      dialogTitle="未出库合同列表"
      @getInfo="getContractInfo"
      :customer_no="this.ruleForm.customer_no"
      :type="2"
      :data_state="1"
      :contract_ids="customer_contract_ids"
    />
    <ContractList
      ref="contractList2"
      :isReceive="true"
      :isAllReceive="false"
      :is_not_outbound_sing="true"
      dialogTitle="未出库合同列表"
      @getInfo="getContractInfo2"
      :customer_no="this.ruleForm.customer_no"
      :type="3"
      :data_state="1"
      :contract_ids="customer_contract_ids"
    />
  </div>
</template>
<script src="./index.js"></script>

<style lang="scss" scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown + .el-dropdown {
  margin-left: 15px;
}

@import "@/assets/css/element/font-color.scss";
</style>
