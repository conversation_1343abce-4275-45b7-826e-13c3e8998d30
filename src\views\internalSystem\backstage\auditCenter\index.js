import API from '@/api/internalSystem/common'
import auditAPI from '@/api/internalSystem/common/auditInfo.js'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import IntentionAudit from "./components/intentionAudit/index.vue"
import VisitingAudit from "./components/visitingAudit/index.vue"
import UpdateAudit from "./components/updateAudit/index.vue"
import ImpleAudit from "./components/impleAudit/index.vue"
import ArticleAudit from "./components/articleAudit/index.vue"
import QuotationAudit from "./components/quotationAudit/index.vue"
import ContractAudit from "./components/contractAudit/index.vue"
import NewContractAudit from "./components/newContractAudit/index.vue"
import TicketAudit from "./components/ticketAudit/index.vue"
import IncomeAudit from "./components/incomeAudit/index.vue"
import InvoiceIncomeAudit from "./components/invoiceIncomeAudit/index.vue"
import InvoiceAudit from "./components/invoiceAudit/index.vue"
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue"
import {
  mapGetters
} from "vuex";
export default {
  name: "auditCenter",
  data() {
    return {
      title: "审核中心",
      loading: false,
      tableData: [],
      formSearch: {
        tareas_no: "",
        tareas_type: "",
        customer_name: ""
      },
      tableList: [{
          name: "单据编号",
          value: "tareas_no"
        },
        {
          name: "审核单据类型",
          value: "tareas_type"
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "员工名称",
          value: "add_user_name"
        },
        {
          name: "发起审核时间",
          value: "tareas_time"
        }
      ],
      tareasTypeList: [{
        name: '客户修改审核'
      }, {
        name: '实施单审核'
      }, {
        name: '意向报告单审核'
      }, {
        name: '销售客户回访单审核'
      }, {
        name: '文案审核'
      }, {
        name: '报价单审核'
      }, {
        name: '合同单审核'
      }, {
        name: '开票单审核'
      }, {
        name: '收入单审核'
      }, {
        name: '发票进项单审核'
      }, {
        name: '报销单审核'
      }],
      isAudit: false
    };
  },

  mounted() {

    this.getList();
  },
  methods: {
    getList(f = false) {
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
          if (f)
            param.pageNum = 1;
          API.queryAudit(param).then(res => {
            this.tableData = res.data;
            this.$refs.pagination.setTotal(res.totalCount);
          }).finally(() => {
            this.loading = false;
          });
        });
      // }, 300);
    },
    open(item) {

      if(item.data_state ===2){
        return this.error('该记录是旧数据，请上旧系统继续操作')
      }

      let type = '';
      let name = '';
      if (item.tareas_type === '意向报告单审核') {
        type = 'intention';
        name = 'customer_intention_id';
      } else if (item.tareas_type === '销售客户回访单审核') {
        type = 'visiting';
        name = 'sales_visiting_id';
      } else if (item.tareas_type === '客户修改审核') {
        type = 'update';
        name = 'customer_temp_id';
      } else if (item.tareas_type === '实施单审核') {
        type = 'imple';
        name = 'implementation_id';
      } else if (item.tareas_type === '文案审核') {
        type = 'article';
        name = 'copy_id';
      } else if (item.tareas_type === '报价单审核') {
        type = 'quotation';
        name = 'quotation_id';
      } else if (item.tareas_type === '合同单审核') {
        type = 'contract';
        name = 'customer_contract_id';
      } else if (item.tareas_type === '开票单审核') {
        type = 'ticket';
        name = 'ticket_id';
      } else if (item.tareas_type === '收入单审核') {
        type = 'income';
        name = 'income_id';
      } else if (item.tareas_type === '发票进项单审核') {
        type = 'invoiceIncome';
        name = 'invoice_income_id';
      } else if (item.tareas_type === '报销单审核') {
        type = 'invoice';
        name = 'financial_cost_invoice_id';
      }
      let params = {
        [`${name}`]: item.tareas_id
      };
      auditAPI[`${type}Info`](params)
        .then(data => {
          this.isAudit = true;
          this.$refs[`${type}Audit`].Show(data.data);
        })
        .catch(() => {});
    }
  },

  components: {
    IntentionAudit,
    VisitingAudit,
    UpdateAudit,
    ImpleAudit,
    ArticleAudit,
    QuotationAudit,
    ContractAudit,
    NewContractAudit,
    TicketAudit,
    IncomeAudit,
    InvoiceIncomeAudit,
    InvoiceAudit,
    Pagination,
    TableView
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};