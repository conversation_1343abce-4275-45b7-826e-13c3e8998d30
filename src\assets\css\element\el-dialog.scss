//element弹窗样式覆盖
.el-message { // 固定提示的图标文字大小
    .el-message__icon {
        font-size: 14px;
    }
}

$--color-danger: #f56c6c;
.scroll-bar {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        border-radius: 50%;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 8px;
        -webkit-box-shadow: inset 0 0 5px #e5e5e5;
        background: #b7b7b7;
    }

    &::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 5px #fff;
        border-radius: 8px;
        background: #e5e5e5;
    }
}


$--el-dialog__header_height: 60px;
.el-dialog__header {
    padding: 0 !important;
    overflow: hidden;
    height: $--el-dialog__header_height;

    span {
        vertical-align: top;
        margin-left: 20px;
        line-height: $--el-dialog__header_height;
    }
}

// 弹窗的标题
.el-dialog__body {
    padding-top: 8px !important;
    &.gray_background_color {
        background-color: #ebebeb;
    }

    .el-form-item__label {
        line-height: 36px;
    }

    .dialog-box {
        max-height: 50vh;
        overflow-y: auto;

        &.is-flex-box, .is-flex-box {
            overflow-x: hidden;
            .is-flex {
                display: flex;
                margin-bottom: 20px;

                label {
                    width: auto;
                    flex: 0 0 auto;
                    line-height: 32px;
                    font-size: 14px;
                    letter-spacing: 3px;
                    .required {
                        font-size: 12px;
                        color: $--color-danger;
                        line-height: 32px;
                        font-weight: bold;
                    }
                }

                .all {
                    width: 100%;
                    height: 100%;
                    flex: 1;
                    margin-left: 5px;
                }
            }
        }
    }

    .image-list {
        .el-icon-zoom-in, .el-icon-delete {
            line-height: 146px;
        }
    }
}

// 弹窗的底部按钮
.el-dialog__footer {
    padding: 0 20px 0 0;
    height: $--el-dialog__header_height;

    .dialog-footer {
        height: 60px;
        display: inline-block;

        button {
            margin-top: 10px;
        }
    }
}

.roleClassification {
    .el-tree-node__content {
        height: 40px;
    }

    .el-tree-node__expand-icon {
        margin-top: 3px;
    }

    .el-tree__empty-text {
        font-size: 26px;
        margin-left: -200px;
    }
    .custom-tree-node {
        margin-left: 10px;
    }
}

.staff-tree {
    .el-tree__empty-text {
        margin-left: 0;
    }
}
