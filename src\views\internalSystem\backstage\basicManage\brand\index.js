import API from "@/api/internalSystem/basicManage/brand";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import AddBrand from "./components/addBrand/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import FileList from "@/views/internalSystem/backstage/components/fileList/index.vue";
import {
  getOptions
} from "@/common/internalSystem/common.js";
import {
  mapGetters
} from "vuex";

export default {
  name: "brand",
  data() {
    return {
      title: "产品管理",
      loading: false,
      tableData: [],
      formSearch: {
        brand_type: "",
        brand_classify: "",
        disable_state: "",
        brand_name: "",
        sale_pattern: "",
        product_sale_model: "",
        product_is_grant: "",
        product_is_period: "",
      },
      tableList: [{
          name: "产品服务",
          value: "brand_type",
          wbrand_idth: 360,
        },
        {
          name: "软件证书编号",
          value: "brand_no",
        },
        {
          name: "产品归属",
          value: "product_attribution_name",
        },
        {
          name: "成本价(元)",
          value: "brand_cost_price",
        },
        {
          name: "建议售价(元)",
          value: "brand_sell_price",
        },
        {
          name: "品牌分类",
          value: "brand_classify_name",
        },
        {
          name: "开发完成",
          value: "complete_time",
        },
        {
          name: "上级产品",
          value: "parent_name",
        },
        {
          name: "税率",
          value: "rate",
        },
        {
          name: "是否启用",
          value: "disable_state_name",
        },
      ],
      parentList: [], 
      brandClassifyList: [],
      brand_id: 0, // 打开附件弹窗需要先赋值
    };
  },

  mounted() {
    this.brandClassifyList = getOptions("t_brand_management", "brand_classify");
    this.getList();
    this.getParentList();

  },
  methods: {
    getParentList() {
      let param = {
        pageNum: 1,
        brand_classify: 1,
        pageSize: 999,
      };
      API.query(param)
        .then((res) => {
     this.parentList  = res.data || [];
        })
        .catch(() => {});


    },
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(
        this.formSearch,
        this.$refs.pagination.obtain()
      );
      if (f) param.pageNum = 1;
      API.query(param)
        .then((res) => {
          this.tableData = res.data;
          if(this.tableData && this.tableData.length > 0){
            this.tableData.map(item=>{
              // console.log(item)
              item['rate'] = item['rate'] ? item['rate'] + '%' : 0
            })
          }
          this.$refs.pagination.setTotal(res.totalCount);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    add() {
      this.$refs.AddBrand.Show();
    },
    //打开修改产品会话框
    modify(item) {
      let params = {
        brand_id: item.brand_id,
      };
      API.getInfo(params)
        .then((data) => {
          this.$refs.AddBrand.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      let params = {
        brand_id: item.brand_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    addFile(item) {
      this.brand_id = item.brand_id;
      this.$refs.fileList.Show("brand", item.brand_id, item);
    },
  },

  components: {
    AddBrand,
    Pagination,
    TableView,
    FileList,
  },
  computed: {
    ...mapGetters([
      "disable_state",
      "sale_pattern",
      "product_sale_model",
      "product_is_period",
      "product_is_grant",
    ]),
  },
};