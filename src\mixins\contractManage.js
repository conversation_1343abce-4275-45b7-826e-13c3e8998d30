import AddContract from "@/views/internalSystem/backstage/salesManage/newContract/components/addContract/index.vue";
import salesAPI from "@/api/internalSystem/salesManage/contract";

export default {
  data() {
    return {
      isShowDetail: false,
    };
  },
  methods: {
    modify(row) {
     
      this.getInfo(row.customer_contract_id);
    },
    getInfo(customer_contract_id) {
      let params = {
        customer_contract_id: customer_contract_id,
      };
      salesAPI
        .getInfo(params)
        .then((data) => {
          this.isShowDetail = true;
          this.$refs.addContract.Show(data.data);
        })
        .catch(() => {});
    },
  },
  components: {
    AddContract,
  },
};
