import Axios from "@/api/index";
import environment from "@/api/environment";
const prefix = `${environment.internalSystemAPI}customer/`;
const prefix2 = `${environment.internalSystemAPI}customerContract/`;
export default {
  // 查询
  query: (params) => Axios.post(`${prefix}query`, params),
  // 查询产品列表
  getCustomerBrand: (params) => Axios.post(`${prefix}getCustomerBrand`, params),
  // 新增
  add: (params) => Axios.post(`${prefix}add`, params),
  // 删除
  remove: (params) => Axios.post(`${prefix}remove`, params),
  // 编辑
  update: (params) => Axios.post(`${prefix}update`, params),
  // 获取单条信息
  getInfo: (params) => Axios.post(`${prefix}getInfo`, params),
  // 获取单条客户财务信息
  getFinanceInfo: (params) => Axios.post(`${prefix}getFinanceInfo`, params),
  // 更新客户公众号绑定
  refreshWechatBindingState: (params) =>
    Axios.post(`${prefix}refreshWechatBindingState`, params),

  // 销售员转移
  updateSalesman: (params) => Axios.post(`${prefix}updateSalesman`, params),
  // 更改客户阶段
  updateCustomerStage: (params) =>
    Axios.post(`${prefix}updateCustomerStage`, params),
  // 合同列表
  contractList: (params) => Axios.post(`${prefix}contractList`, params),
  // 销售员转移列表
  updateSalesmanList: (params) =>
    Axios.post(`${prefix}updateSalesmanList`, params),
  // 客户联系人列表
  customerLinkmanList: (params) =>
    Axios.post(`${prefix}customerLinkmanList`, params),
  // 新增客户联系人
  addCustomerLinkman: (params) =>
    Axios.post(`${prefix}addCustomerLinkman`, params),
  // 修改客户联系人
  updateCustomerLinkman: (params) =>
    Axios.post(`${prefix}updateCustomerLinkman`, params),
  // 删除客户联系人
  delCustomerLinkman: (params) =>
    Axios.post(`${prefix}delCustomerLinkman`, params),
  // 获取单条客户联系人
  getCustomerLinkmanInfo: (params) =>
    Axios.post(`${prefix}getCustomerLinkmanInfo`, params),
  // 客户修改单列表
  customerTempList: (params) => Axios.post(`${prefix}customerTempList`, params),
  // 获取单条客户修改单
  customerTempInfo: (params) => Axios.post(`${prefix}customerTempInfo`, params),
  // 客户修改单审核
  updateCheckAgree: (params) => Axios.post(`${prefix}updateCheckAgree`, params),
  // 删除客户修改单
  delCustomerTemp: (params) => Axios.post(`${prefix}delCustomerTemp`, params),
  // 新增客户修改单
  addCustomerTemp: (params) => Axios.post(`${prefix}addCustomerTemp`, params),
  // 公海客户列表
  highSeasCustomer: (params) => Axios.post(`${prefix}highSeasCustomer`, params),
  // 推送客户回访单
  sales_push: (params) =>
    Axios.post(
      `${environment.internalSystemAPI}wechat_push/sales_push`,
      params
    ),
  // 判断是否是否有合同附件
  checkContractAnexo: (params) =>
    Axios.post(`${prefix2}checkContractAnexo`, params),
};
