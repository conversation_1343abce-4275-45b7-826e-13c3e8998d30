<template>
  <div class="body-p10">
    <el-form
      :inline="true"
      :model="formSearch"
      size="small"
      v-if="!isAdd && !isDetail && !isTracking && !isDblclick"
    >
      <el-form-item label="查询条件">
        <el-input
          v-model="formSearch.customerName"
          placeholder="请输入客户名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.fk_sale_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="formSearch.softwareNo"
          placeholder="请输入软件序列号"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.brandId"
          placeholder="请选择产品"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in brandList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.maintainStartTime"
          hint="请选择维护开始时间"
          clearable
          style="width: 180px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.newMaintainStopTime"
          hint="请选择维护结束时间"
          clearable
          style="width: 180px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <!-- <el-button
          type="primary"
          @click="add"
          v-permit="'ADD_CUSTOMER_BRAND_INFO_NEW'"
          >制单</el-button
        > -->
        <!-- <el-button type="primary" @click="exportCustomer" :loading="exLoading"
          v-permit="'CUSTOMER_INFO_EXPORT_NEW'">导出
        </el-button> -->
      </el-form-item>
    </el-form>
    <table-view
      ref="customerBrand"
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd && !isDetail && !isTracking && !isDblclick"
      isEdit="CUSTOMER_BRAND_INFO_NEW_ALL"
      @getSelectRecords="getSelectRecords"
      @modify="modify"
      @thrid="tracking"
      @rowDblclick="rowDblclick"
      @sortChange="sortChange"
      isFour="true"
      @four="push"
      :handleWidth="160"
      :isDblclick="true"
      :isOrder="true"
    />
    <Pagination
      ref="pagination"
      @success="getList"
      v-show="!isAdd && !isDetail && !isTracking && !isDblclick"
    />
    <!-- 新增修改客户产品信息 -->
    <add-customer-brand
      ref="addCustomerBrand"
      v-show="isAdd"
      @selectData="getList"
    />
  </div>
</template>

<script src="./index.js"></script>
