import API from '@/api/internalSystem/customerManage/salesVisiting'
import CustomerList from "@/views/internalSystem/backstage/components/customerList/index.vue";
import validationRules from "@/common/internalSystem/validationRules.js"
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  mapGetters
} from 'vuex'
export default {
  name: "addVisiting",
  components: {
    MyDate,
    CustomerList
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_id: "",
        customer_no: "",
        customer_name: "",
        link_man: "",
        phone: "",
        contact_time: "",
        fk_sale_employee_id: "",
        visiting_form: "",
        follow_content: "",
        existing_software_functions: "",
        customer_demand: "",
        dateBackupState: "",
        isHandMaintenance: "",
        payDataState: "",
        receiveDataState: "",
        inventoryState: "",
        invoicePortState: "",
        qq: "",
        we_chat: "",
      },
      rules: {
        link_man: [{
          required: true,
          message: "请输入联系人",
          trigger: "blur"
        }],
        phone: [{
          required: true,
          validator: validationRules.checkPhone,
          trigger: "blur"
        }],
        contact_time: [{
          required: true,
          message: "请输入联系时间",
          trigger: "blur"
        }],
        visiting_form: [{
          required: true,
          message: "请选择回访方式",
          trigger: "change"
        }],
        follow_content: [{
          required: true,
          message: "请输入跟进内容",
          trigger: "blur"
        }],
        customer_demand: [{
          required: true,
          message: "请输入客户需求",
          trigger: "blur"
        }],
        dateBackupState: [{
          required: true,
          message: "请选择数据备份是否完整",
          trigger: "change"
        }],
        isHandMaintenance: [{
          required: true,
          message: "请选择是否续交维护费",
          trigger: "change"
        }],
        payDataState: [{
          required: true,
          message: "请选择应付数据是否准确",
          trigger: "change"
        }],
        receiveDataState: [{
          required: true,
          message: "请选择应收数据是否准确",
          trigger: "change"
        }],
        inventoryState: [{
          required: true,
          message: "请选择库存是否准确",
          trigger: "change"
        }],
        invoicePortState: [{
          required: true,
          message: "请选择是否有在使用航天开票接口",
          trigger: "change"
        }]
      },
      checkSoftware: [],
      loading: false,
      confirmNormal: [{
        name: "客户确认正常",
        value: 1
      }, {
        name: "客户未确认",
        value: 2
      }],
      accurate: [{
        name: "准确",
        value: 1
      }, {
        name: "不准确",
        value: 2
      }],
      normal: [{
        name: "使用",
        value: 1
      }, {
        name: "不使用",
        value: 2
      }],
      whether: [{
        name: "是",
        value: 1
      }, {
        name: "否",
        value: 0
      }],
      softwareList: [{
        name: "进销存管理",
        value: '1'
      }, {
        name: "客户关系管理",
        value: '3'
      }, {
        name: "成套生产管理",
        value: '4'
      }, {
        name: "员工资金管理",
        value: '5'
      }, {
        name: "员工绩效管理",
        value: '6'
      }, {
        name: "产品维修管理",
        value: '7'
      }, {
        name: "客户借货管理",
        value: '8'
      }, {
        name: "供应商管理",
        value: '9'
      }, {
        name: "财务做账管理",
        value: '10'
      }],
      isEdit: false
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.isEdit = true;
      if (data) {
        this.isEdit = this.permissionToCheck('UPDATE_CUSTOMER_VISIT_NEW') ? true : false;
        this.ruleForm = data;
        this.checkSoftware = data.existing_software_functions.split(",");
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    save() {
      let params = this.ruleForm;
      if (this.checkSoftware.length < 1)
        return this.error("请选择现有功能模块");
      params.existing_software_functions = this.checkSoftware.join(",");
      this.loading = true;
      if (params.sales_visiting_id) {
        if (params.auditState == 1)
          return this.error("该单据已审核，不允许修改！");
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      }

    },
    //选择客户
    chooseCustomer() {
      this.$refs.customerList.Show();
    },
    getInfo(info) {
      this.ruleForm.customer_id = info.customer_id;
      this.ruleForm.customer_no = info.customer_no;
      this.ruleForm.customer_name = info.customer_name;
      this.ruleForm.fk_sale_employee_id = info.fk_sale_employee_id;
      this.ruleForm.fk_sale_employee_name = info.fk_sale_employee_id_name;
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_id: "",
        customer_no: "",
        customer_name: "",
        link_man: "",
        phone: "",
        contact_time: "",
        fk_sale_employee_id: "",
        visiting_form: "",
        follow_content: "",
        existing_software_functions: "",
        customer_demand: "",
        dateBackupState: "",
        isHandMaintenance: "",
        payDataState: "",
        receiveDataState: "",
        inventoryState: "",
        invoicePortState: "",
        qq: "",
        we_chat: "",
      }
      this.checkSoftware = [];
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'sales_visiting_form'])
  },
};