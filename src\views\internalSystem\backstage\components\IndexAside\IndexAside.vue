<template>
  <el-menu
    :default-active="$route.path"
    :unique-opened="true"
    :router="false"
    class="el-menu-vertical-demo"
    :class="homeIndent ? 'homeIndent' : ''"
    background-color="#40464d"
    text-color="#fff"
    active-text-color="#409EFF"
    :collapse="homeIndent"
    :collapse-transition="false"
  >
    <!--    #001F42 -->
    <el-menu-item :index="home.menuUrl" :key="0" @click="switchTo(home)">
      <div class="flexColoum">
        <i class="el-icon-menu"></i>
        <span class="menuName">{{ home.menuName }}</span>
      </div>
    </el-menu-item>
    <el-submenu
      v-for="item in routerList"
      v-show="item.children"
      :index="item.menuUrl"
      :key="item.menuName"
      :title="item.menuName"
    >
      <template slot="title">
        <div class="flexColoum">
          <i
            v-if="!item.iconUrl || item.iconUrl.includes('el')"
            :class="[item.iconUrl ? item.iconUrl : 'el-icon-menu']"
          ></i>
          <!-- <img class="iconImg" src="https://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/FDYM5Nme_4999_system_f.png" alt=""> -->
          <icon
            v-if="item.menuName === '财务管理'"
            class="icon"
            :name="item.iconUrl"
          ></icon>
          <span class="menuName">{{ item.menuName }}</span>
        </div>
      </template>
      <el-menu-item
        v-for="child in item.children"
        :index="child.menuUrl"
        :key="child.menuName"
        :title="child.menuName"
        @click="switchTo(child)"
      >
        <span>{{ child.menuName }}</span>
      </el-menu-item>
    </el-submenu>
  </el-menu>
</template>

<script>
import API from "@/api/internalSystem/basicInfo/menu/menuApi";
import { mapGetters, mapMutations } from "vuex";
export default {
  name: "IndexAside",
  data() {
    return {
      routerList: [],
      home: {
        menuName: "首页",
        menuUrl: "/backstage/statistics/statisticsPage",
      },
    };
  },
  mounted() {
    this.getMenuList();
  },
  methods: {
    getMenuList() {
      API.queryAllRole({})
        .then((ret) => {
          this.routerList = ret.data;
 
          // 保存权限
          this.$store.dispatch("setFlatPermissions", ret.data || []);
          this.setPermissions();
        })
        .catch(() => {});
    },
    switchTo(index) {
      this.$router.push(index.menuUrl);
    },
    setPermissions() {
      let buttonPermissions = [];
      this.routerList.forEach((list) => {
        list.children.forEach((item) => {
          item.children.forEach((tItem) => {
            buttonPermissions.push(tItem.menuDescribe);
          });
        });
      });
      this.SET_BUTTONPERMISSIONS(buttonPermissions);
    },
    ...mapMutations(["SET_BUTTONPERMISSIONS"]),
  },
  computed: {
    ...mapGetters(["homeIndent"]),
  },
};
</script>

<style lang="scss" scoped>
$themeColor: #409eff;

.homeIndent {
  /deep/ .el-menu-item {
    padding-left: 13px !important;
  }
  /deep/ .el-submenu__title {
    padding-left: 0 !important;
    line-height: 1;
    height: 70px;
  }
  /deep/ .el-menu-item {
    padding: 0 !important;
    height: 70px;
  }

  /deep/ i {
    padding-right: 0 !important;
    font-size: 20px;
    color: #fff;
  }
  /deep/ .el-submenu__icon-arrow {
    display: none !important;
  }
  /deep/.el-menu--inline .el-menu-item {
    padding-left: 10px !important;
  }
}
/deep/.el-submenu.is-active,
.el-menu-item.is-active {
  color: $themeColor !important;
  i {
    color: $themeColor !important;
  }
  .icon {
    color: $themeColor;
  }
  .menuName {
    color: $themeColor;
  }
}
/deep/.el-submenu__title:hover,
/deep/.el-menu-item:hover {
  color: $themeColor !important;
  // background-color: $themeColor !important;
  i {
    color: $themeColor !important;
  }
}
.flexColoum {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 5px;
  height: 100%;
  .icon {
    width: 22px;
    height: 22px;
    color: #fff;
  }
  &:hover {
    .icon {
      color: $themeColor;
    }
  }
  .menuName {
    width: 100%;
  }
}
/deep/.el-submenu__title {
  padding: 0 !important;
}
/deep/.el-menu--collapse > .el-menu-item span,
.el-menu--collapse > .el-submenu > .el-submenu__title span {
  height: 32px;
  width: 70px;
  visibility: visible;
  overflow: visible;
  display: flex;
  justify-content: center;
  align-items: center;
}

.el-menu--collapse > .el-menu-item span,
.el-menu--collapse > .el-submenu > .el-submenu__title span {
  height: 32px;
  width: 70px;
  visibility: visible;
  overflow: visible;
  display: flex;
  justify-content: center;
  align-items: center;
}
.el-menu-item {
  &:hover {
    // background-color: #409EFF !important;
    color: #409eff;
  }
}
</style>


<style>

</style>