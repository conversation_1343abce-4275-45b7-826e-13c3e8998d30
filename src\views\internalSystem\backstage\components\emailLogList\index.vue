<template>
  <el-dialog
    title="邮件发送日志"
    :visible.sync="dialogVisible"
    width="60%"
    append-to-body
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <TableView
      :tableList="tableList"
      :tableData="tableData"
      :tableHeight="460"
    ></TableView>
    <Pagination ref="pagination" @success="getList" />
  </el-dialog>
</template>

<script>
import API from "@/api/internalSystem/common/email.js";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";

export default {
  name: "EmailLogList",
  components: {
    Pagination,
    TableView,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableData: [],
      anexo_id: null,
      tableList: [
        { name: "发送时间", value: "send_time", width: 160 },
        { name: "收件人", value: "recipient_email" },
        { name: "主题", value: "subject" },
        { name: "发送状态", value: "status_name", width: 100 },
        { name: "操作人", value: "operator_name", width: 120 },
      ],
    };
  },
  methods: {
    Show(anexo_id) {
      this.anexo_id = anexo_id;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.getList(true);
      });
    },
    getList(isFirst = false) {
      this.loading = true;
      const params = this.$refs.pagination.obtain();
      if (isFirst) {
        params.pageNum = 1;
      }
      params.anexo_id = this.anexo_id;
      
      API.getEmailLogs(params)
        .then((res) => {
          this.tableData = res.data;
          this.$refs.pagination.setTotal(res.totalCount);
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script> 