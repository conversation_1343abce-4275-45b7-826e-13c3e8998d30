<template>
  <div class="body-p10" v-loading="loading">
    <el-form
      :inline="true"
      :model="formSearch"
      size="small"
      v-if="!isShowDetail"
    >
      <el-form-item label="查询条件">
        <el-input
          v-model="formSearch.employee_number"
          placeholder="请输入合同编号"
          clearable
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-input
          v-model="formSearch.employee_number"
          placeholder="请输入客户名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.fk_sell_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.startTime" hint="请选择开始时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.endTime" hint="请选择结束时间"></my-date>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.in_position"
          clearable
          placeholder="请选择授权状态"
        >
          <el-option
            v-for="item in in_position"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button type="primary" @click="add">填写授权码</el-button>
      </el-form-item>
    </el-form>

    <table-view
      v-if="!isShowDetail"
      :tableList="tableList"
      :tableData="tableData"
      isEdit="permanent_button"
      :isSel="true"
      isDel="DEL_EMPLOYEE_NEW"
      @getSelectRecords="getSelectRecords"
      @modify="modify"
    ></table-view>
    <Pagination v-if="!isShowDetail" ref="pagination" @success="getList" />
    <AddContract ref="addContract" @getInfo="getInfo" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>
