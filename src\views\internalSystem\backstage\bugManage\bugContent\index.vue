<template>
  <div class="bugContainer">
    <div class="head">
      <div class="left"></div>
    </div>
    <div class="content">
      <keep-alive>
        <router-view></router-view>
      </keep-alive>
    </div>
  </div>
</template>

<script>
export default {};
</script>

<style lang="scss">
.bugContainer {
  .el-input,
  .el-select,
  .el-cascader,
  .el-input__inner {
    width: 100%;
    
  }

  .no-border-input {
    .el-input__inner {
      border: none;
    }
  }
}
</style>

<style lang="scss" scoped>
.bugContainer {
  height: 100%;
  width: 100%;
  overflow: hidden;
  .head {
    display: flex;
    justify-content: center;
  }
  .content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
  }
}
</style>
