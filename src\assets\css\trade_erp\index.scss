
@import "./element/colour.scss";

$--font-path: '~element-ui/lib/theme-chalk/fonts';
@import "~element-ui/packages/theme-chalk/src/index";

@import "element/dialog";
//ele输入框样式修改
@import "element/input";
//ele其他杂项样式修改
@import "element/others";
//按钮之间间距
@import "element/button";



@import "../global/style";

@import "../echarts/index";

//@import "../element/index";
//修改的element弹窗样式
//@import "../element/element-dialog.scss";
//修改的vxe-table样式
@import "../vxe_table/index.scss";

//修改的全局滚动条样式
@import "../scroll/index.scss";

//弹窗底部分页条与按钮一行的通用样式
@import "./dialogFooter.scss";
// 表格通用样式
@import "./tabel.scss";
//单据查询页头部样式
@import "./selectPageHeadForm.scss";
// 按钮通用样式
@import "./btn.scss";
// 项目内的除以前具体类别外的公用样式
@import "./common.scss";


@import "./newTable.scss";