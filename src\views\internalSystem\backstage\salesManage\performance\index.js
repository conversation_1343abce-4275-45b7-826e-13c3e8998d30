import API from '@/api/internalSystem/salesManage/performance'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
export default {
  name: "performance",
  data() {
    return {
      title: "销售业绩统计",
      loading: false,
      tableData: [],
      formSearch: {
        employee_number: "",
        startTime: "",
        endTime: ""
      },
      tableList: [{
          name: "员工工号",
          value: "employee_number"
        },
        {
          name: "员工姓名",
          value: "employee_name"
        },
        {
          name: "部门名称",
          value: "department_name"
        },
        {
          name: "维护费",
          value: "maintenanceFee"
        },
        {
          name: "首次销售",
          value: "softwareVersion"
        },
        {
          name: "软件升级",
          value: "softwareUpgrade"
        },
        {
          name: "租用软件",
          value: "rentSoftware"
        },
        {
          name: "增加端口",
          value: "increasePort"
        },
        {
          name: "金万维",
          value: "thousand"
        },
        {
          name: "意向单数量",
          value: "intentionNum"
        },
        {
          name: "回访单数量",
          value: "salesVisitingNum"
        }
      ],
      employeeList: []
    };
  },

  mounted() {
    this.getList();
    this.$store.dispatch('getEmployee').then(res => {
      this.employeeList = res;
    });
  },
  methods: {
    getList(f = false) {
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
          if (f)
            param.pageNum = 1;
          API.query(param).then(res => {
            this.tableData = res.data;
            this.$refs.pagination.setTotal(res.totalCount);
          }).finally(() => {
            this.loading = false;
          });
        });
      // }, 300);
    },
  },

  components: {
    Pagination,
    TableView,
    MyDate
  }
};