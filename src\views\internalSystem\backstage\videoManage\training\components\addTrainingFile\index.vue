<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    append-to-body
    @close="dialogCancel"
    width="40%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    v-dialogDrag
  >
    <el-button size="small" @click="dialogCancel">取 消</el-button>

    <el-upload
      ref="uploadFile"
      action=""
      :show-file-list="false"
      :http-request="(file) => ossFile(file, index)"
      style="width: 100%; margin-top: 10px"
    >
      <el-button size="small" type="primary" :disabled="!training_video_id"
        >添加附件</el-button
      >
    </el-upload>
    <vxe-table
      :data="fileList"
      :id="'addTrainingFile'"
      column-key
      border
      resizable
      highlight-hover-row
      highlight-current-row
      auto-resize
      ref="xTable"
      :height="500"
      :loading="false"
      header-row-class-name="head-row-class"
      align="center"
      @sort-change="sortChange"
      :checkbox-config="{
        reserve: true,
        showHeader: true,
        trigger: 'row',
      }"
      :custom-config="{
        storage: {
          visible: true,
          resizable: true,
        },
      }"
      size="small"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
    >
      <vxe-table-column prop="file_name" label="附件名称"> </vxe-table-column>
      <vxe-table-column prop="enable_flag" label="状态" width="60">
        <template slot-scope="scope">
          {{ scope.row.enable_flag === 1 ? "启用" : "禁用" }}
        </template>
      </vxe-table-column>
      <vxe-table-column prop="traffic" label="访问量" width="60">
      </vxe-table-column>
      <vxe-table-column prop="update_time" label="更新时间" width="140">
      </vxe-table-column>
      <vxe-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button
            @click="changeEnableFlag(scope.row, scope.$index)"
            type="text"
            size="small"
            >{{ scope.row.enable_flag === 1 ? "禁用" : "启用" }}</el-button
          >
          <el-button
            @click="removeFile(scope.row, scope.$index)"
            type="text"
            size="small"
            style="color: red"
            >删除</el-button
          >
        </template>
      </vxe-table-column>
    </vxe-table>
  </el-dialog>
</template>
<script src="./index.js">
</script>