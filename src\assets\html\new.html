<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>吉勤科技</title>
    <style>
      .headForm-li {
        list-style-type: none;
        text-align: center;
        margin-top: 30px;
        width: 100%;
        height: 45px;
        display: flex;
        float: left;
      }

      .labelBox {
        width: 140px;
        flex: 0 0 140px;
        height: 100%;
        text-align: center;
        overflow: hidden;
      }

      .labelName {
        display: inline-block;
        width: 100%;
        line-height: 20px;
        font-size: 14px;
        overflow: hidden;
      }

      .headFormData {
        line-height: 22px;
        width: calc(100% - 140px);
        height: 100%;
        flex: 1;
        border-bottom: 1px solid #000;
        display: flex;
        align-items: center;
      }

      .headFormData-span {
        width: 100%;
        word-wrap: break-word;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        -webkit-box-align: stretch;
      }

      .tr-td {
        text-align: center;
        min-height: 30px;
        height: 50px;
        min-width: 150px;
        padding: 0 10px;
        border-bottom: 1px solid #999;
        border-right: 1px solid #999;
      }
      .support{
       text-align: center;
      }
      .support a{
        text-decoration: none;
      }

    </style>
  </head>
  <body>
    <h2 style="margin-top: 20px;font-size: 1.6rem; text-align: center">
      {{title}}
    </h2>
    <ul
      style="width: 95%;margin: 40px auto; list-style: none; padding-left: 0; height: 200px;"
    >
      <li class="headForm-li">
        <div class="labelBox">
          <label class="labelName">收件人</label>
          <label class="labelName">Receiver:</label>
        </div>
        <div class="headFormData">
          <span class="headFormData-span">{{Receiver}}</span>
        </div>
      </li>
      <li class="headForm-li">
        <div class="labelBox">
          <label class="labelName">公司名称</label>
          <label class="labelName">ReceiveCompany:</label>
        </div>
        <div class="headFormData">
          <span class="headFormData-span">{{ReceiveCompany}}</span>
        </div>
      </li>
    </ul>

    <div style="width: 100%; overflow-x: auto; height: auto;">
      <table
        style="margin-bottom: 30px; border-top: 1px solid #999; border-left: 1px solid #999;"
        cellspacing="0"
        cellpadding="0"
      >
        <thead>
          <tr>
            <td class="tr-td">
              入库单号
            </td>
            <td class="tr-td">
              品牌
            </td>
            <td class="tr-td">
              型号
            </td>
            <td class="tr-td">
              品名
            </td>
            <td class="tr-td">
              计量单位
            </td>
            <td class="tr-td">
              入库数量
            </td>
            <td class="tr-td">
              销售合同
            </td>
          </tr>
          <tr>
            <td class="tr-td">
              storage number
            </td>
            <td class="tr-td">
              brand
            </td>
            <td class="tr-td">
              model
            </td>
            <td class="tr-td">
              name
            </td>
            <td class="tr-td">
              measure unit
            </td>
            <td class="tr-td">
              public int count
            </td>
            <td class="tr-td">
              sales agreement
            </td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="tr-td">
              {{storageNumber}}
            </td>
            <td class="tr-td">
              {{brand}}
            </td>
            <td class="tr-td">
              {{model}}
            </td>
            <td class="tr-td">
              {{name}}
            </td>
            <td class="tr-td">
              {{measureUnit}}
            </td>
            <td class="tr-td">
              {{publicIntCount}}
            </td>
            <td class="tr-td">
              {{salesAgreement}}
            </td>
          </tr>
        </tbody>
      </table>
    </div>


    <ul
    style="width: 95%;margin: 40px auto; list-style: none; padding-left: 0; height: 400px;"
  >
    <li class="headForm-li">
      <div class="labelBox">
        <label class="labelName">发件人</label>
        <label class="labelName">Sender:</label>
      </div>
      <div class="headFormData">
        <span class="headFormData-span">{{Sender}}</span>
      </div>
    </li>
    <li class="headForm-li">
      <div class="labelBox">
        <label class="labelName">发件公司</label>
        <label class="labelName">SendCompany:</label>
      </div>
      <div class="headFormData">
        <span class="headFormData-span">{{SendCompany}}</span>
      </div>
    </li>
    <li class="headForm-li">
      <div class="labelBox">
        <label class="labelName">发件地址</label>
        <label class="labelName">SendAddress:</label>
      </div>
      <div class="headFormData">
        <span class="headFormData-span">{{SendAddress}}</span>
      </div>
    </li>
    <li class="headForm-li">
      <div class="labelBox">
        <label class="labelName">发件人电话</label>
        <label class="labelName">SendPhone:</label>
      </div>
      <div class="headFormData">
        <span class="headFormData-span">{{SendPhone}}</span>
      </div>
    </li>
    <li class="headForm-li">
      <div class="labelBox">
        <label class="labelName">发件人邮箱</label>
        <label class="labelName">SendEmail:</label>
      </div>
      <div class="headFormData">
        <span class="headFormData-span">{{SendEmail}}</span>
      </div>
    </li>
   
  </ul>

  <div class="support"><a href="https://www.jiqinyun.com/home">技术支持：福州吉勤信息科技有限公司</a></div>
  </body>
</html>
