import API2 from "@/api/internalSystem/salesManage/contract";
import brandAPI from "@/api/internalSystem/basicManage/brand";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
// import { getMainBrand } from "@/utils/calculate.js";
export default {
  name: "salesTransferList",
  data() {
    return {
      loading: false,
      tableData: [],
      tableList: [
        {
          name: "客户编号",
          value: "customer_no",
        },
        {
          name: "产品服务",
          value: "brand_name",
        },
        {
          name: "软件授权码",
          value: "software_no",
        },
        // {
        //   name: "原有端口数",
        //   value: "port_number"
        // },
        {
          name: "端口数",
          value: "original_port_count",
        },
        // {
        //   name: "软件授权码",
        //   value: "software_no"
        // },
        {
          name: "合同金额",
          value: "contract_amount",
        },
        {
          name: "维护比例",
          value: "year_maintain_cost",
        },
        {
          name: "维护费",
          value: "maintenance_fee",
        },
        {
          name: "维护起始日期",
          value: "maintain_start_time",
        },
        {
          name: "维护结束日期",
          value: "new_maintain_stop_time",
        },
        {
          name: "产品备注",
          value: "remark",
        },
        // {
        //   name: "成交日期",
        //   value: "deal_time"
        // },
        // {
        //   name: "备案日期",
        //   value: "record_time"
        // },
        // {
        //   name: "维护状态",
        //   value: "maintain_stop_state"
        // },
        // {
        //   name: "软件状态",
        //   value: ""
        // }
      ],
    };
  },
  props: {
    customer_id: {
      type: Number,
    },
    port_number: {
      tyle: Number,
      default: () => {
        return 0;
      },
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    async getList() {
      let param = Object.assign(this.$refs.sales_pagination.obtain());
      let { data } = await brandAPI.getPortBrand();
      param.customer_id = this.customer_id;
      param.pageSize = 100;
      API2.getCustomerBrandByCustomer(param)
        .then((res) => {
          this.tableData = res.data;
          let element = {}
          for (let index = 0; index < this.tableData.length; index++) {
            element = this.tableData[index]
            element["year_maintain_cost"] =
              Number(element["year_maintain_cost"]) &&
              Number(element["year_maintain_cost"]) > 0
                ? Number(element["year_maintain_cost"]) + "%"
                : 0;
            if (data.includes(element["fk_brand_id"])) {
              element["port_number"] = this.port_number;
            }
          }
          this.$refs.sales_pagination.setTotal(res.totalCount);
        })
        .finally(() => {
          this.loading = false;
        });

      // API.getCustomerBrand(param).then(res => {
      //   this.tableData = res.data

      //   this.$refs.sales_pagination.setTotal(res.totalCount)
      // }).finally(() => {
      //   this.loading = false
      // })
    },
  },
  components: {
    TableView,
    Pagination,
  },
};
