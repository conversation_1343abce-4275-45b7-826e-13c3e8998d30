<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small" v-if="!isAdd&&!isAudit">
      <!-- <el-form-item label="查询条件">
        <el-select
          v-model="formSearch.add_user_id"
          placeholder="请选择操作员"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_name"
            :value="item.employeeId"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item>
        <el-select
          v-model="formSearch.approval_state"
          placeholder="请选择审核状态"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in financial_cost_invoice_audit"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="查询条件">
      <el-select
          v-model="formSearch.employee_name"
          placeholder="请选择承担员工"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_name"
            :value="item.employee_name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.cost_detail_no"
          placeholder="请选择明细名称"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="(item,index) in costDetailOption"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.startTime" hint="请选择开始时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.endTime" hint="请选择结束时间"></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button
          type="primary"
          @click="add"
          v-permit="'ADD_INVOICE_NEW'"
        >添加</el-button>
      </el-form-item>
    </el-form>
    <!-- 新增修改费用报销单信息 -->
    <add-invoice ref="addInvoice" @selectData="getList" />
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      :isEdit="'permanent_button'"
      v-if="!isAdd&&!isAudit"
      :isDel="'DEL_INVOICE_NEW'"
      @modify="modify"
      @del="del"
      :isThrid="'AUDIT_INVOICE_NEW'"
      :thridTitle="'审核'"
      @thrid="item => toAuditDet(item, '报销单审核','approval_state')"
    ></table-view>
    <div class="mt10 moneyTitle" v-if="!isAdd && !isAudit">
      <el-row>
        <el-col :span="4">报销总金额：{{ totalMoney }}元</el-col>
      </el-row>
    </div>
    <Pagination ref="pagination" @success="getList" v-show="!isAdd&&!isAudit" />
    <!-- 审核 -->
    <InvoiceAudit ref="invoiceAudit" v-show="isAudit" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>