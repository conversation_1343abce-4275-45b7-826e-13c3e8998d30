.el-button+.el-button {
  margin-left: 5px;
}

/* 消息提示样式覆盖 */
.el-message {
  margin-top: 20px !important;
  top: 0 !important;
  z-index: 5000 !important;
}

/* tab栏容器高度覆盖 */
.el-tabs__content {
  height: calc(100% - 40px) !important;

  .el-tab-pane {
    height: 100%;
    width: 100%;
  }
}

//树形控件当前行样式
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #e6f7ff;
  &:hover{
    background-color:#d7effb;
  }
}
.el-form-item{
  width: 100%;
}
.el-col .el-form-item .el-form-item__content .el-select {
  width: 100%;
}