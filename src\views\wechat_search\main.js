import Vue from "vue";
import router from '@/router/wechat_search/index.js'
import store from '@/store/wechat_search/index.js'
import "@/utils/global"
import App from "./App.vue";
import Icon from "vue-svg-icon/Icon.vue";

// css reset
import 'normalize.css/normalize.css'

// 自动设置根rem
// import 'amfe-flexible/index.js'
// 手动设置根rem（自动的需要引入postcss会影响其他项目，需要把px改成rem）
import setRem from '@/utils/rem.js'
setRem()

import { List,PullRefresh,Toast,Empty } from 'vant';

Vue.use(List).use(PullRefresh).use(Toast).use(Empty);

Vue.component("icon", Icon);
Vue.config.productionTip = false;
if (process.env.NODE_ENV == 'development') {
    Vue.config.devtools = true;
} else {
    Vue.config.devtools = false;
}
new Vue({
    router,
    store,
    render: h => h(App)
}).$mount("#App");
