import API from '@/api/internalSystem/salesManage/quotation'
import paramAPI from '@/api/internalSystem/basicManage/parameter'
import brandAPI from '@/api/internalSystem/basicManage/brand'
import TableCustom from "@/views/internalSystem/backstage/components/tableCustom/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  getOptions
} from "@/common/internalSystem/common.js"
import { mapGetters } from 'vuex';
export default {
  name: "quotationAudit",
  components: {
    TableCustom,
    MyDate
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_name: "",
        fk_customer_id: "",
        train_type: "",
        pay_type: "",
        sell_type: "",
        sales_unit_id: "",
        link_man: "",
        fk_sell_employee_id: "",
        phone: "",
        introducer_name: "",
        introducer: "",
        province_name: "",
        city_name: "",
        remark: "",
        customer_address: "",
        fax: "",
        software_no: "",
        link_qq: "",
        sales_unit_id_format: "",
        introducer_contract_format: "",
        introducer_contract_id: ""
      },
      trainTypeList: [], //培训方式
      payTypeList: [], //付款方式
      sellTypeList: [], //销售类型
      softwareVersionList: [], //软件版本
      measurementUnitList: [], //计量单位
      paramsList: [],
      loading: false,
      activeName: "first",
      proList: [], //产品列表
      moduleList: [], //模块列表
      quotationRateList: [], //报价税率
      prepaymentRatioList: [], //预付款比例
      yearsFeeList: [], //年维护费比例
      proTableCol: [{
        label: "产品服务",
        prop: "brand_id",
        need: true,
        width: 160
      }, {
        label: "软件版本",
        prop: "software_version",
        need: true,
        width: 160
      }, {
        label: "计量单位",
        prop: "measurement_unit",
        need: true,
        width: 160
      }, {
        label: "报价数量",
        prop: "quotation_number",
        width: 160
      }, {
        label: "报价单价(元)",
        prop: "quotation_unit",
        need: true,
        width: 160
      }, {
        label: "原有端口数",
        prop: "qriginal_port_number",
        width: 160
      }, {
        label: "新增端口数",
        prop: "new_port_number",
        width: 160
      }, {
        label: "备注",
        prop: "remark",
        width: 160
      }, {
        label: "报价税率",
        prop: "quotation_rate",
        width: 160
      }, {
        label: "预付款比例%",
        prop: "prepayment_ratio",
        width: 160
      }, {
        label: "年维护费比例%",
        prop: "years_maintenance_fee",
        width: 160
      }, {
        label: "维护起始日期",
        prop: "maintain_start_time",
        need: true,
        width: 160
      }, {
        label: "新维护结束日期",
        prop: "new_maintain_stop_time",
        need: true,
        width: 160
      }, {
        label: "原维护结束日期",
        prop: "original_maintain_stop_time",
        width: 160
      }, {
        label: "付款天数",
        prop: "payment_day",
        need: true,
        width: 160
      }],
      proObj: {},
      moduleTableCol: [{
        label: "模块名称",
        prop: "brand_id",
        need: true
      }, {
        label: "计量单位",
        prop: "measurement_unit",
        need: true
      }, {
        label: "报价单价(元)",
        prop: "quotation_unit",
        need: true
      }, {
        label: "备注",
        prop: "remark"
      }],
      moduleObj: {},
      dialogVisibleAudit: false,
      auditForm: {
        auditState: 1,
        auditRemark: ""
      },
      rules: {
        auditState: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }]
      },
      auditStateList: [],
    
    };
  },
  methods: {
    async Show(data = null) {
      this.dialogVisible = true;
      this.proList=[];
      this.moduleList=[];
      this.softwareVersionList=[];
      this.measurementUnitList=[];
      this.quotationRateList=[];
      this.prepaymentRatioList=[];
      this.yearsFeeList=[];
      this.trainTypeList = getOptions('t_quotation', 'train_type');
      this.payTypeList = getOptions('t_quotation', 'pay_type');
      this.sellTypeList = getOptions('t_quotation', 'sell_type');
      this.softwareVersionList = getOptions('t_quotation_detail', 'software_version');
      this.measurementUnitList = getOptions('t_quotation_detail', 'measurement_unit');
      this.auditStateList = getOptions('t_quotation', 'audit_state');
      this.activeName="first";
      this.softwareVersionList.forEach(item => {
        item.label = item.sysName;
        item.value = item.sysValue;
      });
      this.measurementUnitList.forEach(item => {
        item.label = item.sysName;
        item.value = item.sysValue;
      });
      await this.getBrand();
      await this.getParam();
      this.proObj = {
        brand_id: {
          value: "",
          type: "select",
          option: this.proList,
          disabled: true
        },
        software_version: {
          value: "",
          type: "select",
          option: this.software_version,
          disabled: true
        },
        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
          disabled: true
        },
        quotation_number: {
          value: "1",
          type: "input",
          disabled: true
        },
        quotation_unit: {
          value: "",
          type: "float",
          disabled: true
        },
        qriginal_port_number: {
          value: 0,
          type: "number",
          disabled: true
        },
        new_port_number: {
          value: "",
          type: "number",
          disabled: true
        },
        remark: {
          value: "",
          type: "input",
          disabled: true
        },
        quotation_rate: {
          value: "",
          type: "select",
          option: this.quotationRateList,
          disabled: true
        },
        prepayment_ratio: {
          value: "",
          type: "select",
          option: this.prepaymentRatioList,
          disabled: true
        },
        years_maintenance_fee: {
          value: "",
          type: "select",
          option: this.yearsFeeList,
          disabled: true
        },
        maintain_start_time: {
          value: "",
          type: "date",
          disabled: true
        },
        new_maintain_stop_time: {
          value: "",
          type: "date",
          disabled: true
        },
        original_maintain_stop_time: {
          value: "",
          type: "date",
          disabled: true
        },
        payment_day: {
          value: "",
          type: "number",
          disabled: true
        }
      }
      this.moduleObj = {
        brand_id: {
          value: "",
          type: "select",
          option: this.moduleList,
          disabled: true
        },
        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
          disabled: true
        },
        quotation_unit: {
          value: "",
          type: "input",
          disabled: true
        },
        remark: {
          value: "",
          type: "input",
          disabled: true
        }
      }
      if (data) {
        this.ruleForm = data;
        API.detailList({
            quotation_id: data.quotation_id
          })
          .then(res => {
            res.data.map(item => {
              for (let v in item) {
                if (item.detail_type == 1) {
                  if (this.proObj[v]) {
                    this.proObj[v].value = item[v]
                  }
                } else {
                  if (this.moduleObj[v]) {
                    this.moduleObj[v].value = item[v]
                  }
                }
              }
              if (item.detail_type == 1) {
                this.$refs.proTableCustom.add();
              } else {
                this.$refs.moduleTableCustom.add();
              }
            })

          })
          .catch(() => {}).finally(() => {

          });
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    save() {
      let params = this.auditForm;
      params.quotation_id = this.ruleForm.quotation_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    getBrand() {
      return new Promise((resolve, reject) => {
				brandAPI.query()
						.then(data => {
							data.data.forEach(item => {
								if (item.brand_classify == 1)
									this.proList.push({
										label: item.brand_type,
										value: item.brand_id
									})
								else if (item.brand_classify == 2)
									this.moduleList.push({
										label: item.brand_type,
										value: item.brand_id
									})
							});
							resolve(1)
						}).catch(() => {
					reject()
				});
			})
    },
    getParam() {
      paramAPI.query()
        .then(data => {
          data.data.forEach(item => {
            if (item.parameter_type == 5)
              this.quotationRateList.push({
                label: item.content,
                value: item.content
              })
            else if (item.parameter_type == 1)
              this.prepaymentRatioList.push({
                label: item.content,
                value: item.content
              })
            else if (item.parameter_type == 2)
              this.yearsFeeList.push({
                label: item.content,
                value: item.content
              })
          });
        }).catch(() => {});
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_name: "",
        fk_customer_id: "",
        train_type: "",
        pay_type: "",
        sell_type: "",
        sales_unit_id: "",
        link_man: "",
        fk_sell_employee_id: "",
        phone: "",
        introducer_name: "",
        introducer: "",
        province_name: "",
        city_name: "",
        remark: "",
        customer_address: "",
        fax: "",
        software_no: "",
        link_qq: "",
        sales_unit_id_format: "",
        introducer_contract_format: "",
        introducer_contract_id: ""
      }
    }
  },
  computed: {
    ...mapGetters(["sell_type",
    "contract_pay_type",
    "contract_train_type",
    "software_version",
    "measurement_unit",
  "contract_auditStateList"])
  },
};