const CompressionPlugin = require("compression-webpack-plugin");
const _moment = require("moment");
const path = require("path");
const productionGzipExtensions = ["js", "css", "html"];
const BundleAnalyzerPlugin = require("webpack-bundle-analyzer")
  .BundleAnalyzerPlugin;
const configuration = {
  buildTime: _moment().format("YYYY-MM-DD HH:mm:ss"),
  title: "吉勤云",
  keywords: "",
  description: ""
};

const externals = {
  vue: "Vue",
  "vue-router": "VueRouter",
  vuex: "Vuex",
  "vxe-table-plugin-element": "VXETablePluginElement",
  "xe-utils": "XEUtils",
  echarts: "echarts",
  "vxe-table": "VXETable",
  lodash: "_",
  OSS: "ali-oss",
  axios: "axios",
  "element-ui": "ELEMENT",
  moment: "moment",
  swiper: "Swiper",
  "vue-awesome-swiper": "VueAwesomeSwiper",
  "js-cookie": "Cookies"
};

module.exports = {
  publicPath: "/",
  outputDir: "dist",
  lintOnSave: true,
  chainWebpack: config => {
    config.resolve.alias
      .set('@', path.resolve(__dirname, 'src'));
  },
  pages: {
    index: {
      //内部系统
      entry: "src/views/internalSystem/main.js",
      template:
        process.env.NODE_ENV === "production"
          ? "public/index.html"
          : "public/index_dev.html",
      filename: "index.html",
      buildTime: configuration.buildTime,
      title: "吉勤云-内部系统",
      keywords: configuration.keywords,
      description: configuration.description
    },
    wechat_search: {
      // erp公众号搜索
      entry: "src/views/wechat_search/main.js",
      template:
        process.env.NODE_ENV === "production"
          ? "public/wechat_search.html"
          : "public/wechat_search.html",
      filename: "wechat_search.html",
      buildTime: configuration.buildTime,
      title: "erp库存搜索",
      keywords: configuration.keywords,
      description: configuration.description
    },
  },
  // eslint-disable-next-line no-unused-vars
  configureWebpack: config => {
    if (process.env.NODE_ENV === "production") {
      return {
        externals: externals,
        plugins: [
          new CompressionPlugin({
            test: new RegExp(
              "\\.(" + productionGzipExtensions.join("|") + ")$"
            ),
            threshold: 10240,
            deleteOriginalAssets: false
          }),
          new BundleAnalyzerPlugin()
        ]
      };
    }
    return {
      externals: externals
    };
  },
  productionSourceMap: false,

  runtimeCompiler: false,
  css: {
    extract: false,
    sourceMap: false,
    loaderOptions: {
      sass: {
        data: `@import "@/assets/css/global/variable.scss";`
      }
    },
    modules: false
  },
  parallel: require("os").cpus().length > 1,
  pwa: {},
  devServer: {
    open: true,
    host: "0.0.0.0",
    port: 4016,
    https: false,
    hotOnly: false,
    proxy: {
      "/basicAPI": {
        // 云平台和云平台后台用同一个
        // target: "http://************:5002",
        // target: "http://************:5002",
        target: "http://yun.jiqinyun.com",
        // target:"http://**************:1031",

        changeOrigin: true,
        pathRewrite: {
          "^/basicAPI": "/basicAPI"
        }
      },
      "/jiqin_server": {
        // 部分功能
        target: "http:/yun.jiqinyun.com",
        changeOrigin: true,
        pathRewrite: {
          "^/jiqin_server": "/jiqin_server"
        }
      },
      "/internalSystemAPI": { // 内部系统
        target: "http://***********:5311",
        //target: "http://jiqin.jiqinyun.com",
        changeOrigin: true,
        pathRewrite: {
          "^/internalSystemAPI": "/internalSystemAPI"
        }
      },
 
    },
    // eslint-disable-next-line no-unused-vars
    before: app => {}
  },
  pluginOptions: {}
};
