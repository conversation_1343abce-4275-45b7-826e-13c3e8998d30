import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}copyPushLog/query`,
      params
    );
  },
  // 新增
  save: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}copyPushLog/save`,
      params
    );
  },
  // 获取单条记录客户
  getCopyPushDetail: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}copyPushLog/getCopyPushDetail`,
      params
    );
  },
  // //满意度反馈明细
  // customerPushVisitLog: (params) => {
  //   return Axios.post(
  //     `${environment.internalSystemAPI}copyPushLog/customerPushVisitLog`,
  //     params
  //   );
  // },
   // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}copyPushLog/remove`, params)
  },

};
