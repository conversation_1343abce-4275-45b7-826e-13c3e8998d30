import API from "@/api/internalSystem/bugManage/module/index.js";
import {mapGetters} from 'vuex'
export default {
  props:{
    brand_id:{
      
      default:""
    }
  },
  data() {
    return {
      dialogVisible: false,
      title: "新增",
      ruleForm: {
      
        module_name:'',
        brand_id:'',
        module_id:"",
        parent_id:"",
        parent_name:""
      },
      rules: {
        module_name: [{
          required: true,
          message: "请输入模块名称",
          trigger: "blur"
        }],
     
       
        parent_id: [{
          required: true,
          message: "请选择上级菜单",
          trigger: "blur"
        }],
        brand_id: [{
          required: true,
          message: "请选择对应产品",
          trigger: "blur"
        }],
      
      }
    };
  },
  computed: {
    ...mapGetters(['params_constant_menu_type'])
  },
  methods: {
    //获取单个菜单
    getMenu(id) {
      API.getInfo({
          module_id: id
        })
        .then(data => {
          this.ruleForm = data.data;
          if (data.data.parentId == 0) this.ruleForm.parent_name = "无上级菜单";
        })
        .catch(() => {})
        .finally(() => {});
    },
    Show(menuObject = {
      parent_id: "",
      parent_name: ""
    }) {
      //弹窗显示
      this.dialogVisible = true;
      this.ruleForm.parent_id = menuObject.parent_id;
      this.ruleForm.parent_name = menuObject.parent_name;
      this.ruleForm.level = menuObject.level;
      if (!menuObject.module_id) {
        this.title = "新增";
      } else {
        this.title = "修改";
        this.getMenu(menuObject.module_id);
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let apiName=this.ruleForm.module_id?'update':'add';
          
          let params={}
          
          Object.assign(params,this.ruleForm);
          params.brand_id=params.brand_id||this.brand_id;
          // if (this.ruleForm.module_id) {
          //   API.update(this.ruleForm)
          //     .then(() => {
          //       this.closeDialog();
          //     })
          //     .catch(() => {})
          //     .finally(() => {});
          // } else {
          //   API.add(this.ruleForm)
          //     .then(() => {
          //       this.closeDialog();
          //     })
          //     .catch(() => {})
          //     .finally(() => {});
          // }

          API[apiName](params)
          .then(() => {
            this.closeDialog();
            this.$emit("getList")
          })
          .catch(() => {})
          .finally(() => {});


        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    closeDialog() {
      this.dialogVisible = false
      this.resetForm("ruleForm");
      this.clearData();
      this.$emit("getMenuTree");
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        module_name:'',
        brand_id:'',
        module_id:"",
        parent_id:"",
        parent_name:""
      };
    }
  }
};