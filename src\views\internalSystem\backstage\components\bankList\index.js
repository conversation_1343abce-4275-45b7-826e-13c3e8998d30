import API from '@/api/internalSystem/financialManage/bankAccout'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
export default {
  name: "bankList",
  components: {
    TableView,
    Pagination
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      tableList: [{
          name: "银行编码",
          value: "bank_code",
          width: 70
        },
        {
          name: "银行名称",
          value: "bank_name"
        },
        {
          name: "户名",
          value: "accout_name",
          width: 60
        },
        {
          name: "账号",
          value: "bank_accout",
          width: 140
        },
        {
          name: "企业单位",
          value: "sales_unit_id_format",
          width: 120
        },
        {
          name: "备注",
          value: "remark",
          width: 120
        }
      ]
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "银行列表"
    },
    fk_sales_unit_id: {
      type: Number
    }
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.getList();
        });
      // }, 300);
    },
    getList() {
      this.loading = true;
      let param = Object.assign(this.$refs.brand_pagination.obtain());
      if (this.fk_sales_unit_id)
        param.fk_sales_unit_id = this.fk_sales_unit_id
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.brand_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    //提交
    submitForm() {
      if (this.selectRecords.length != 1)
        return this.error("请选择一条记录");
      this.$emit("getInfo", this.selectRecords[0]);
      this.dialogCancel();
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.selectRecords = [];
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
    rowDblclick(row, column, event){
      this.selectRecords=[]
      this.selectRecords.push(row)
      this.submitForm()
    }
  }
};