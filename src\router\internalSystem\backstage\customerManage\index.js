export default [{
  path: 'customerManage',
  name: 'customerManage',
  title: '客户管理',
  icon: 'home',
  show: true,
  meta: {
    keepAlive: false
  },
  component: resolve =>
    require([
      "@/views/internalSystem/backstage/components/view/view.vue"
    ], resolve),
  children: [{
      path: 'customerInfo',
      name: 'customerInfo',
      title: '客户列表',
      meta: {
        title: "客户列表"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/customerInfo/index.vue')
    },
    {
      path: 'customerBrand',
      name: 'customerBrand',
      title: '客户产品列表',
      meta: {
        title: "客户产品列表"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/customerBrand/index.vue')
    },
    {
      path: 'customerUpdate',
      name: 'customerUpdate',
      title: '客户修改单',
      meta: {
        title: "客户修改单"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/customerUpdate/index.vue')
    },
    {
      path: 'customerIntention',
      name: 'customerIntention',
      title: '客户意向单',
      meta: {
        title: "客户意向单"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/customerIntention/index.vue')
    },
    {
      path: 'salesVisiting',
      name: 'salesVisiting',
      title: '客户回访单',
      meta: {
        title: "客户回访单"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/salesVisiting/index.vue')
    },
    {
      path: 'customerPush',
      name: 'customerPush',
      title: '客户回访单',
      meta: {
        title: "客户回访单"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/customerPush/index.vue')
    },
    {
      path: 'satisfactionVisitLog',
      name: 'satisfactionVisitLog',
      title: '满意度调查表',
      meta: {
        title: "满意度调查表"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/satisfactionVisitLog/index.vue')
    },
    {
      path: 'implementation',
      name: 'implementation',
      title: '客户实施单',
      meta: {
        title: "客户实施单"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/implementation/index.vue')
    },
    {
      path: 'highSeasCustomer',
      name: 'highSeasCustomer',
      title: '公海客户',
      meta: {
        title: "公海客户"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/highSeasCustomer/index.vue')
    },
    {
      path: 'copyPushLog',
      name: 'copyPushLog',
      title: '文案推送管理',
      meta: {
        title: "文案推送管理"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/copyPushLog/index.vue')
    },
    {
      path: 'auditCenter',
      name: 'auditCenter',
      title: '审核中心',
      meta: {
        title: "审核中心"
      },
      component: () => import('@/views/internalSystem/backstage/auditCenter/index.vue')
    },
    {
      path: 'article',
      name: 'article',
      title: '文案管理',
      meta: {
        title: "文案管理"
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/article/index.vue')
    },
    {
      path: "customerQuestionCollect",
      name: "customerQuestionCollect",
      title: "客户文案搜索查询",
      meta: {
        title: "客户文案搜索查询",
      },
      component: () => import('@/views/internalSystem/backstage/customerManage/customerQuestionCollect/index.vue')
    },

    // {
    //   path: 'satisfaction',
    //   name: 'satisfaction',
    //   title: '满意度调查',
    //   meta: {
    //     title: "满意度调查"
    //   },
    //   component: () => import('@/views/internalSystem/backstage/customerManage/satisfaction/index.vue')
    // }
  ]
}]