import axios from "axios";
import { Message } from "element-ui";
import environment from "@/store/userInfo.js";
// import Cookies from "js-cookie";
const service = axios.create({
  timeout: 60 * 1000, // 响应时间
  withCredentials: true
});
service.defaults.transformRequest = [
  function(data) {
    let ret = new FormData();
    for (let it in data) {
      if (data[it] === null) {
        // 过滤null,后端会接收到"null"
        ret.append(it, "");
      } else if (data[it] instanceof Array) {
        ret.append(it, JSON.stringify(data[it]));
      } else {
        ret.append(it, data[it]);
      }
    }
    let companyId = environment.state.userInfo.companyId;
    if (!data.companyId) {
      ret.append("companyId", companyId);
    }
    return ret;
  }
];

service.interceptors.response.use(
  res => {
    if (res.data.code === 0) {
      Message.error(res.data.msg);
      return Promise.reject(res.data);
    }
    return Promise.resolve(res.data);
  },
  err => {

    Message.error("服务器开小差了,请稍后再试。");
    return Promise.reject(err);
  }
);

export default service;
