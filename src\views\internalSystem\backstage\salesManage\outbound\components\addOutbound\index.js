import API from "@/api/internalSystem/salesManage/outbound";
import cusAPI from "@/api/internalSystem/customerManage/customerInfo";
import ContractDetailList from "@/views/internalSystem/backstage/components/contractDetailList/index.vue";
import TableView from "./../tableView/index.vue";
import TableView2 from "./../tableView2/index.vue";
import ContractList from "@/views/internalSystem/backstage/components/contractList/index.vue";

import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";

export default {
  name: "addOutbound",
  components: {
    ContractDetailList,
    TableView,
    TableView2,
    MyDate,
    ContractList,
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_name: "",
        customer_no: "",
        remark: "",
        customer_contract_id: "",
      },
      loading: false,
      tableData: [],
      tableData2: [],
      tableList: [
        {
          name: "合同单号",
          value: "contract_no",
        },
        {
          name: "产品服务",
          value: "brandName",
        },
        {
          name: "合同数量",
          value: "contract_count",
        },
        {
          name: "计量单位",
          value: "measurement_unit_name",
        },
        {
          name: "出库数量",
          value: "stock_removal_count",
        },
        {
          name: "出库金额",
          value: "contract_amount",
        },
        {
          name: "原有端口数",
          value: "original_port_count",
        },
        {
          name: "新增端口数",
          value: "add_port_count",
        },
        {
          name: "新维护结束时间",
          value: "new_maintain_stop_time",
          width: 120,
        },
      ],
      tableList2: [
        {
          name: "编号",
          value: "contract_no",
          width: 112,
        },
        {
          name: "客户名称",
          value: "customer_name",
          width: 130,
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 70,
        },
        {
          name: "已出库金额",
          value: "outbound_amount",
          width: 82,
        },
        {
          name: "已开票金额",
          value: "open_ticket_amount",
          width: 82,
        },
      ],
      contract_detail_ids: [],
      customer_contract_ids: [],
      tableFlag: 1,
    };
  },
  methods: {
    check() {
      this.$confirm("是否同步端口数？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        API.updateEndState({
          outbound_id: this.ruleForm.outbound_id,
        }).then((res) => {
          this.success("同步成功");
          this.ruleForm.end_state = 1;
        });
      });
    },
    Show(data = null) {
      this.tableFlag = 1
      this.dialogVisible = true;
      this.contract_detail_ids = [];
      if (data) {
        this.ruleForm = data;
        this.contract_detail_ids = data.fk_contract_detail_ids.split(",");
        API.detailList({
          fk_contract_detail_ids: data.fk_contract_detail_ids,
          outbound_id: data.outbound_id,
        })
          .then((res) => {
            this.tableData = res.data;
          })
          .catch(() => {})
          .finally(() => {});
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    /**
     *
     * @param {*} flag1 是否需要关闭弹窗
     * @param {*} flag2 是否要清空页面数据
     */
    dialogCancel(flag1 = true, flag2 = true) {
      this.isOperation = true;
      if (flag1) {
        this.dialogVisible = false;
        this.$emit("selectData");
      }
      if (flag2) {
        this.resetForm("ruleForm");
        this.clearData();
      }
    },
    async batchSave() {

      let batchData = this.$refs.tableView2.getData();
      if (batchData.length == 0) {
        return this.error("至少要有一条数据");
      }
      if (batchData.length > 51) {
        return this.error("一次保存50条以内");
      }
      this.loading = true;
      const expLoading = this.$loading({
        lock: true,
        text: "正在保存数据，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      for (let index = 0; index < batchData.length; index++) {

        let info = batchData[index];
        let { data } = await API.getDetails({
          customer_contract_id: info.customer_contract_id,
        });

        let params = {};
        let detailIds = [];
        data.forEach((item) => {
          detailIds.push(item.contract_detail_id);
          item.outbound_remark = item.outbound_remark || "";
        });

        let ids = Array.from(new Set(detailIds));
        params["customer_no"] = info.customer_no;
        params["customer_name"] = info.customer_name;
        params["customer_contract_id"] = info.customer_contract_id;
        params["fk_customer_id"] = info.fk_customer_id;
        params["remark"] = info.remark || "";
        params.fk_contract_detail_ids = ids.join(",");
        params.idAndSoftwareNo = data;

        await API.add(params).catch((res) => {
          return this.error(res);
        });
      }
      this.loading = false
      this.addNew()
      this.tableData = []
      expLoading.close();
    },
    save() {

      let params = this.ruleForm;
      this.tableData = this.$refs.tableView.getData();
      if (this.tableData.length == 0) {
        return this.error("至少要有一条数据");
      }
      let flag01 = true; //判断收款金额
      this.tableData.forEach((item) => {
        //针对赠送端口，金额可以是0
        if (![8, 3].includes(item.detail_sell_type)) {
          if (parseFloat(item.amount) <= 0) {
            flag01 = false;
            return;
          }
          if (parseFloat(item.amount) > parseFloat(item.no_contract_amount)) {
            flag01 = false;
            return;
          }
        }
      });
      if (!flag01) {
        this.error("出库金额有误，请检查后保存");
        return;
      }
      let detailIds = [];
      this.tableData.forEach((item) => {
        detailIds.push(item.contract_detail_id);
        item.outbound_remark = item.outbound_remark || "";
      });

      let ids = Array.from(new Set(detailIds));

      params.fk_contract_detail_ids = ids.join(",");
      params.idAndSoftwareNo = this.tableData;

      this.loading = true;
      // if (params.outbound_id) {
      //   API.update(params)
      //     .then(() => {
      //       this.dialogCancel(false, false);
      //     })
      //     .catch(() => {}).finally(() => {
      //       this.loading = false;
      //     });
      // } else {
      API.add(params)
        .then(() => {
          this.dialogCancel(false, true);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
      // }
    },
    del(info = {}) {
      let delIndex = "";
      this.contract_detail_ids.map((item, index) => {
        if (item === info.contract_detail_id) {
          delIndex = index;
        }
      });
      this.contract_detail_ids.splice(delIndex, 1);
      let tDelIndex = "";
      this.tableData.map((item, index) => {
        if (item.contract_detail_id === info.contract_detail_id) {
          tDelIndex = index;
        }
      });
      this.tableData.splice(tDelIndex, 1);
      if (this.tableData.length == 0) {
        this.resetForm("ruleForm");
        this.clearData();
      }
    },
    //调入合同
    callIn() {
      this.$refs.contractDetailList.Show();
    },
    //选择合同
    chooseContract() {
      this.tableFlag = 1;
      this.$refs.contractList.Show();
    },
    chooseContract2() {
      this.tableFlag = 2;
      this.$refs.contractList2.Show();
    },
    getContract(info = []) {
      this.contract_detail_ids.push(info.contract_detail_id);
      if (!this.ruleForm.customer_no) {
        cusAPI
          .getInfo({
            customer_id: info.fk_customer_id,
          })
          .then((res) => {
            this.ruleForm.customer_no = res.data.customer_no;
            this.ruleForm.customer_name = res.data.customer_name;
          })
          .catch(() => {})
          .finally(() => {});
      }
      this.tableData.push(info);
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_name: "",
        customer_no: "",
        remark: "",
        customer_contract_id: "",
      };
      this.tableData = [];
      this.tableData2 = []
      this.customer_contract_ids = [];
      this.contract_detail_ids = [];
      // this.customer_contract_id
    },
    addNew() {
      this.dialogCancel(false, true);
    },
    //根据合同id，查找未出库的产品明细
    async getContractInfo(info = []) {
      this.ruleForm.customer_contract_id = info[0].customer_contract_id;
      this.ruleForm.fk_customer_id = info[0].fk_customer_id;
      this.customer_contract_ids.push(info[0].customer_contract_id);
      let { data } = await API.getDetails({
        customer_contract_id: info[0].customer_contract_id,
      });
      for (let i = 0; i < data.length; i++) {
        this.getContract(data[i]);
      }
    },
    async getContractInfo2(info = []) {
      info.forEach(item=>{
        item['remark'] = ""
      })
      this.tableData2 = info;
    },
  },
};
