
const state = {
	flatPermissions: [], // 扁平总权限数组
	curRoutePermissions: [] // 页面权限
}
const getters = {
	flatPermissions: state => state.flatPermissions || [],
	curRoutePermissions: state => state.curRoutePermissions
}
const mutations = {
	SET_FlATPERMISSIONS(state, arr = []) {
		state.flatPermissions = arr
	},
	SET_CURROUTEPERMISSIONS(state, arr = []) {
		state.curRoutePermissions = arr
	},
}

// json树扁平化
const flatten = data => {
	return data.reduce((arr, {menuId, menuName, parentId, menuDescribe, menuUrl, type, children = []}) =>
    arr.concat([{menuId, menuName, parentId, menuDescribe, menuUrl, type}], flatten(children)), []);
}

const actions = {
	setFlatPermissions: ({ commit }, arr = []) => {
		commit('SET_FlATPERMISSIONS', flatten(arr))
	},
	// 设置页面权限
	setCurRoutePermissions: ({getters, commit}, path = '') => {
		//新版合同单与旧版共享权限
		if(path==='/backstage/salesManage/newContract') path='/backstage/salesManage/contract'
		const permissions = getters.flatPermissions

		let arr = []
		if (permissions.length > 0) {
			const curRoute = permissions.find(item => item.menuUrl === path)

			if (!curRoute) return

			arr = permissions.filter(item => item.parentId === curRoute.menuId)
	
		}
		commit('SET_CURROUTEPERMISSIONS', arr)
	}
}
export default {
	state,
	getters,
	mutations,
	actions
}
