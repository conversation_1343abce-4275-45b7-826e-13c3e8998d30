<template>
  <div class="body-p10">
    <el-form
      :inline="true"
      :model="formSearch"
      size="small"
      v-if="!isAdd && !isAudit"
    >
      <el-form-item label="查询条件">
        <el-select
          v-model="formSearch.publish_man"
          placeholder="请选择发布人"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
          :disabled="!['总经理'].includes(cookiesUserInfo.role_name)"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_name"
            :value="item.employeeId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="formSearch.title"
          placeholder="请输入主题"
          style="width: 150px"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="formSearch.keywords"
          placeholder="请输入关键词"
          style="width: 150px"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.type"
          placeholder="请选择类型"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in typeList"
            :key="item.sysValue"
            :label="item.sysName"
            :value="item.sysValue"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.copy_state"
          placeholder="请选择审核状态"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in copy_audit"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.default_flag"
          placeholder="是否默认推送"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option label="是" :value="1"> </el-option>
          <el-option label="否" :value="2"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button type="primary" @click="add" v-permit="'ADD_ARTICLE_NEW'"
          >制单</el-button
        >
      </el-form-item>
    </el-form>
    <TableView
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd && !isAudit"
      @del="del"
      @modify="modify"
      isEdit="permanent_button"
      :isDel="'DEL_ARTICLE_NEW'"
      :isThrid="'PUSH_ARTICLE_NEW'"
      thridTitle="推送"
      @thrid="push"
      :isFive="'UPT_ARTICLE_DEFAULT_NEW'"
      fiveTitle="修改默认"
      @five="updateDefault"
      isFour="AUDIT_ARTICLE_NEW"
      :fourTitle="'审核'"
      @four="(item) => toAuditDet(item, '文案审核', 'copy_state')"
    >
    </TableView>
    <ArticleAudit ref="articleAudit" v-show="isAudit" @selectData="getList" />
    <Pagination
      ref="pagination"
      @success="getList"
      v-show="!isAdd && !isAudit"
    />
    <!-- 新增文案 -->
    <AddArticle ref="addArticle" @selectData="getList" />
    <!-- 新增文案推送 -->
    <AddCopyPush ref="addCopyPush" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.moneyTitle {
  font-size: 14px;
  font-weight: bold;
}

@import "@/assets/css/element/font-color.scss";
</style>