import Axios from "@/api";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}quotation/query`, params)
  },
  // 查询明细
  detailList: params => {
    return Axios.post(`${environment.internalSystemAPI}quotation/detailList`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}quotation/add`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}quotation/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}quotation/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}quotation/getInfo`, params)
  },
  // 审核
  updateAudit: params => {
    return Axios.post(`${environment.internalSystemAPI}quotation/updateAudit`, params)
  }
};