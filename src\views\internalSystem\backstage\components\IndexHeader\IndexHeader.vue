<template>
  <!--<div class="header">
    <div class="header-left">
      <div class="header-title">在线订货后台管理</div>
      <div class="welcome-info">
        <span v-if="phone">{{phone}}，</span>
        欢迎使用
      </div>
    </div>
    <div class="button-logout">
      <i class="iconfont">&#xe650;</i>
    </div>
  </div>-->
  <div class="top-tab-bar">
    <!-- <div class="logo-admin">
      <img :src="Img.logoImg" />
    </div>-->
    <div
      class="top-user-info"
      :class="[homeIndent ? 'top-user-info-indent' : '']"
    >
      <el-dropdown @command="handleCommand">
        <div class="cur-container">
          <div class="user-head-img">
            <img :src="Img.imgHead" style="height: 32px;width: 32px" />
          </div>
          <p class="top-user-name">{{ userInfo.fullName }}</p>
          <i class="el-icon-caret-bottom"></i>
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="/backstage/basicManage/updatepassword"
            >修改密码</el-dropdown-item
          >
          <el-dropdown-item command="/backstage/basicManage/loginOut"
            >退出系统</el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
<!-- 
      <i
        class=" foldIcon"
        :class="[!homeIndent ? 'el-icon-s-fold' : 'el-icon-s-unfold']"
        :style="{marginLeft:homeIndent?'-25px':'0'}"
        :title="homeIndent ? '点击展开' : '点击缩进'"
        @click="indent"
      ></i> -->
    </div>
    <div class="right-bar">
      <h1 class="top-left-company-name"></h1>
      <div class="top-right">
        <!-- <div class="top-user-info">
          <div class="user-head-img">
            <img :src="Img.imgHead" style="height: 32px;width: 32px" />
          </div>
          <p class="top-user-name">{{userInfo.fullName}}</p>
          <i class="el-icon-caret-bottom"></i>
        </div>-->
        <!--<div class="top-link-gkt">-->
        <!--<img src="../../assets/img/logo-gkt.png" height="22" width="32" />-->
        <!--<p>工控通</p>-->
        <!--<span>search</span>-->
        <!--</div>-->
        <ul class="top-tool-bar">
          <!-- <li>
            <i class="el-icon-switch-button" @click="signOut"></i>
          </li> -->
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import API from "@/api/internalSystem/common/index";
import Img from "@/assets/images/internalSystem/index";
import { mapGetters, mapMutations } from "vuex";
export default {
  name: "IndexHeader",
  data() {
    return {
      Img: Img,
    };
  },
  computed: {
    ...mapGetters(["userInfo", "homeIndent"]),
  },
  methods: {
    ...mapMutations(["SET_HOMEINDENT"]),
    indent() {
      //缩进
      let homeIndent = !this.homeIndent;
      this.SET_HOMEINDENT(homeIndent);
    },
    // 获取url参数
    getQueryString(name) {
      let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
      let r = window.location.search.substr(1).match(reg);
      if (r != null) {
        return unescape(r[2]);
      } else {
        return null;
      }
    },
    // 退出
    signOut() {
      this.$confirm("此操作将退出系统, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          API.loginOut({}).then(() => {
            window.location.href = window.location.pathname;
          });
        })
        .catch(() => {});
    },
    handleCommand(command) {
      if (command === "/backstage/basicManage/updatepassword") {
        this.$router.push(command);
      } else if (command === "/backstage/basicManage/loginOut") {
        this.signOut();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.foldIcon {
  float: right;
  margin-right: 10rpx;
  font-size: 18px;
  padding: 0 10px;
  color: #ccc;
  cursor: pointer;
}
.top-tab-bar {
  font-size: 14px;
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;

  .logo-admin {
    width: 200px;
    height: 100%;
    text-align: center;
    border-bottom: 2px solid #6c7580;
    background-color: #40464d;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .top-user-info {
    width: 200px;
    height: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-bottom: 2px solid #6c7580;
    background-color: #40464d; //    #013B80
    // transition: all 0.1s ease;
    .cur-container {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all .1s;
      .user-head-img {
        width: 32px;
        height: 32px;
        background: #eee;
        border: 1px solid #ccc;
        border-radius: 16px;
        overflow: hidden;
        display: none;
        img {
          vertical-align: middle;
        }
      }

      .top-user-name {
        color: #ccc;
        padding: 0 10px;
      }

      .el-icon-caret-bottom,
      .el-icon-s-fold {
        color: #ccc;
      }
    }
  }
  .top-user-info-indent {
    width: 90px;
    // .cur-container {
    //   display: none !important;
    // }
  }
  img {
    width: 160px;
  }

  .right-bar {
    height: 0%;
    border-bottom: 1px solid #ebebeb;
    box-shadow: 3px 2px 4px #e6e6e6;
    padding: 0 20px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .top-left-company-name {
      color: #333;
      font-size: 18px;
    }

    .top-right {
      display: flex;
      align-items: center;

      .top-user-info {
        margin-right: 15px;
        display: flex;
        align-items: center;

        .user-head-img {
          width: 32px;
          height: 32px;
          background: #eee;
          border: 1px solid #ccc;
          border-radius: 16px;
          overflow: hidden;

          img {
            vertical-align: middle;
          }
        }

        .top-user-name {
          color: #888;
          padding: 0 10px;
        }
      }

      .top-link-gkt {
        &:hover {
          background-color: #f5f5f5;
        }

        position: relative;
        margin-right: 20px;
        display: flex;
        align-items: center;
        width: 100px;
        height: 32px;
        border-radius: 4px;
        background: #eee;
        padding-left: 10px;
        transition: all 0.3s;
        cursor: pointer;

        p {
          font-size: 16px;
          padding: 5px 0 0 3px;
          margin: 0;
          color: #1a9fd3;
        }

        span {
          position: absolute;
          right: -5px;
          top: -8px;
          display: block;
          width: 54px;
          height: 16px;
          border: 2px solid #fff;
          border-radius: 8px;
          background: #ff5353;
          font-size: 12px;
          line-height: 14px;
          text-align: center;
          color: #fff;
          font-weight: bold;
        }
      }

      .top-tool-bar {
        li {
          .iconfont {
            font-size: 18px;
            color: #999;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
