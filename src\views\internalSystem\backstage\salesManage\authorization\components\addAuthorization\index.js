import API from "@/api/internalSystem/salesManage/authorization";
import cusAPI from "@/api/internalSystem/customerManage/customerInfo";
import ContractDetailList from "@/views/internalSystem/backstage/components/contractDetailList/index.vue";
import CustomerBrandList from "@/views/internalSystem/backstage/components/CustomerBrand/index.vue";
import TableView from "./../tableView/index.vue";
import TableView2 from "./../tableView2/index.vue";
import ContractList from "@/views/internalSystem/backstage/components/contractList/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { dateFormat } from "@/common/internalSystem/common.js";
export default {
  name: "addAuthorization",
  components: {
    ContractDetailList,
    TableView,
    TableView2,
    MyDate,
    ContractList,
    CustomerBrandList
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_name: "",
        customer_no: "",
        remark: "",
        customer_contract_id: "",
      },
      loading: false,
      tableData: [],
      tableData2:[],
      contract_detail_ids: [],
      customer_contract_ids: [],
      contractDetailIds: [],
      customerIds: null,
      authorizationFlag:"customerContract"// 默认按照合同授权
    };
  },
  methods: {
        /**
     * 客户产品信息表选择
     * @param {*} info 勾选产品
     * @param {*} customerIds  客户id，判重用
     * @param {*} contractDetailIds 产品id，判重用
     */
      async getCustomerBrandInfo(
          info = [],
          customerIds = null,
          contractDetailIds = []
        ) {
          this.customerIds = customerIds;
          this.contractDetailIds = contractDetailIds;
         info.map((item) => {
            item["original_port_count_new"] = item["original_port_count"];
            item["original_port_count_old"] = item["original_port_count"];
            item["new_maintain_stop_time_new"] = item["new_maintain_stop_time"];
            item["new_maintain_stop_time_old"] = item["new_maintain_stop_time"];
          });

            cusAPI
              .getInfo({
                customer_id: info[0].fk_customer_id,
              })
              .then((res) => {
                this.ruleForm.customer_no = res.data.customer_no;
                this.ruleForm.customer_name = res.data.customer_name;
              })
              .catch(() => {})
              .finally(() => {});
          this.ruleForm.fk_customer_id = info[0].fk_customer_id;
           this.tableData2 =JSON.parse(JSON.stringify(info))
        },
    chooseCustomerBrand(){
      if(this.authorizationFlag != "customerBrand"){
        this.addNew()
      }
      this.authorizationFlag = "customerBrand"
      this.$refs.customerBrandList.Show();
    },
    check() {
      this.$confirm("是否同步端口数？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        API.updateEndState({
          authorization_id: this.ruleForm.authorization_id,
        }).then((res) => {
          this.success("同步成功");
          this.ruleForm.end_state = 1;
        });
      });
    },
    Show(data = null) {
      this.dialogVisible = true;
      this.contract_detail_ids = [];
      if (data) {
        this.ruleForm = data;
        if(data.authorization_flag == 1){
          this.authorizationFlag = "customerContract"
          this.contract_detail_ids = data.fk_contract_detail_ids.split(",");
          API.detailList({
            fk_contract_detail_ids: data.fk_contract_detail_ids,
            authorization_id: data.authorization_id,
          })
            .then((res) => {
                this.tableData = res.data;
              if (this.tableData && this.tableData.length > 0) {
                this.ruleForm.customer_contract_id = this.tableData[0][
                  "fk_customer_contract_id"
                ];
              }
            })
            .catch(() => {})
            .finally(() => {});
        }else{
          this.authorizationFlag = "customerBrand"
          API.detailList2({
            authorization_id: data.authorization_id,
          })
            .then((res) => {
                this.tableData2 = res.data;
              if (this.tableData && this.tableData.length > 0) {
                this.ruleForm.customer_contract_id = this.tableData[0][
                  "fk_customer_contract_id"
                ];
              }
            })
            .catch(() => {})
            .finally(() => {});
        }

      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if(this.authorizationFlag=="customerContract"){
            this.save();
          }else{
            this.save2();
          }
      
        } else {
          return false;
        }
      });
    },
    /**
     *
     * @param {*} flag1 是否需要关闭弹窗
     * @param {*} flag2 是否要清空页面数据
     */
    dialogCancel(flag1 = true, flag2 = true) {
      if (flag1) {
        this.dialogVisible = false;
        this.$emit("selectData");
      }
      if (flag2) {
        this.resetForm("ruleForm");
        this.clearData();
      }
    },
    save2(){
      let params = this.ruleForm;
      params['authorization_flag'] = this.authorizationFlag=="customerContract"?1:2
      this.tableData = this.$refs.tableView2.getData();
      if (this.tableData.length == 0) {
        return this.error("至少要有一条数据");
      }
      let detailIds = [];
      this.tableData.forEach((item) => {
        detailIds.push(item.contract_detail_id);
        item.authorization_remark = item.authorization_remark || "";
        item.software_no = item.software_no || "";
      });

      let ids = Array.from(new Set(detailIds));

      params.fk_contract_detail_ids = ids.join(",");
      params.idAndSoftwareNo = this.tableData;

      this.loading = true;
      API.add2(params)
        .then(() => {
          this.dialogCancel(false, true);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    save() {
      let params = this.ruleForm;
      params['authorizationFlag'] = this.authorizationFlag=="customerContract"?1:2
      this.tableData = this.$refs.tableView.getData();
      if (this.tableData.length == 0) {
        return this.error("至少要有一条数据");
      }
      let detailIds = [];
      this.tableData.forEach((item) => {
        detailIds.push(item.contract_detail_id);
        item.authorization_remark = item.authorization_remark || "";
        item.software_no = item.software_no || "";
        item.authorization_maintain_stop_time = dateFormat(
          "yyyy-MM-dd",
          new Date(item.authorization_maintain_stop_time)
        );
      });

      let ids = Array.from(new Set(detailIds));

      params.fk_contract_detail_ids = ids.join(",");
      params.idAndSoftwareNo = this.tableData;

      this.loading = true;
      API.add(params)
        .then(() => {
          this.dialogCancel(false, true);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    del(info = {}) {
      let delIndex = "";
      this.contract_detail_ids.map((item, index) => {
        if (item === info.contract_detail_id) {
          delIndex = index;
        }
      });
      this.contract_detail_ids.splice(delIndex, 1);
      let tDelIndex = "";
      this.tableData.map((item, index) => {
        if (item.contract_detail_id === info.contract_detail_id) {
          tDelIndex = index;
        }
      });
      this.tableData.splice(tDelIndex, 1);
      if (this.tableData.length == 0) {
        this.resetForm("ruleForm");
        this.clearData();
      }
    },
    //调入合同
    callIn() {
      this.$refs.contractDetailList.Show();
    },
    //选择合同
    chooseContract() {
      if(this.authorizationFlag != "customerContract"){
        this.addNew()
      }
      this.authorizationFlag = "customerContract"
      this.$refs.contractList.Show();
    },
    getContract(info = {}) {
      this.contract_detail_ids.push(info.contract_detail_id);
      if (!this.ruleForm.customer_no) {
        cusAPI
          .getInfo({
            customer_id: info.fk_customer_id,
          })
          .then((res) => {
            this.ruleForm.customer_no = res.data.customer_no;
            this.ruleForm.customer_name = res.data.customer_name;
          })
          .catch(() => {})
          .finally(() => {});
      }
      this.tableData.push(info);
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_name: "",
        customer_no: "",
        remark: "",
        customer_contract_id: "",
      };
      this.tableData2 = [];
      this.tableData = [];
      this.customer_contract_ids = []
      this.contract_detail_ids = []
      this.contractDetailIds =  []
      this.customerIds = null

    },
    addNew() {
      this.dialogCancel(false, true);
    },
    //根据合同id，查找未出库的产品明细
    async getContractInfo(info = []) {
      // await API.checkContractInfo({
      //   fk_customer_contract_id: info[0].customer_contract_id,
      // });

      this.ruleForm.customer_contract_id = info[0].customer_contract_id;
      this.ruleForm.fk_customer_id = info[0].fk_customer_id;
      this.ruleForm.remark = info[0].remark;
      this.customer_contract_ids.push(info[0].customer_contract_id);
      let { data } = await API.getDetails({
        customer_contract_id: info[0].customer_contract_id,
        fk_customer_id: info[0].fk_customer_id,
      });
      for (let i = 0; i < data.length; i++) {
        this.getContract(data[i]);
      }
    },
    jumpContract() {

      if (!this.ruleForm.customer_contract_id) {
        return;
      }
      this.$router.push({
        // path: "/backstage/salesManage/contractAdd",
        path: "/backstage/salesManage/contract",
        query: {
          customer_contract_id: this.ruleForm.customer_contract_id,
          type: "authorization",
        },
      });
    },
  },
};
