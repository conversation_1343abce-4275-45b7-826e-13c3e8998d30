import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}menu/query`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}menu/add`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}menu/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}menu/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}menu/getInfo`, params)
  },
  // 获取菜单列表
  queryAll: params => {
    return Axios.post(`${environment.internalSystemAPI}menu/queryAll`, params)
  },
  // 查询当前用户能获取的菜单列表
  queryAllRole: params => {
    return Axios.post(`${environment.internalSystemAPI}menu/queryAllRole`, params)
  },
  // 查询当前用户能获取的菜单列表
  queryAllRoleList: params => {
    return Axios.post(`${environment.internalSystemAPI}menu/queryAllRoleList`, params)
  },
  // 获取单个菜单是否有权限
  getInfoRole: params => {
    return Axios.post(`${environment.internalSystemAPI}menu/getInfoRole`, params)
  }
};