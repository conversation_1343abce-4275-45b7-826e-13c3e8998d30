import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}customerPushLog/query`,
      params
    );
  },
  // 新增
  save: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}customerPushLog/save`,
      params
    );
  },
  // 获取单条记录客户
  getCustomerPushDetail: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}customerPushLog/getCustomerPushDetail`,
      params
    );
  },
  //满意度反馈明细
  customerPushVisitLog: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}customerPushLog/customerPushVisitLog`,
      params
    );
  },
   // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}customerPushLog/remove`, params)
  },
  // // 编辑
  // update: params => {
  //   return Axios.post(`${environment.internalSystemAPI}customerPushLog/update`, params)
  // },
  // // 获取单条信息
  // getInfo: params => {
  //   return Axios.post(`${environment.internalSystemAPI}customerPushLog/getInfo`, params)
  // },
  // // 审核
  // updateAudit: params => {
  //   return Axios.post(`${environment.internalSystemAPI}customerPushLog/updateAudit`, params)
  // }
};
