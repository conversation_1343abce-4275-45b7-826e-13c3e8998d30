import API from "@/api/internalSystem/customerManage/customerInfo";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import AddCustomer from "./components/addCustomer/index.vue";
import CustomerDetail from "./components/customerDetail/index.vue";
import CustomerProduct from "./components/customerProduct/index.vue";
import CustomerTracking from "./components/customerTracking/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { getOptions, dateFormat } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";

export default {
  name: "customerInfo",
  data() {
    return {
      title: "客户基础资料",
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customerName: "",
        customerStage: "",
        fk_sale_employee_id: "",
        fk_maintain_employee_id: "",
        startTime: "",
        endTime: "",
        createTimeStart:"",
        createTimeEnd:"",
        customerNameOld:""
        // oFields: ["update_time"],
        // oTypes: ["desc"],
      },
      tableList: [
        {
          name: "客户阶段",
          value: "customer_stage_format",
          width: 100,
        },
        {
          name: "客户编码",
          value: "customer_no",
          width: 100,
        },
        {
          name: "客户名称",
          value: "customer_name",
          width: 200,
        },
        {
          name: "客户曾用名",
          value: "customer_name_old",
          width: 200,
        },
        // {
        //   name: "所有端口数",
        //   value: "port_number",
        //   width: 90,
        // },
        {
          name: "端口数",
          value: "sure_port_number",
          width: 60,
        },
        {
          name: "授权码",
          value: "software_no",
          width: 100,
        },
        // {
        //   name: "客户别名",
        //   value: "customer_name_alias",
        //   width: 160,
        // },
        // {
        // 	name: "法定名称",
        // 	value: "customer_legal_name",
        // 	width: 160
        // },

        // {
        //   name: "产品服务",
        //   value: "software_version_format",
        //   width: 100,
        // },
        // {
        // 	name: "软件版本",
        // 	value: "software_version_format",
        // 	width: 100
        // },

        // {
        // 	name: "端口数",
        // 	value: "port_number",
        // 	width: 80
        // },
        {
          name: "维护状态",
          value: "maintain_stop_state",
          width: 100,
        },
        {
          name: "软件到期日",
          value: "maintain_stop_time",
          width: 100,
        },
        {
          name: "软件总金额",
          value: "contract_amount",
          width: 100,
        },
        {
          name: "维护比例",
          value: "year_maintain_cost",
          width: 80,
        },

        // {
        //   name: "维护费",
        //   value: "contract_amount_year_maintain_cost",
        //   width: 100,
        // },

        // {
        // 	name: "销售员部门",
        // 	value: "department_name",
        // 	width: 100
        // },
        // {
        //   name: "维护结束日期",
        //   value: "maintain_stop_time",
        //   width: 110,
        // },

        {
          name: "销售员",
          value: "fk_sale_employee_id_name",
          width: 70,
        },
        {
          name: "维护员",
          value: "fk_maintain_employee_id_str",
          width: 70,
        },
        {
          name: "联系人",
          value: "link_man",
          width: 70,
        },
        // {
        //   name: "联系电话",
        //   value: "phone",
        //   width: 100,
        // },
        {
          name: "手机",
          value: "telephone",
          width: 100,
        },
        {
          name: "绑定公众号",
          value: "wechat_binding_flag",
          width: 82,
        },
        {
          name: "成交日期",
          value: "deal_time",
          width: 100,
        },
        {
          name: "备案日期",
          value: "create_time",
          width: 100,
          sortable: "custom",
        },
        // {
        // 	name: "绑定公众号",
        // 	value: "is_open_name",
        // 	width: 82
        // }
      ],
      customerStageList: [],
      employeeList: [],
      isAdd: false,
      isDetail: false,
      isTracking: false,
      isDblclick: false,
      exLoading: false,
      refreshWechatBindingStateLoading: false,
    };
  },

  mounted() {
    this.getList();
    this.getEmployee();
  },
  created() {

  },
  methods: {
    getEmployee() {
      this.$store
        .dispatch("getEmployee")
        .then((res) => {
          this.employeeList = res;
        })
        .catch(() => {});
    },
    getList(f = false) {
      this.customerStageList = getOptions("t_customer", "customer_stage");
      this.isAdd = false;
      this.isDetail = false;
      this.isTracking = false;
      this.isDblclick = false;
      this.loading = true;
      let param = Object.assign(
        this.formSearch,
        this.$refs.pagination.obtain()
      );
      if (f) param.pageNum = 1;
      param.isJurisdiction = this.permissionToCheck("all") ? 1 : 0;
      API.query(param)
        .then((res) => {
          this.tableData = res.data;
          if (this.tableData.length > 0) {
            this.tableData.map((item) => {
              //计算维护费
              item["year_maintain_cost"] = item["year_maintain_cost"]
                ? item["year_maintain_cost"] + "%"
                : 0;
            });
          }
          this.$refs.pagination.setTotal(res.totalCount);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    add() {
      this.isAdd = true;
    },
    //打开详情
    modify(item) {
      this.$refs.CustomerDetail.Show(item);
      this.isDetail = true;
    },
    del(item) {
      if (item.customer_stage === "2")
        return this.error("不允许删除已成交客户");
      let params = {
        customer_id: item.customer_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    //客户跟踪
    tracking(item) {
      this.isTracking = true;
      this.$refs.CustomerTracking.Show(item);
    },
    //推送
    push(item) {
      if (!item.open_id) return this.error("该客户未绑定公众号，推送失败");
      this.$confirm("确定给该客户推送回访单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            customer_id: item.customer_id,
            open_id: item.open_id,
            first: "吉勤软件客户回访",
            keyword1: "吉勤-" + item.fk_sale_employee_id_name,
            keyword2: dateFormat("yyyy-MM-dd"),
            remark: "感谢您的参与，祝您生活愉快",
          };
          API.sales_push(params)
            .then(() => {
              this.getList();
            })
            .catch(() => {});
        })
        .catch(() => {});
    },
    //导出客户
    exportCustomer() {
      this.exLoading = true;
      let param = this.formSearch;
      param.userId = this.userInfo.userId;
      let isJurisdiction = this.buttonPermissions.length
        ? this.existence(this.buttonPermissions, "ALL_CUSTOMER_INFO_NEW")
        : false;
      param.isJurisdiction = isJurisdiction ? 1 : 0;
      let data = {
        url: "excel/customerExport",
        data: param,
      };

      this.ws.send(JSON.stringify(data));
      this.ws.onmessage = (e) => {
        let res = JSON.parse(e.data);
        if (res.code === 1) {
          this.success(res.message);
          this.Download(res.data);
        } else this.error(res.message);
        this.exLoading = false;
      };
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
    refreshWechatBindingState() {
      this.refreshWechatBindingStateLoading = true;
      API.refreshWechatBindingState()
        .then(() => {
          this.getList();
          this.refreshWechatBindingStateLoading = false;
        })
        .finally(() => {
          this.refreshWechatBindingStateLoading = false;
        });
    },
    /**
     * 双击
     */
    rowDblclick(row, column, event) {
      this.$refs.CustomerAndProduct.Show(row);
      this.isDblclick = true;
    },
    /**
     * 排序
     * @param {*} param0
     */
    sortChange({ column, prop, order }) {
      const dic = {
        ascending: "asc",
        descending: "desc",
      };
      // this.formSearch.oFields = (prop && [prop]) || ["update_time"];
      // this.formSearch.oTypes = (order && [dic[order]]) || ["desc"];
      this.getList();
    },
    handleExcel(flag) {
      this.$confirm("是否导出数据到excel文件?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const expLoading = this.$loading({
            lock: true,
            text: "正在导出数据，请稍候...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain({newSize:50})
          );
          if(flag == 2){
            param.pageNum = 1
            param.pageSize = 999999
          }
   
          const { data } = await API.query(param);
          this.excelData = data;
           this.export2Excel(expLoading);
        })
        .catch(() => {});
    },
    export2Excel(expLoading) {
      const that = this;
      const option = {};
      option.tHeader = [
        "客户阶段",
        "客户编码",
        "客户名称",
        "端口数",
        "授权码",
        "维护状态",
        "软件到期日",
        "软件总金额",
        "维护比例",
        "销售员",
        "维护员",
        "联系人",
        "手机",
        "绑定公众号",
        "成交日期",
        "备案日期",
      ];
      option.filterVal = [
        "customer_stage_format",
        "customer_no",
        "customer_name",
        "sure_port_number",
        "software_no",
        "maintain_stop_state",
        "maintain_stop_time",
        "contract_amount",
        "year_maintain_cost",
        "fk_sale_employee_id_name",
        "fk_maintain_employee_id_str",
        "link_man",
        "telephone",
        "wechat_binding_flag",
        "deal_time",
        "create_time",

      ];
      option.name = "客户资料导出";
      require.ensure([], () => {
        const { export_json_to_excel } = require("@/utils/Export2Excel"); // 这里必须使用绝对路径
        const tHeader = option.tHeader; // 导出的表头名
        const filterVal = option.filterVal; // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        expLoading.close();
        export_json_to_excel(tHeader, data, option.name); // 导出的表格名称，根据需要自己命名
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          return v[j];
        })
      );
    },
  },

  components: {
    AddCustomer,
    CustomerDetail,
    CustomerTracking,
    Pagination,
    TableView,
    MyDate,
    CustomerProduct,
  },
  computed: {
    ...mapGetters(["buttonPermissions", "customer_stage", "ws", "userInfo","cookiesUserInfo"]),
  },
};
