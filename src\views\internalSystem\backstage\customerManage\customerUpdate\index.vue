<template>
  <div style="overflow-y: auto; overflow-x: hidden" class="body-p10">
    <el-form
      :inline="true"
      :model="formSearch"
      size="small"
      v-if="!isAdd && !isAudit"
    >
      <el-form-item label="查询条件">
        <el-input
          v-model="formSearch.customer_name"
          placeholder="请输入客户名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.audit_state"
          placeholder="请选择审核状态"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in customer_temp_audit"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button
          type="primary"
          @click="add"
          v-permit="'ADD_CUSTOMER_UPDATE_NEW'"
          >制单</el-button
        >
      </el-form-item>
    </el-form>
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      isEdit="SHOW_CUSTOMER_UPDATE_NEW"
      v-if="!isAdd && !isAudit"
      :isDel="'DEL_CUSTOMER_UPDATE_NEW'"
      @del="del"
      isThrid="CHECK_SALECUSTOMERMANAGER_NEW"
      :thridTitle="'审核'"
      @modify="modify"
      @thrid="(item) => toAuditDet(item, '客户修改审核', 'audit_state')"
    ></table-view>
    <Pagination
      ref="pagination"
      @success="getList"
      v-show="!isAdd && !isAudit"
    />
    <!-- 新增客户单 -->
    <UpCustomer
      ref="upCustomer"
      v-if="isAdd"
      @selectData="getList"
      :customerTempId="customerTempId"
    />
    <!-- 审核 -->
    <UpdateAudit ref="updateAudit" v-show="isAudit" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>