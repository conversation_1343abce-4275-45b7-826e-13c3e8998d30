import API from '@/api/internalSystem/basicManage/costDetails'
import projectAPI from '@/api/internalSystem/basicManage/costProject'
export default {
  name: "addCostDetails",
  data() {
    return {
      dialogTitle: "新增费用明细",
      dialogVisible: false,
      ruleForm: {
        cost_detail_no: "",
        cost_detail_name: "",
        fk_financial_cost_project_id: "",
        cost_description: ""
      },
      rules: {
        cost_detail_no: [{
          required: true,
          message: "请输入费用明细编码",
          trigger: "blur"
        }],
        cost_detail_name: [{
          required: true,
          message: "请输入费用明细名称",
          trigger: "blur"
        }],
        cost_description: [{
          required: true,
          message: "请输入明细说明",
          trigger: "blur"
        }],
        fk_financial_cost_project_id: [{
          required: true,
          message: "请选择费用项目",
          trigger: "blur"
        }]
      },
      costProjectList: []
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      projectAPI.query({}).then(res => {
        this.costProjectList = res.data;
      }).finally(() => {});
      if (!data) {
        this.dialogTitle = "新增费用明细";
      } else {
        this.dialogTitle = "修改费用明细";
        this.ruleForm = data;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      if (params.financial_cost_details_id) {
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        cost_detail_no: "",
        cost_detail_name: "",
        fk_financial_cost_project_id: "",
        cost_description: ""
      }
    }
  }
};