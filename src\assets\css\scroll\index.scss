.scroll-container {
  overflow-y: auto !important;
  overflow-x: auto !important;
    &::-webkit-scrollbar {
      width: 8px;
      border-radius: 50%;
      height: 14px;
    }
    &::-webkit-scrollbar-corner, /* 滚动条角落 */
    &::-webkit-scrollbar-thumb,
    &::-webkit-scrollbar-track {
      border-radius: 4px;
    }
    &::-webkit-scrollbar-corner,
    &::-webkit-scrollbar-track {
      /* 滚动条轨道 */
      background-color: rgba(120, 180, 138, 0.1);
      box-shadow: inset 0 0 1px rgba(180, 160, 120, 0.5);
    }
    &::-webkit-scrollbar-thumb {
      /* 滚动条手柄 */
      background-color: rgba(77, 77, 77,0.3);
      
    }
   }