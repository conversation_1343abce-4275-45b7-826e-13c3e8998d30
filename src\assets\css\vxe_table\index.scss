/* 表格编辑背景色 */
.col-notEdit {
  background-color: rgb(245, 247, 250) !important;
}

//非编辑行样式
.row-notEdit {
  background-color: rgb(245, 247, 250);
}

.reconciliation {
  //对账单已对账
  background-color: #A1BFDE !important;

  // color: white;
}

/* 表格编辑背景色 */
.col-edit {
  background-color: rgb(255, 255, 255) !important;
}

/* loading遮罩层 */
div.vxe-table--loading {
  background-color: rgba(245, 255, 255, 0) !important;
}

.vxe-toolbar {
  padding: 0;
}

div.vxe-pager {
  padding: 5px 0;
  text-align: left;
}

/* 这个用来解决使用了键盘导航导致非编辑态中无法选中的问题 */
.vxe-table.t--checked,
.vxe-table.t--selected {
  user-select: text !important;
}

//工具栏重置按钮隐藏
.vxe-toolbar .vxe-custom--option-wrapper .vxe-custom--footer {
  display: none;
}

// .vxe-table--body-wrapper,
// .vxe-table--fixed-left-body-wrapper,
// .vxe-table--fixed-right-body-wrapper {
//   overflow-y: auto !important;
//   overflow-x: auto !important;

//   &::-webkit-scrollbar {
//     width: 10px;
//     border-radius: 50%;
//     height: 10px;
//     cursor: pointer !important;
//   }

//   &::-webkit-scrollbar-corner, /* 滚动条角落 */
//     &::-webkit-scrollbar-thumb,
//     &::-webkit-scrollbar-track {
//     border-radius: 4px;
//   }

//   &::-webkit-scrollbar-corner,
//   &::-webkit-scrollbar-track {
//     /* 滚动条轨道 */
//     background-color: rgb(248, 248, 249);
//     box-shadow: inset 0 0 1px rgba(180, 160, 120, 0.5);
//   }

//   &::-webkit-scrollbar-thumb {
//     /* 滚动条手柄 */
//     background-color: #ccc;
//   }
// }
/*修改生产erp默认行颜色*/
// .product-erp{
.vxe-body--row td {
  background-color: rgb(245, 247, 250);
  background-color: rgb(255, 255, 255);
}
// }

.vxe-table tr.row--current td {
  background-color: rgb(230, 247, 255);
}

.vxe-cell{
  color: #000;
}

.vxe-table--body-wrapper,
.vxe-table--fixed-left-body-wrapper,
.vxe-table--fixed-right-body-wrapper {
  overflow-y: auto !important;
  overflow-x: auto !important;

  &::-webkit-scrollbar {
    width: 10px;
    border-radius: 50%;
    height: 10px;
    cursor: pointer !important;
  }

  &::-webkit-scrollbar-corner, /* 滚动条角落 */
    &::-webkit-scrollbar-thumb,
    &::-webkit-scrollbar-track {
    border-radius: 4px;
  }

  &::-webkit-scrollbar-corner,
  &::-webkit-scrollbar-track {
    /* 滚动条轨道 */
    background-color: rgb(248, 248, 249);
    box-shadow: inset 0 0 1px rgba(180, 160, 120, 0.5);
  }

  &::-webkit-scrollbar-thumb {
    /* 滚动条手柄 */
    background-color: #ccc;
  }
}