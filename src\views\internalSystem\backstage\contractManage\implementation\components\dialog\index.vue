<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <div>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
      >
        <el-form-item label="实施人" prop="implement_id">
          <el-select
            v-model="ruleForm.implement_id"
            clearable
            placeholder="请选择实施人"
          >
            <el-option
              v-for="item in propleList"
              :key="item.employeeId"
              :label="item.employee_number + '-' + item.employee_name"
              :value="item.employeeId"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submit()">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import API from "@/api/internalSystem/contractManage/index.js";
import {cloneDeep} from "lodash"
export default {
  data() {
    return {
      title: "更换实施人",
      dialogVisible: false,
      ruleForm: {
        implement_id: "",
      },
      propleList: [],
      rules: {
        implement_id: [{ required: true, message: "请选择", trigger: "blur" }],
      },
      selectObj: {},
    };
  },
  methods: {
    Show(selectObj) {
      this.dialogVisible = true;
      this.selectObj = cloneDeep(selectObj) ;
      this.$store.dispatch("getEmployee").then((res) => {
        this.propleList = res;
      });
   
      this.ruleForm.implement_id=this.selectObj.implement_id
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
      });
    },
    handleClose() {
      Object.assign(this.$data, this.$options.data());
      this.selectObj={}
    },
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) return false;
        this.save();
      });
    },
    save() {
      let params = {
        implement_state: 0,
        customer_contract_id: this.selectObj.customer_contract_id,
        implement_id: this.ruleForm.implement_id,
      };
      API.setImplementContract(params).then(() => {
        this.success("保存成功");
        this.handleClose();
        this.$emit("success");
      });
    },
  },
};
</script>
