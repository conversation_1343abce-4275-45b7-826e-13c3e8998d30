<template>
  <div>
    <el-form :model="ruleForm" ref="ruleForm" label-width="100px" class="mt10">
  
      <el-row :gutter="20">
        <el-col :span="8" class="formItem">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input
              v-model="ruleForm.customer_name"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem">
          <el-form-item label="销售员">
            <el-input
              v-model="fkSaleEmployeeUserInfo"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="formItem">
          <el-form-item label="维护员" prop="fk_maintain_employee_id_str">
            <el-input
              v-model="ruleForm.fk_maintain_employee_id_str"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
              </el-row>
              <el-row :gutter="20">
        <el-col :span="8" class="formItem">
          <el-form-item label="法定名称" prop="customer_legal_name">
            <el-input
              v-model="ruleForm.customer_legal_name"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem">
          <el-form-item label="联系人" prop="link_man">
            <el-input
              v-model="ruleForm.link_man"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="8" class="formItem">
          <el-form-item label="手机" prop="telephone">
            <el-input
              v-model="ruleForm.telephone"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>
                 </el-row>
              <el-row :gutter="20">
        <el-col :span="8" class="formItem">
          <el-form-item label="联系地址" prop="link_address">
            <el-input
              v-model="ruleForm.link_address"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem">
          <el-form-item label="QQ号码" prop="qq">
            <el-input
              v-model="ruleForm.qq"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="8" class="formItem">
          <el-form-item label="客户来源" prop="customer_source">
            <el-select
              v-model="ruleForm.customer_source"
              placeholder="请选择客户来源"
              :disabled="isEdit"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in params_constant_customer_source"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
</el-row>
              <el-row :gutter="20">
        <!-- <el-col :span="8" class="formItem">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input
              v-model="ruleForm.customer_name"
              :disabled="isEdit"
              clearable
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem">
          <el-form-item label="客户税号" prop="customer_tax_number">
            <el-input
              v-model="ruleForm.customer_tax_number"
              :disabled="isEdit"
              clearable
                      onkeyup="this.value=this.value.replace(/[, ]/g,'')" 
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem">
          <el-form-item label="收票地址" prop="receive_ticket_address">
            <el-input
              v-model="ruleForm.receive_ticket_address"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem">
          <el-form-item label="介绍公司" prop="introducer_name">
            <el-input
              v-model="ruleForm.introducer_name"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
         </el-row>
              <el-row :gutter="20">
        <el-col :span="8" class="formItem">
          <el-form-item label="银行账号" prop="customer_account">
            <el-input
              v-model="ruleForm.customer_account"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem">
          <el-form-item label="收票人" prop="receive_ticket_person">
            <el-input
              v-model="ruleForm.receive_ticket_person"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="formItem">
          <el-form-item label="开票地址" prop="open_ticket_address">
            <el-input
              v-model="ruleForm.open_ticket_address"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
         </el-row>
              <el-row :gutter="20">
        <el-col :span="8" class="formItem">
          <el-form-item label="开票电话" prop="open_ticket_phone">
            <el-input
              v-model="ruleForm.open_ticket_phone"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem">
          <el-form-item label="收票电话" prop="receive_ticket_phone">
            <el-input
              v-model="ruleForm.receive_ticket_phone"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem">
          <el-form-item label="开户银行" prop="opening_bank">
            <el-input
              v-model="ruleForm.opening_bank"
              :disabled="isEdit"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
.toLabel {
  width: 8%;
  color: black;
  cursor: default;
}

.add_customer_finance {
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
  background: #********;
}

@import "@/assets/css/element/font-color.scss";
</style>


<style lang="scss" >
.el-select /deep/ .el-input.is-disabled .el-input__inner {
  cursor: not-allowed;
  color: #000000;
}

.el-form-item--small /deep/ .el-form-item__label {
  line-height: 32px;
  color: black;
}
</style>