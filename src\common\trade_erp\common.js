import { Message } from "element-ui";
import store from "@/store/internalSystem/index.js";

/**
 * 批量添加填充单元格
 * @param {number} value 填充值
 * @param {String} field 需要填充的字段
 * @param {array} tableFullData 表格数据
 */
function batchFill(value = "", field = "", tableFullData = []) {
  if (tableFullData.length) {
    tableFullData.forEach(item => {
      item[field] = value;
    });
  }
}

/**
 * 表格最后新增根据导入字段添加数据
 * @param {Object} refTable vxe-table获取表格数据对象
 * @param {Object} records vxe-table预先设置表格新增行初始值字段
 * @param {array} data 传入数据
 * @param {number} ImportField 需要改变的数据字段
 * @param {*} row 插入行位置 -1为最后插入
 */
function batchAddTableData(
  refTable,
  records = [],
  data = [],
  ImportField = [],
  row = {},
  type = "purchase"
) {
  /* 表格数据是异步渲染 */
  let promise = new Promise(function(resolve, reject) {
    for (let i = 0; i < data.length; i++) {
      /* 表格创建行 */
      refTable
        .insertAt(records, row)
        .then(table => {
          let lineCalc = {};

          if (
            ImportField.includes("bNumb") &&
            ImportField.includes("bfWsdj") &&
            ImportField.includes("bPrice")
          ) {
            if (type === "purchase") {
              /* bNumb = whxsl 未核销数量 */
              lineCalc = tablePurchaseLineCount(
                data[i].whxsl,
                data[i].bfWsdj,
                data[i].bPrice
              );
            } else if (type === "order") {
              /* bNumb = whxsl 未核销数量 */
              lineCalc = tableOrderLineCount(
                data[i].whxsl,
                data[i].bfWsdj,
                data[i].bPrice,
                data[i].gPrice
              );
            }
          }

          ImportField.forEach(item => {
            table.row[item] = data[i][item]; /* 根据许需要导入字段进行赋值 */

            switch (item) {
              case "bPrice":
                table.row.bPrice = data[i].bfCpmj ? data[i].bfCpmj : "";
                break;
              case "bNumb":
                table.row.bNumb = data[i].whxsl;
                break;
              case "gNumb":
                table.row.gNumb = data[i].whxsl;
                break;
              case "bfWsje":
                table.row.bfWsje = lineCalc.bfWsje;
                break;
              case "bfCalje":
                table.row.bfCalje = lineCalc.bfCalje;
                break;
              case "bfSe":
                table.row.bfSe = lineCalc.bfSe;
                break;
              case "bfCalml":
                table.row.bfCalml = lineCalc.bfCalml;
                break;
            }
          });

          resolve(null);
        })
        .catch(err => {
          reject(err);
        });
    }
  });
  return promise;
}

/**
 * 表格行计算
 * @param {number} bNumb 数量
 * @param {number} bfWsdj 未税单价
 * @param {number} bPrice 含税单价
 */
function tablePurchaseLineCount(bNumb = 0, bfWsdj = 0, bPrice = 0) {
  let bfWsje = parseFloat(bfWsdj) * parseFloat(bNumb) || 0; //未税金额
  let bfCalje = parseFloat(bPrice) * parseFloat(bNumb) || 0; //价税合计
  let bfSe = parseFloat(bfCalje) - parseFloat(bfWsje) || 0; //税额

  return {
    bfWsje,
    bfCalje,
    bfSe
  };
}

/**
 * 表格行计算
 * @param {number} bNumb 数量
 * @param {number} bfWsdj 未税单价
 * @param {number} bPrice 含税单价
 */
function tableOrderLineCount(bNumb = 0, bfWsdj = 0, bPrice = 0, gPrice = 0) {
  let bfWsje = parseFloat(bfWsdj) * parseFloat(bNumb) || 0; //未税金额
  let bfCalje = parseFloat(bPrice) * parseFloat(bNumb) || 0; //价税合计
  let bfSe = parseFloat(bfCalje) - parseFloat(bfWsje) || 0; //税额
  let bfCalml = bfWsje - parseFloat(bNumb) * parseFloat(gPrice) || 0; //库存总成本

  return {
    bfWsje,
    bfCalje,
    bfSe,
    bfCalml
  };
}
/**
 * 表格单元个小数值显示
 * @param {Sting} field 字段名
 * @param {number} value 字段值
 * Function({cellValue, row, rowIndex, column, columnIndex})
 */
function getFixedDemical(field = "", value = 0) {
  let data;
  value ? parseFloat(value) : (value = 0);

  /* 获取用户设定的小数位 */
  const userInfo = store.getters.userInfo;
  const pDecimal = userInfo.priceFixedNum || 0;
  const nDecimal = userInfo.numFixedNum || 0;

  switch (field) {
    case "bfDdzk":
      data = parseFloat(value).toFixed(4);

      break;

    case "bNumb":
    case "gNumb":
      data = parseFloat(value).toFixed(nDecimal);
    
      break;

    case "bfCpmj":
    case "bPrice":
    case "bfWsdj":
    case "gPrice":
      data = parseFloat(value).toFixed(pDecimal);

      break;
    default:
      if (field.endsWith("Num") || field.endsWith("sl")) {
        data = parseFloat(value).toFixed(nDecimal);
      } else {
        data = parseFloat(value).toFixed(2);
      }

      break;
  }
  if (data == 0) {
    data = Math.abs(data);
  }
  return data;
}

//获取当前时间 yyyy-MM-dd hh:mm:ss 格式
function getYMDHMS() {
  let myDate = new Date();
  let moth = myDate.getMonth() + 1;
  let day = myDate.getDate();
  moth = moth < 10 ? "0" + moth : moth;
  day = day < 10 ? "0" + day : day;
  let ymd = myDate.getFullYear() + "-" + moth + "-" + day;
  let hours = myDate.getHours();
  let minute = myDate.getMinutes();
  let secend = myDate.getSeconds();
  hours = hours < 10 ? "0" + hours : hours;
  minute = minute < 10 ? "0" + minute : minute;
  secend = secend < 10 ? "0" + secend : secend;
  let hms = hours + ":" + minute + ":" + secend;
  let ymdhms = ymd + " " + hms;
  return ymdhms;
}

//获取当前时间 yyyy-MM-dd 格式
function getYMD() {
  let mydate = new Date();
  let year = mydate.getFullYear();
  let moth = mydate.getMonth() + 1;
  let day = mydate.getDate();
  moth = moth < 10 ? "0" + moth : moth;
  day = day < 10 ? "0" + day : day;
  let addTime = year + "-" + moth + "-" + day;
  return addTime;
}

/**
 * 设置表格单元格宽度存储
 * @param {String} djType
 * @param {number} widthData
 */

function setCellWidth(djType = "", widthData = 100) {
  let cellWidth = {};
  let key = "VXE_TABLE_CUSTOM_COLUMN_WIDTH";

  let value = localStorage.getItem(key);
  if (value) {
    value[djType] = widthData;
  }

  localStorage.setItem(
    "VXE_TABLE_CUSTOM_COLUMN_WIDTH",
    JSON.stringify({
      XSDD: cellWidth,
      _v: 0
    })
  );
}

/**
 * 获取表格单元格宽度
 */
function getCellWidth() {
  let cellWidth = JSON.parse(
    localStorage.getItem("VXE_TABLE_CUSTOM_COLUMN_WIDTH")
  ).XSDD;
  return cellWidth;
}

/**
 * 计算输入相关行的值
 * 适用于销售订单/采购订单/销售报价单
 * @param {String} value 输入参数值
 * @param {String} field 输入字段名
 * @param {Object} row 输入当前所在行  存储的数据为临时数据 必须通过getFullTableData进行同步
 * 商品面价不为0 含税单价 = 商品面价 * 折扣
 * 商品面价为0 含税单价不可计算得出
 * 填写含税单价 未税单价 = 含税单价 / (1 + taxRate)
 * 商品面价不为0 折扣 = 商品面价 / 含税单价
 * 填写未税单价 含税单价 = 未税单价 * (1 + taxRate)
 */
function lineCalc(value = 0, field = "", row = {}) {
  /* 初始化 */
  let taxRate = row.bfXsfpsl || row.bfCgfpsl || 0; //税率
  let markedPrice = row.bfCpmj || 0; //面价
  let quantity = row.bNumb || 0; //数量
  let taxIncludedPrice; //销售单价
  let untaxedPrice; //未税价格
  let discount; //折扣
  let tax;
  let totalPriceTax;
  let totalPriceUntax;

  switch (field) {
    case "bfCpmj":
      break;

    case "bNumb":
      if (!row.proId) return;
      untaxedPrice = row.bfWsdj || 0; //未税单价
      taxIncludedPrice = row.bPrice || 0; //销售单价
      totalPriceTax = parseFloat(value) * taxIncludedPrice; //价税合计等于数量乘销售单价
      totalPriceUntax = parseFloat(value) * untaxedPrice; //未税金额=数量乘未税金额
      tax = totalPriceTax - totalPriceUntax;

      break;

    case "bPrice":
      taxIncludedPrice = parseFloat(value);
      /* 折扣/含税单价/未税单价赋值 */
      untaxedPrice = parseFloat(taxIncludedPrice) / (1 + taxRate / 100);
      row.bfWsdj = untaxedPrice;
      row.bfDdzk = 0;

      break;

    case "bfWsdj":
      untaxedPrice = parseFloat(value);
      /* 含税单价 = 未税单价 / 税率 */
      taxIncludedPrice = parseFloat(value) * (1 + taxRate / 100);

      /* 折扣/含税单价/未税单价赋值 */
      row.bPrice = taxIncludedPrice;
      row.bfWsdj = untaxedPrice;
      row.bfDdzk = 0;

      break;

    case "bfDdzk":
      /* 折扣改变 */
      discount = parseFloat(value);
      if (!row.proId) {
        row.bfDdzk = discount;
        if (row.proCode === "SPXXZR") row.bfDdzk = 1;
        return;
      }
      if (parseFloat(markedPrice) === 0) {
        // Message({
        //   type: "warning",
        //   message: "未设置商品面价,折扣输入无效"
        // });
        discount = 0;
        row.bfDdzk = discount;
        return;
      }
      /* 含税单价 = 商品面价 / 折扣 */
      parseFloat(value) === 0
        ? (taxIncludedPrice = 0)
        : (taxIncludedPrice = parseFloat(markedPrice) * parseFloat(value));

      taxIncludedPrice = markedPrice * discount;
      /* 计算未税单价 */
      untaxedPrice = parseFloat(taxIncludedPrice) / (1 + taxRate / 100);

      /* 折扣/含税单价/未税单价赋值 */
      row.bPrice = taxIncludedPrice;
      row.bfWsdj = untaxedPrice;
      row.bfDdzk = discount;

      break;

    case "bfXsfpsl":
      if (!row.proId) return;
      /* 销售订单发票税率改变重新获取未税单价 */
      taxIncludedPrice = row.bPrice || 0;
      untaxedPrice = parseFloat(taxIncludedPrice) / (1 + taxRate / 100);
      row.bfWsdj = untaxedPrice;
      break;

    case "bfCgfpsl":
      if (!row.proId) return;
      /* 采购订单发票税率改变重新获取未税单价 */
      taxIncludedPrice = row.bPrice || 0;
      untaxedPrice = parseFloat(taxIncludedPrice) / (1 + taxRate / 100);
      row.bfWsdj = untaxedPrice;
      break;
  }

  if (field !== "bNumb") {
    //判断数量是否为空
    if (quantity) {
      /* 计算 价税合计 / 未税金额/ 税额 */
      totalPriceTax = parseFloat(taxIncludedPrice) * parseFloat(quantity);
      totalPriceUntax = parseFloat(untaxedPrice) * parseFloat(quantity);
      tax = totalPriceTax - totalPriceUntax;
    } else {
      totalPriceTax = 0;
      totalPriceUntax = 0;
      tax = 0;
    }
  }

  row.bfSe = tax; /* 税额赋值 */
  row.bfCalje = totalPriceTax; /* 价税合计赋值 */
  row.bfWsje = totalPriceUntax; /* 未税金额赋值 */
}

/**
 * 计算表格价税合计 / 未税金额	/	未税毛利
 * @param {array} tableFullData 表格数据
 */
function calcTableCount(tableFullData = [], type = "purchse") {
  /* 初始化 */
  let UntaxedTotal = 0; // 未税金额
  let taxedTotal = 0; //价税合计
  let taxTotal = 0; //总税额
  let UntaxedGrossTotal = 0;

  /* 总金额 = 每行相同项字段累加 */
  tableFullData.forEach(item => {
    /* 如果该行不存在商品不给予计入 */
    if (item.proId) {
      let unTaxed = parseFloat(item.bfWsje) || 0;
      let taxed = parseFloat(item.bfCalje) || 0;

      UntaxedTotal += unTaxed; /* 未税金额累加 */
      taxedTotal += taxed; /* 价税合计累加 */

      /* 销售单据计算未税毛利 */
      if (type === "order") {
        let UntaxedGross = parseFloat(item.bfCalml) || 0;
        UntaxedGrossTotal += UntaxedGross; /* 未税毛利累加 */
      }
    }
  });

  /* 税额 = 价税合计 - 未税金额 */
  taxTotal = taxedTotal - UntaxedTotal;

  let bfCalse = taxTotal; /* 总税额 */
  let bfCalwsje = UntaxedTotal; /* 未税总金额 */
  let bfCalje = taxedTotal; /* 价税合计总额 */
  let bfCalmlje = UntaxedGrossTotal; /* 未税毛利总额 */

  return {
    bfCalse,
    bfCalwsje,
    bfCalje,
    bfCalmlje
  };
}

/**
 * 设置默认选中税率
 * @param {number} defaultTaxRate 默认税率
 * @param {array} taxList 数据列表
 */
function getDefaultTaxRate(defaultTaxRate = 16, taxList = []) {
  if (taxList.length > 0) {
    return taxList.filter(
      /* 查找出税率相同的的项目 */
      item => getTaxRate("", taxList, item.name) === defaultTaxRate
    )[0].invoiceId;
  }
}

/**
 * 获取发票税率
 * @param {number} invoiceId 税率ID
 * @param {array} taxList 数据列表
 * @param {Object} invoiceItem 税率列表项
 */
function getTaxRate(invoiceId = "", taxList = [], invoiceItem = "") {
  let invoiceName = "";

  if (invoiceId) {
    let selectItem = taxList.filter(item => item.invoiceId === invoiceId);
    invoiceName = selectItem[0].name;
  } else {
    invoiceName = invoiceItem; /* 用于设置默认税率 */
  }

  let x = invoiceName.lastIndexOf("(");
  let y = invoiceName.lastIndexOf("%");
  let taxRate = invoiceName.substring(x + 1, y); /* 截取字符串获取税率 */

  taxRate = parseFloat(taxRate);

  return taxRate;
}

/**
 * 根据选中的税率Id获取相应税率
 * @param {number} invoiceId 税率Id
 * @param {array} taxList 税率列表
 */
function getTaxRateById(invoiceId = "", taxList = []) {
  let taxRate = 0;
  taxList.forEach(item => {
    if (item.id === invoiceId) {
      taxRate = item.taxRate;
    }
  });
  return taxRate;
}

/**
 * 调入采购单据校验选中数据,判断供应商/仓库/税率是否一致
 * @param {*} data 选中的数据
 * @param {*} providerId 供应商Id
 * @param {*} depotId 仓库Id
 * @param {*} taxRate 表格税率
 * @param {*} djState 单据状态
 */
function validatePurchaseImport(
  data = [],
  providerId = "",
  depotId = "",
  taxRate = "",
  djState = ""
) {
  if (!data.length) {
    Message.error(`请至少选择一条数据进行操作`);
    return false;
  }

  if (djState === 2 || djState === 1) {
    Message.error(`当前单据流程状态不可调入数据`);
    return false;
  }

  if (providerId && providerId !== data[0].providerId) {
    Message.error(`当前选中的数据,第1条数据与已存在表格数据供应商不一致`);
    return false;
  }

  if (depotId && depotId !== data[0].depotId) {
    Message.error(`当前选中的数据,第1条数据与已存在表格数据仓库不一致`);
    return false;
  }

  if (taxRate && taxRate !== 0 && taxRate !== data[0].bfCgfpsl) {
    Message.error(`当前选中的数据,第1条数据与已存在表格数据税率不一致`);
    return false;
  }

  /* 以上条件通过后,后面代码判断才有效 */
  if (data.length === 1) return true;
  for (let i = 1; i < data.length; i++) {
    if (data[i].providerId !== data[0].providerId) {
      Message.error(`当前选中的数据,第${i + 1}条供应商与前一条不一致`);
      return false;
    }
    if (data[i].bfCgfpsl !== data[0].bfCgfpsl) {
      Message.error(`当前选中的数据,第${i + 1}条税率与前一条不一致`);
      return false;
    }
    if (data[i].depotId !== data[0].depotId) {
      Message.error(`当前选中的数据,第${i + 1}条仓库与前一条不一致`);
      return false;
    }
  }

  return true;
}

/**
 * 调入采购单据校验选中数据,判断供应商/仓库/税率是否一致
 * @param {array} data 选中的数据
 * @param {number} customerId 供应商Id
 * @param {number} depotId 仓库Id
 * @param {number} taxRate 表格税率
 * @param {number} djState 单据状态
 */
function validateOrderImport(
  data = [],
  customerId = "",
  depotId = "",
  taxRate = "",
  djState = "",
  invoiceId
) {
  if (!data.length) {
    Message.error(`请至少选择一条数据进行操作`);
    return false;
  }

  if (djState === 2 || djState === 1) {
    Message.error(`当前单据流程状态不可调入数据`);
    return false;
  }

  if (customerId && customerId !== data[0].customerId&&data[0].proCode!=="SPXXZR") {
    Message.error(`当前选中的数据,第1条数据与已存在表格数据供应商不一致`);
    return false;
  }

  if (depotId && depotId !== data[0].depotId&&data[0].proCode!=="SPXXZR") {
    Message.error(`当前选中的数据,第1条数据与已存在表格数据仓库不一致`);
    return false;
  }

  if (taxRate && taxRate !== 0 && taxRate !== data[0].bfXsfpsl&&data[0].proCode!=="SPXXZR") {
    Message.error(`当前选中的数据,第1条数据与已存在表格数据税率不一致`);
    return false;
  }
  if (invoiceId && invoiceId !== 0 && invoiceId !== data[0].invoiceId&&data[0].proCode!=="SPXXZR") {
    Message.error(`当前选中的数据,第1条数据与已存在表格数据发票类型不一致`);
    return false;
  }

  /* 以上条件通过后,后面代码判断才有效 */
  if (data.length === 1) return true;
  for (let i = 1; i < data.length; i++) {
    if (data[i].customerId !== data[0].customerId&&data[i].proCode!=="SPXXZR"&& data[0].proCode!=="SPXXZR") {
      Message.error(`当前选中的数据,第${i + 1}条客户与前一条不一致`);
      return false;
    }

    if (data[i].bfXsfpsl !== data[0].bfXsfpsl && data[i].proCode !== "SPXXZR"&& data[0].proCode!=="SPXXZR") {
      Message.error(`当前选中的数据,第${i + 1}条税率与前一条不一致`);
      return false;
    }

    if (data[i].depotId !== data[0].depotId && data[i].proCode !== "SPXXZR"&& data[0].proCode!=="SPXXZR") {
      Message.error(`当前选中的数据,第${i + 1}条仓库与前一条不一致`);
      return false;
    }

    if (
      data[i].invoiceId !== data[0].invoiceId &&
      data[i].proCode !== "SPXXZR"&& data[0].proCode!=="SPXXZR"
    ) {
      Message.error(`当前选中的数据,第${i + 1}条发票类型与前一条不一致`);
      return false;
    }
    //
  }

  return true;
}

function IncomingData(refs, data = [], djState = 2, type = "purchse") {
  
  if(!data.length) return Message.error("至少选择一条数据")
  let tableData = refs.mainTable.GetTableFullData(); // 数据源
  let temp=[]
  for (let i = 0; i < data.length; i++) {

    for (let j = 0; j < tableData.length; j++) {
      if (
        tableData[j].sourceBId &&
        data[i].sourceBId &&
        tableData[j].sourceBId === data[i].sourceBId
      ) {
      
        Message.warning(`当前选中的数据,第${i + 1}条来源与表格前一条一致,已自动过滤`);
        // data.splice(i,1)
        temp.push(data[i].sourceBId)
        break;
      }
    }
  }

  data=data.filter(item=>{
    return item.sourceBId&&temp.indexOf(item.sourceBId)===-1
  })
  if(!data.length){
    Message.error("所有选择的数据都不符合要求全被过滤，请重新选择");
    refs.historyOrderDialog.ClearData();
    return
  }

  if (type === "order") {
    let depotId = refs.headForm.GetDepotId(); // 仓库
    let taxRate = tableData.length !== 0 ? tableData[0].bfXsfpsl : ""; // 税率
    let invoiceId = tableData.length !== 0 ? tableData[0].invoiceId : ""; // 税率id
    let customerId = refs.headForm.GetCustomerId(); // 客户
    if (
      !validateOrderImport(
        data,
        customerId,
        depotId,
        taxRate,
        djState,
        invoiceId
      )
    )
      return; /* 验证数据是否一致 */
  }
  if (type === "purchase") {
    let depotId = refs.headForm.GetDepotId(); // 仓库
    let taxRate = tableData.length !== 0 ? tableData[0].bfCgfpsl : ""; // 税率
    let providerId = refs.headForm.GetProviderId(); // 供应商
    if (!validatePurchaseImport(data, providerId, depotId, taxRate, djState))
      return; /* 验证数据是否一致 */
  }

  /* 调入数据 */
  refs.mainTable.getHistoryData(data);
  refs.headForm.importForm(data);
  refs.historyOrderDialog.ClearData();
  if(!refs.historyOrderDialog.GetCloseState())  refs.historyOrderDialog.dialogCancel();
}

export {
  getFixedDemical,
  getYMDHMS,
  getYMD,
  setCellWidth,
  getCellWidth,
  batchFill,
  batchAddTableData,
  tablePurchaseLineCount,
  tableOrderLineCount,
  lineCalc,
  calcTableCount,
  getDefaultTaxRate,
  getTaxRate,
  getTaxRateById,
  validatePurchaseImport,
  validateOrderImport,
  IncomingData
};
