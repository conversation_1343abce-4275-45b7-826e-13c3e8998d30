<template>
  <div v-if="dialogVisible" class="orderH100">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button
        type="primary"
        @click="dialogCancel(false)"
        v-permit="'ADD_RECEIVABLES_NEW'"
        >新增</el-button
      >
      <el-button
        type="primary"
        @click="submitForm('ruleForm')"
        :loading="loading"
        :disabled="!!ruleForm.receivables_id"
        v-permit="'ADD_RECEIVABLES_NEW'"
        >保 存
      </el-button>

      <el-button
        type="primary"
        @click="chooseContract"
        :disabled="!!ruleForm.receivables_id"
        v-permit="'ADD_RECEIVABLES_NEW'"
        >调入合同
      </el-button>
      <el-button
        type="primary"
        @click="back"
        v-permit="'AUDIT_BACK_RECEIVABLES_NEW'"
        :disabled="!(ruleForm.receivables_id && ruleForm.check_state !== 3)"
      >
        退回审核
      </el-button>
      <el-button
        type="danger"
        @click="del"
        v-permit="'DEL_RECEIVABLES_NEW'"
        :disabled="!ruleForm.receivables_id"
      >
        删除
      </el-button>
    </div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="mt10"
      style="margin-right: 40px"
    >
      <el-row :gutter="20">
        <el-col :span="8" class="ellipsis">
          <el-form-item label="单据编号">
            <el-input
              v-model="ruleForm.bill_code"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="ellipsis">
          <el-form-item label="单据日期">
            <el-input
              v-model="ruleForm.update_time"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="ellipsis">
          <el-form-item label="制单人">
            <el-input v-model="addUserInfo" disabled clearable></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2 ellipsis">
          <el-form-item label="制单部门">
            <el-input
              v-model="ruleForm.add_user_depot"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->

        <el-col :span="8" class="formItem2 ellipsis">
          <el-form-item label="收款银行" prop="bank_name">
            <el-input
              :disabled="!!ruleForm.receivables_id"
              v-model="ruleForm.bank_name"
              @focus="chooseBank"
              placeholder="点击选择收款银行"
              readonly
              clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2 ellipsis">
          <el-form-item label="结算方式" prop="settlement_method">
            <el-select
              v-model="ruleForm.settlement_method"
              :disabled="!!ruleForm.receivables_id"
              placeholder="请选择结算方式"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in receivables_settlement_method"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- <el-divider></el-divider> -->

    <table-view
      :tableList="tableList"
      :tableData="tableData"
      :isDblclick="true"
      @del="delDetail"
      class="mt10 tableContent"
      :isDel="isDel"
      :contract_detail_ids="contract_detail_ids"
      style="margin-right: 40px; margin-left: 10px"
    >
    </table-view>

    <sales-unit-list
      ref="salesUnitList"
      dialogTitle="销货单位列表"
      @getInfo="getSalesUnit"
    />
    <contract-list
      ref="contractList"
      :not_complete_receivables="true"
      :isReceive="true"
      :isJudge="true"
      dialogTitle="未收款合同列表"
      :data_state="1"
      @getInfo="getContractInfo"
      :type="2"
      :contract_ids="contract_detail_ids"
    />
    <bank-list
      ref="bankList"
      dialogTitle="银行列表"
      @getInfo="getBankInfo"
      :fk_sales_unit_id="ruleForm.fk_sales_unit_id || 0"
    />
  </div>
</template>
<script src="./index.js"></script>
<style lang="scss" scoped>
.w100 {
  width: 100% !important;
}

@import "@/assets/css/element/font-color.scss";
</style>
