<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.brand_type" placeholder="请输入产品服务"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.brand_classify"
          placeholder="请选择品牌分类"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in brandClassifyList"
            :key="item.sysValue"
            :label="item.sysName"
            :value="item.sysValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.disable_state"
          placeholder="请选择状态"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in disable_state"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
       <!-- <el-form-item>
        <el-select
          v-model="formSearch.sale_pattern"
          placeholder="请选择销售方式"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in sale_pattern"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
       <el-form-item>
        <el-select
          v-model="formSearch.product_sale_model"
          placeholder="请选择软件模式"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in product_sale_model"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
       <el-form-item>
        <el-select
          v-model="formSearch.product_is_grant"
          placeholder="请选择是否有授权码"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in product_is_grant"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
       <el-form-item>
        <el-select
          v-model="formSearch.product_is_period"
          placeholder="请选择有无维护期"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in product_is_period"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button type="primary" @click="add" v-permit="'ADD_BRAND_NEW'">添加</el-button>
      </el-form-item>
    </el-form>
    <!-- 新增修改产品信息 -->
    <add-brand :parentList="parentList" ref="AddBrand" @selectData="getList" />
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      @modify="modify"
      @del="del"
      :isTableIndex="true"
      :isEdit="'UPDATE_BRAND_NEW'"
      :isDel="'DEL_BRAND_NEW'"
      isThrid="BRAND_FILE"
      :thridTitle="'附件'"
      @thrid="addFile"
    ></table-view>
    <Pagination ref="pagination" @success="getList" />
    <!-- 附件 -->
    <file-list ref="fileList" :fileId="brand_id" fileType="brand" />
  </div>
</template>

<script src="./index.js"></script>
