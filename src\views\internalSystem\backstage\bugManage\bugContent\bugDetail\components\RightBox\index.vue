<template>
     <div class="rig">
          <div class="head">{{title}}</div>
          <div class="content">
            <ul>
              <li v-for="(item, index) in infoList" :key="index">
                <div class="left">{{ item.label }}</div>
                <div class="right">
                  <span class="bugState" v-if="item.label === 'Bug状态'" :class="[infoData[item.field]==='已关闭'?'close':'']">
                    {{ infoData[item.field] }}
                  </span>

                  <span v-else-if="item.label === '当前指派'">
                    {{ infoData[item.field] }} 于 {{ infoData.update_time }}
                  </span>

                  <span
                    v-else
                    :class="[
                      item.label === '所属产品' ? 'system' : '',
                      item.label === '严重程度' ?infoData[item.field]===1?'radus danger':'radus' : '',
                       item.label === '优先级' ? infoData[item.field]===1?'radus yxj border-danger':'radus yxj' : '',
                    ]"
                    @click="toList(item.label)"
                 
                  >
                    {{ item.field ? infoData[item.field] : "" }}
                  </span>
                </div>
              </li>
            </ul>
          </div>
        </div>
</template>

<script>
export default {
    props:{
        infoList:{
            type:Array,
            default(){
                return []
            }
        },
        infoData:{
            type:Object,
            default(){
                return []
            }
        },
        title:{
            type:String
        },
         outside: {
      type: Boolean,
      default: false,
    },
    },
    methods:{
      toList(label){
        if(label!=='所属产品') return
        let brand_id=this.infoData.brand_id;
        let path=this.outside?'/outside/outsideBugList':'/backstage/bugManage/bugContent/bugList'
        this.$router.push({
          path,
          query:{
            brand_id
          }
        })
      }
    }
}
</script>
<style lang="scss" scoped>
.bruce {
  vertical-align: top;
}
.radus {
  border-radius: 50%;
  height: 18px;
  width: 18px;
  background-color: #db7c12;
  color: #fff;
  display: inline-block;
  display: flex;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  align-items: center;
  box-sizing: border-box;
}
.yxj {
  background-color: #fff;
  color: #db7c12;
  border: 1px solid #db7c12;
}
.danger {
  background-color: #d71319;
}
.fz-danger {
  color: #d71319;
}
.success {
  color: green;
}
.border-danger {
  border-color: #d71319;
  color: #d71319;
}
.wenhao {
  border: 2px solid #ccc;
  background-color: #fff;
  color: #ccc;
  font-weight: bold;
}
.rig {
  vertical-align: top;
  width: 100%;
  box-sizing: border-box;
  font-size: 13px;
  display: inline-block;
  border: 1px solid #ccc;
  color: #444;
  .head {
    font-weight: bold;

    line-height: 20px;
    padding: 5px 10px;
    background-color: #f0f0f0;
  }
  .content {
    border-top: 1px solid #ccc;
    padding: 10px;
    ul {
      li {
        width: 100%;
        line-height: 18px;
        display: flex;
        padding: 3px 8px;
        box-sizing: border-box;
        .left {
          text-align: right;
          width: 60px;
        }
        .right {
          padding-left: 15px;
          flex: 1;
          color: #141414;
          .system {
            cursor: pointer;
            color: #03c;
          }
          .bugState {
            color: #229f24;
            font-weight: bold;
          }
          .close{
              color: #777;
          }
        }
      }
    }
  }
}
</style>
