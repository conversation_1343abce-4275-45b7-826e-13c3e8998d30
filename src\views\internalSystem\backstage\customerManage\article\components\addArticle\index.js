import API from '@/api/internalSystem/customerManage/article'
import RichText from '@/components/internalSystem/RichText/RichText.vue'
// import RichText from "@/components/RichText/RichText.vue";
import {
  mapGetters
} from 'vuex'
import {
  getOptions
} from "@/common/internalSystem/common.js"
export default {
  name: "addArticle",
  components: {
    RichText
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        title: "",
        copy_content: "",
        type:"",
        keywords:""
      },
      rules: {
        title: [{
          required: true,
          message: "请输入主题",
          trigger: "blur"
        }],
        copy_content: [{
          required: true,
          message: "请输入内容",
          trigger: "blur"
        }],
        type: [{
          required: true,
          message: "请选择类型",
          trigger: "change"
        }]
      },
      loading: false,
      isEdit: false,
      typeList:[]
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.isEdit = true;
      this.typeList = getOptions('t_copy', 'type');

      if (data) {
        this.isEdit = this.buttonPermissions.length ? (this.existence(this.buttonPermissions, 'UPDATE_ARTICLE_NEW')) : false;
        this.ruleForm = data;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    //发出
    send() {
      if (this.ruleForm.copy_state !== 4 && this.ruleForm.copy_state !==2)
        return this.error("该单据已发出，不允许重复发出");
      let params = {
        copy_id: this.ruleForm.copy_id,
        copy_state: 3
      };
      API.updateAudit(params)
        .then(() => {
          this.dialogCancel();
        })
        .catch(() => {});
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    save() {
      let params = this.ruleForm;
      params.publish_man = this.userInfo.employeeId;
      this.loading = true;
      if (params.copy_id) {
        if (params.copy_state == 2)
          return this.error("该单据已审核，不允许修改！");
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      }

    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        title: "",
        copy_content: "",
        type:""
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'buttonPermissions'])
  },
};