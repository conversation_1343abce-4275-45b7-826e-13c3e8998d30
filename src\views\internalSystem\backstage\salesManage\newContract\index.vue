<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small" v-if="!isAdd&&!isAudit">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.customer_name" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.fk_sell_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number+'-'+item.employee_name"
            :value="item.employeeId"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item>
        <el-select v-model="formSearch.add_user_id" placeholder="请选择操作员" class="inputBox" filterable clearable>
          <el-option v-for="item in employeeList" :key="item.user_id" :label="item.employee_name" :value="item.user_id">
          </el-option>
        </el-select>
      </el-form-item>-->
      <el-form-item>
        <el-select
          v-model="formSearch.sale_type"
          placeholder="请选择销售类型"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in sale_type"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.audit_state"
          placeholder="请选择审核状态"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in customer_contract_audit"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.startTime" hint="请选择开始时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.endTime" hint="请选择结束时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.maintain_start_time" hint="请选择维护费开始时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.maintain_stop_time" hint="请选择维护费结束时间"></my-date>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="formSearch.has_receivables">已收款</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="formSearch.not_has_receivables">未收款</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="formSearch.has_open_ticket">已开票</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="formSearch.not_has_open_ticket">未开票</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button
          type="primary"
          @click="add"
          v-permit="'ADD_CONTRACT_NEW'"
        >制单</el-button>
      </el-form-item>
    </el-form>
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd&&!isAudit"
      @del="del"
      @modify="modify"
      isEdit="permanent_button"
      :isDel="'DEL_CONTRACT_NEW'"
      isThrid="AUDIT_CONTRACT_NEW"
      :thridTitle="'审核'"
      @thrid="item => toAuditDet(item, '合同单审核','audit_state')"
    ></table-view>
    <div class="mt10 moneyTitle" v-if="!isAdd&&!isAudit">
      <el-row >
        <el-col :span="4">合同总金额：{{allList.contract_amount}}元</el-col>
        <el-col :span="4">已收款总金额：{{allList.receivables_amount}}元</el-col>
        <el-col :span="4">未收款总金额：{{allList.not_receivables_amount}}元</el-col>
        <el-col :span="4">已开票总金额：{{allList.open_ticket_amount}}元</el-col>
        <el-col :span="4">未开票总金额：{{allList.not_open_ticket_amount}}元</el-col>
      </el-row>
    </div>
    <Pagination ref="pagination" @success="getList" v-show="!isAdd&&!isAudit" />
    <!-- 新增销售合同单 -->
    <add-contract ref="addContract" @getInfo="getInfo" @selectData="getList" />
    <!-- 审核 -->
    <NewContractAudit ref="contractAudit" v-show="isAudit" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.moneyTitle {
  font-size: 14px;
  font-weight: bold;
}
</style>
