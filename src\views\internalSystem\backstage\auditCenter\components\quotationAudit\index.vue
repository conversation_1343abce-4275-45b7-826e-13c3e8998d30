<template>
  <div
    class="body-p10 orderH100"
    style="overflow-y:auto;overflow-x: hidden;"
    v-if="dialogVisible"
  >
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="160px"
      class="mt10 flexAndFlexColumn"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input disabled v-model="ruleForm.customer_name" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="操作员工" prop="add_user_name">
            <el-input
              disabled
              v-model="ruleForm.add_user_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售员" prop="fk_sell_employee_name">
            <el-input
              disabled
              v-model="ruleForm.fk_sell_employee_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销货单位" prop="sales_unit_id_format">
            <el-input
              disabled
              v-model="ruleForm.sales_unit_id_format"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="操作员部门" prop="add_user_department_name">
            <el-input
              disabled
              v-model="ruleForm.add_user_department_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售员部门" prop="fk_sell_department_name">
            <el-input
              disabled
              v-model="ruleForm.fk_sell_department_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="培训方式" prop="train_type">
            <el-select
              disabled
              v-model="ruleForm.train_type"
              placeholder="请选择培训方式"
              filterable
              clearable
            >
              <el-option
                v-for="item in contract_train_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户传真" prop="fax">
            <el-input disabled v-model="ruleForm.fax" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所在省份" prop="province_name">
            <el-input
              disabled
              v-model="ruleForm.province_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="付款方式" prop="pay_type">
            <el-select
              disabled
              v-model="ruleForm.pay_type"
              placeholder="请选择付款方式"
              filterable
              clearable
            >
              <el-option
                v-for="item in contract_pay_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="手机" prop="phone">
            <el-input disabled v-model="ruleForm.phone" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所在城市" prop="city_name">
            <el-input
              disabled
              v-model="ruleForm.city_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售类型" prop="sell_type">
            <el-select
              disabled
              v-model="ruleForm.sell_type"
              placeholder="请选择销售类型"
              filterable
              clearable
            >
              <el-option
                v-for="item in sell_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="介绍人" prop="introducer_name">
            <el-input
              disabled
              v-model="ruleForm.introducer_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="详细地址" prop="customer_address">
            <el-input
              disabled
              v-model="ruleForm.customer_address"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人" prop="link_man">
            <el-input disabled v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="介绍合同" prop="introducer_contract_format">
            <el-input
              disabled
              v-model="ruleForm.introducer_contract_format"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="软件序列号" prop="software_no">
            <el-input
              disabled
              v-model="ruleForm.software_no"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人QQ" prop="link_qq">
            <el-input disabled v-model="ruleForm.link_qq" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据备注" prop="remark">
            <el-input disabled v-model="ruleForm.remark" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-tabs v-model="activeName" class="flexAndFlexColumn">
        <el-tab-pane label="合同产品" name="first" class="flexAndFlexColumn">
          <table-custom
            ref="proTableCustom"
            :obj="proObj"
            :tableCol="proTableCol"
            :isDel="false"
          />
        </el-tab-pane>
        <el-tab-pane label="功能模块" name="second" class="flexAndFlexColumn">
          <table-custom
            ref="moduleTableCustom"
            :obj="moduleObj"
            :tableCol="moduleTableCol"
            :isDel="false"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisibleAudit"
      append-to-body
      @close="dialogCancelAudit"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="auditForm"
        :rules="rules"
        ref="auditForm"
        label-width="100px"
      >
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select
            v-model="auditForm.auditState"
            placeholder="请选择审核状态"
            filterable
            clearable
          >
            <template v-for="item in contract_auditStateList">
              <el-option
                v-if="item.id != ruleForm.audit_state && item.id != 3"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
              v-if="item.id != ruleForm.audit_state && item.id != 3"
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="auditRemark">
          <el-input
            v-model="auditForm.auditRemark"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('auditForm')"
          :loading="loading"
          >保 存</el-button
        >
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js"></script>

<style lang="scss" scoped>
@import "@/assets/css/element/font-color.scss";
</style>
