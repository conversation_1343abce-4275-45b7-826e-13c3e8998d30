import API from '@/api/internalSystem/common'
const state = {
	provinceList: [], // 省份
	cityList: [], // 获取城市列表
	fieldPermissions: [], // 当前页面的字段权限
	buttonPermissions: [], // 当前页面的按钮权限
	userInfo: {}, // 用户信息
	pathPermissions: [], //当前页面的菜单栏权限
	routeList: [],//打开的标签页
	homeIndent:true,//首页是否缩进
	closeRoute:"",
	indexFkSellEmployeeId:null,
}
const getters = {
	fieldPermissions: state => state.fieldPermissions,
	cityList: state => state.cityList,
	provinceList: state => state.provinceList,
	buttonPermissions: state => state.buttonPermissions,
	pathPermissions: state => state.pathPermissions,
	userInfo: state => state.userInfo,
	routeList: state => state.routeList,
	homeIndent:state=>state.homeIndent,
	closeRoute:state=>state.closeRoute,
	indexFkSellEmployeeId:state=>state.indexFkSellEmployeeId,

}
const mutations = {
	SET_FIELDPERMISSIONS(state, permissions = {}) {
		state.fieldPermissions = permissions
	},
	SET_BUTTONPERMISSIONS(state, permissions = {}) {
		state.buttonPermissions = permissions
	},
	SET_PATHPERMISSIONS(state, permissions = []) {
		state.pathPermissions = permissions
	},
	SET_USERINFO(state, data = {}) {
		state.userInfo = data
	},
	SET_PROVINCES (state, data = []) {
		state.provinceList = data
	},
	SET_CITY (state, data = []) {
		state.cityList = data
	},
	SET_ROUTELIST(state, data = {}) {
		state.routeList = data
	},
	SET_CLOSEROUTER(state, data = {}){
		state.closeRoute = data
	},
	SET_HOMEINDENT(state, data = false){
		state.homeIndent = data
	},
	SET_SELL_EMLOYEE_ID(state, data = null ){
		state.indexFkSellEmployeeId = data
	},

}
const actions = {
	GET_CITY: (store) => {
		API.queryAreaCode({
			level: 2
		}).then(res => {
			store.commit('SET_CITY', res.data)
		}).catch(() => {
		})
	},
	GET_PROVINCES: (store) => {
		API.queryAreaCode({
			level: 1
		}).then(res => {
			store.commit('SET_PROVINCES', res.data)
		}).catch(() => {
		})
	},
	GET_USERINFO: (store) => {
		API.getInfo({}).then(res => {
			if (res.data.employeeId) {
				store.commit('SET_USERINFO', res.data)
			} else {
				
				window.location.href = `http://${window.location.host}/#/login`
			}
		}).catch(() => {
		})
	},
	
}
export default {
	state,
	getters,
	mutations,
	actions
}
