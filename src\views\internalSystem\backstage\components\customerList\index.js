import API from "@/api/internalSystem/customerManage/customerInfo";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import { getOptions } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";
export default {
  name: "customerList",
  components: {
    TableView,
    Pagination,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customerName: "",
        customerStage: "",
        fk_sale_employee_id: "",
        fk_maintain_employee_id: "",
      },
      tableList: [
        {
          name: "客户编码",
          value: "customer_no",
          width: 80,
        },
        {
          name: "客户名称",
          value: "customer_name",
        },
        {
          name: "维护结束时间",
          value: "maintain_stop_time",
          width: 94,
        },
        {
          name: "客户阶段",
          value: "customer_stage_format",
          width: 70,
        },
        // {
        //   name: "所有端口数",
        //   value: "port_number",
        //   width: 90
        // },
        {
          name: "端口数",
          value: "sure_port_number",
          width: 90,
        },
        {
          name: "联系人",
          value: "link_man",
          width: 60,
        },
        {
          name: "手机",
          value: "telephone",
          width: 100,
        },
        {
          name: "销售员姓名",
          value: "fk_sale_employee_id_name",
          width: 82,
        },
        {
          name: "销售员部门",
          value: "department_name",
          width: 82,
        },
        {
          name: "备案日期",
          value: "update_time",
          width: 90,
        },
        {
          name: "成交日期",
          value: "deal_time",
          width: 90,
        },
      ],
      customerStageList: [],
      employeeList: [],
      // contractSellType:0
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "客户列表",
    },
    customerStage: {
      type: Number,
    },
    isJudge: {
      type: Boolean,
      default: false,
    },
    //成交型客户 customer_stage = 3,4,5,6
    isDealCustomer: {
      type: Boolean,
      default: false,
    },
    selectMultiple: {
      type: Boolean,
      default: false,
    },

    wechatBindingState: {
      type: Number,
      default: null,
    },
    // isContract:{
    //   type:Boolean,
    //   default:false
    // }
  },
  created() {

  },
  methods: {
    Show() {

      if (!["总经理"].includes(this.cookiesUserInfo.role_name)) {
        this.formSearch.fk_sale_employee_id = this.cookiesUserInfo.userId;
      }
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.customerStageList = getOptions("t_customer", "customer_stage");

            this.getList();
          this.$store.dispatch("getEmployee").then((res) => {
            this.employeeList = res;
          });
        });
      // }, 300);
    },
    getList(f = false) {
      this.loading = true;

      let param = Object.assign(
        this.formSearch,
        this.$refs.cus_pagination.obtain()
      );
      if (f) param.pageNum = 1;
      if (this.isDealCustomer) {
        param.isDealCustomer = true;
      }
      if (this.wechatBindingState) {
        param.wechatBindingState = this.wechatBindingState;
      }
      // param.customerStage = this.customerStage;
      // let isJurisdiction = true;
      // if (this.isJudge) {
      //   isJurisdiction = this.buttonPermissions.length ? (this.existence(this.buttonPermissions, 'ALL_CUSTOMER_INFO_NEW')) : false
      // }
      // param.isJurisdiction = isJurisdiction ? 1 : 0;
      API.query(param)
        .then((res) => {
          this.tableData = res.data;
          this.$refs.cus_pagination.setTotal(res.totalCount);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //提交
    submitForm() {
      if (this.selectMultiple) {
        if (this.selectRecords.length < 1) {
          return this.error("请至少选择一条记录");
        }
        this.$emit("getInfo", this.selectRecords);
      } else {
        if (this.selectRecords.length != 1) {
          return this.error("请选择一条记录");
        }
        this.$emit("getInfo", this.selectRecords[0]);
      }

      this.dialogCancel();
    },
    dialogCancel() {
      // this.contractSellType = 0
      this.dialogVisible = false;
      this.selectRecords = [];
      this.formSearch = {}
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
    rowDblclick(row, column, event) {
      this.selectRecords = [];
      this.selectRecords.push(row);
      this.submitForm();
    },
  },
  computed: {
    ...mapGetters(["buttonPermissions", "params_constant_customer_stage","cookiesUserInfo"]),
  },
};
