<template>
  <div class="body-p10">
    <div class="qr-container">
      <img :src="qrCode" alt="" />
      <p class="qr-des">扫码关注公众号</p>
	    <!-- <el-button  type="primary" @click="confirmBind()">立即绑定</el-button> -->
    </div>
	<div>
  <!-- <div ref="qrBox" ></div> -->

	</div>
    <!-- <el-form :inline="true" :model="formSearch" size="small">
			<el-form-item label="公司名称">
				<el-input v-model="formSearch.company_name" placeholder="请输入公司名称"></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
				<el-button v-permit="'add'" @click="$refs.update.show()" type="success">添加
				</el-button>
			</el-form-item>
		</el-form> -->
    <!-- <table-view
			:tableList="tableList"
			:tableData="tableData"
			@del="del"
			@modify="row => $refs.update.show(row)"
			isEdit='update'
			isDel='del'
		/> -->
    <!-- <Pagination ref="pagination" @success="getList"/> -->
    <!-- <update ref="update" @success="getList" /> -->
  </div>
</template>

<script src="./index.js"></script>
<style scoped lang="scss">
.qr-container{
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  img{
    width: 30%;
  }
  .qr-des{
    font-size: 24px;
    color: #999999;
  }
}
</style>
