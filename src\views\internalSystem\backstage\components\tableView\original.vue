<template>
  <el-table 
    :data="tableData" 
    border align="center" 
    @selection-change="getSelectRecords"
    :height="tableHeight?tableHeight:'100%'" size="mini"
    :header-cell-style="{ 'font-weight': 'bold', color: '#333333' }"
    @row-dblclick="rowDblclick"
    @sort-change="sortChange"
    :row-class-name="tableRowClassName"

    >
    <el-table-column type="selection" width="40" v-if="isSel" > </el-table-column>
    <!-- v-if="isTableIndex" -->
    <el-table-column type="index" width="50" label="序号"  > </el-table-column>
    <template v-for="item in tableList">
      <el-table-column  v-if="!item.isHide" :key="item.value" :label="item.name" :prop="item.value"
        :width="item.width?item.width:''" :sortable="item.sortable?item.sortable:false">
        <template slot-scope="scope">
          <el-popover placement="bottom" title="" width="200" :open-delay="300" trigger="hover"
            :content="scope.row[item.value]+''">
            <div slot="reference" class="ellipsis">{{scope.row[item.value]}}</div>
          </el-popover>
        </template>
      </el-table-column>
      
      <el-table-column v-if="item.isSlot" :key="item.value" :label="item.name" :prop="item.value"
        :width="item.width?item.width:''" :sortable="item.sortable?item.sortable:false" >


        <!-- 销售合同单 => 剩余维护时间 -->
        <template slot-scope="scope" v-if="item.value === 'balance_days'">
          <el-popover placement="bottom" title="" width="200" :open-delay="300" trigger="hover"
            :content="scope.row[item.value]+''">
            <div slot="reference" class="ellipsis" v-if="scope.row[item.value] > 30">{{scope.row[item.value]}}</div>
            <div slot="reference" class="ellipsis" style="color: bleak;font-size: large;" v-if="scope.row[item.value] <=30 && scope.row[item.value] >=0">{{scope.row[item.value]}}</div>
            <div slot="reference" class="ellipsis"  v-if="scope.row[item.value] <0">{{scope.row[item.value]}}</div>

          </el-popover>
        </template>
   

      </el-table-column>

    </template>
    <el-table-column fixed="right" label="操作" :width="handleWidth" v-if="(isEdit||isDel||isThrid)&&isOperation">
      <template slot-scope="scope">
        <el-button @click="modify(scope.row)" type="text" size="small" v-permit="isEdit">详情</el-button>
        <el-button @click="thrid(scope.row)" v-permit="isThrid" v-if="isThrid" type="text" size="small" class="success ml30">{{thridTitle}}
        </el-button>
        <el-button @click="four(scope.row)"  v-permit="isFour" v-if="isFour"  type="text" size="small" class="success ml30">{{fourTitle}}
        </el-button>
        <el-button @click="del(scope.row)" v-permit="isDel" type="text" size="small" class="danger ml30">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>
<script src="./index.js"></script>
<style scoped lang="scss">
  .danger {
    color: #f56c6c;
  }

  .ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

.el-table td,.building-top .el-table th.is-leaf {
    border-bottom:  1px solid #f00;
  }
</style>

<style>
  .el-table .danger-row {
    /* background: #f56c6c;*/
         background: oldlace;
  }

    .el-table .death-row {
    /* background: rgb(148, 148, 148); */
        background: #f0f9eb;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

</style>
