<template>
  <div class="tableCloum">
    <!-- <el-form :model="form" ref="form"> -->
    <vxe-table
      :id="tableId"
      column-key
      border
      resizable
      show-overflow
      show-header-overflow

      highlight-hover-row
      highlight-current-row
      auto-resize
      ref="xTable"
      :height="tableHeight || 400"
      :loading="loading"
      align="center"
      :checkbox-config="{
        reserve: true,
        showHeader: true,
        trigger: 'row',
      }"
      :custom-config="{
        storage: {
          visible: true,
          resizable: true,
        },
      }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        autoClear: false, //是否立即释放焦点
        showStatus: false,
      }"
      :mouse-config="{ selected: true }"
      :edit-rules="editRules"
      :data="form.tableData"
      :keyboard-config="{
        isArrow: true,
        isDel: true,
        isTab: true,

        isChecked: true,
        enterToTab: true,
      }"
      @keydown="keyDownMethods"
      :context-menu="{ header: { options: headerMenus } }"
      @context-menu-click="contextMenuClickEvent"

    >
      <template v-for="(item, index) in showTableHead">
        <vxe-table-column
          :field="item.prop"
          :key="index"
          :title="item.label"
          :width="item.width ? item.width : ''"
          :min-width="'100'"
          v-if="!item.isHide"
          align="center"
          :fixed="isFixed[item.prop] || ''"
          :edit-render="item.isEditConfig ? { name: 'input' } : undefined"
        >
          <!-- :edit-render="{ name: 'input' } " -->
          <!--           :edit-render="item.isEditConfig?{ name: 'input' }:undefined " -->
          <!-- :edit-render="{
            name: '$select',
            options: roleList,
            props: { clearable: true },
            events: { change: roleChangeEvent },
          }" -->
          <template slot="header">
            <div>
              <span v-if="item.need" style="color: red">*</span>{{ item.label }}
            </div>
          </template>
          <!-- 展示的插槽 -->
          <template v-slot="{ row }">
            <div v-if="row[item.prop].type === 'select'">
              {{
                getSelectShowData(
                  row[item.prop].option,
                  row[item.prop].value,
                  item
                )
              }}
            </div>
            <div v-else>
              {{ row[item.prop].value }}
            </div>
          </template>
          <!-- 可编辑的插槽 -->
          <template v-slot:edit="scope">
            <!-- 下拉框 -->
            <div v-if="scope.row[item.prop].type == 'select'">
              <el-select
                v-model="scope.row[item.prop].value"
                style="width: 100%"
                :disabled="scope.row[item.prop].disabled"
                clearable
                v-focus
                filterable
                @change="
                  selectChange(
                    scope.row[item.prop].option,
                    scope.row[item.prop].value,
                    item.prop,
                    scope
                  )
                "
              >
                <el-option
                  v-for="(item2, index) in scope.row[item.prop].option"
                  :key="index"
                  :label="item.isJoin ? item2.label + '%' : item2.label"
                  :value="
                    item2.value ||
                    (item2.value !== 0 && item2.value !== false
                      ? item2.id
                      : item2.value)
                  "
                >
                </el-option>
              </el-select>
            </div>
            <!-- 输入框 -->
            <div v-else-if="scope.row[item.prop].type == 'input'">
              <el-input
                v-model="scope.row[item.prop].value"
                :disabled="scope.row[item.prop].disabled"
                style="width: 100%"
                clearable
                @focus="selectValue($event)"
              ></el-input>
            </div>
            <div v-else-if="scope.row[item.prop].type == 'date'">
              <my-date
                v-model="scope.row[item.prop].value"
                style="width: 100%"
                :disabled="scope.row[item.prop].disabled"
              ></my-date>
            </div>
            <!-- 数字输入框 -->
            <div v-else-if="scope.row[item.prop].type == 'number'">
              <el-input
                @focus="selectValue($event)"
                v-model.number="scope.row[item.prop].value"
                @input="
                  (e) => {
                    item.fn ? priceFn(e, item, scope.row, item.prop) : '';
                  }
                "
                :disabled="scope.row[item.prop].disabled"
                style="width: 100%"
                clearable
              ></el-input>
            </div>
            <div v-else-if="scope.row[item.prop].type == 'float'">
              <el-input
                v-if="scope.row[item.prop].isNeed === true"
                @focus="selectValue($event)"
                v-model="scope.row[item.prop].value"
                placeholder="请填写"
                @blur="
                  (e) => {
                    blurInput(e, item, scope.row, item.prop);
                  }
                "
                :disabled="scope.row[item.prop].disabled"
                style="width: 100%"
                clearable
              ></el-input>

              <el-input
                v-else
                @focus="selectValue($event)"
                v-model="scope.row[item.prop].value"
                placeholder="请填写"
                @input="
                  (e) => {
                    item.fn ? priceFn(e, item, scope.row, item.prop) : '';
                  }
                "
                :disabled="scope.row[item.prop].disabled"
                style="width: 100%"
                clearable
              ></el-input>
            </div>
            <div v-else-if="scope.row[item.prop].type == 'dialog'">
              <el-input
                v-model="scope.row[item.prop].value"
                :disabled="scope.row[item.prop].disabled"
                placeholder="请点击选择"
                style="width: 100%"
                @focus="choose(scope.row[item.prop].dialogType, scope)"
                readonly
                clearable
              >
              </el-input>
            </div>
            <div v-else>{{ scope.row[item.prop] }}</div>
            <!-- <div>{{JSON.stringify(scope.row[item.prop])}}{{item.prop}}</div> -->
          </template>
        </vxe-table-column>
      </template>
      <vxe-table-column
        fixed="right"
        label="操作"
        width="80"
        v-if="isDel"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            @click="del(scope)"
            type="text"
            size="small"
            class="danger ml30"
            >删除</el-button
          >
        </template>
      </vxe-table-column>
    </vxe-table>
    <!-- </el-form> -->
    <bank-list ref="bankList" dialogTitle="银行列表" @getInfo="getBankInfo" />
    <cost-list
      ref="costList"
      dialogTitle="费用明细列表"
      @getInfo="getCostInfo"
    />
    <employee-list
      ref="employeeList"
      dialogTitle="员工列表"
      @getInfo="getEmployeeInfo"
    />
  </div>
</template>
<script src="./index.js"></script>

<style lang="scss" scoped>
@import "@/assets/css/element/font-color.scss";
// .el-form-item--small.el-form-item{
//       margin-bottom: 6px;
// }
.tableCloum {
  height: 100%;
  overflow: hidden;
  min-height: 300px;

  .vxe-table /deep/.vxe-cell {
    padding: 0 !important;
  }
}

/deep/.el-input__inner {
  height: 38px !important;
}
</style>
