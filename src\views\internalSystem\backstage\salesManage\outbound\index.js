import API from "@/api/internalSystem/salesManage/outbound";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import AddOutbound from "./components/addOutbound/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { mapGetters } from "vuex";
export default {
  name: "outbound",
  data() {
    return {
      title: "销售出库单",
      loading: false,
      tableData: [],
      formSearch: {
        fk_sell_employee_id: "",
        customer_name: "",
        startTime: "",
        endTime: "",
      },
      tableList: [
        {
          name: "单据编号",
          value: "outbound_no",
        },
        {
          name: "销售合同号",
          value: "contract_no",
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 70,
        },
        {
          name: "客户名称",
          value: "customer_name",
        },
        {
          name: "出库数量",
          value: "outbound_detail_count",
          width: 70,
        },
        // {
        //   name: "出库金额",
        //   value: "outbound_detail_amount",
        // },
        {
          name: "原有端口数",
          value: "port_number",
        },
        {
          name: "新增端口数",
          value: "add_port_count",
        },
        {
          name: "软件序列号",
          value: "software_no",
        },
        {
          name: "单据备注",
          value: "remark",
        },
      ],
      employeeList: [],
      isAdd: false,

      allList: {
        amount: 0,
      },
    };
  },
  activated() {
    let params = this.$route.params;

    if (params.type === "home") {
      Object.assign(this.formSearch, params);
      this.$refs.addOutbound.dialogCancel(true, false);
    }
  },
  created() {
    if (!["总经理"].includes(this.cookiesUserInfo.role_name)) {
      this.formSearch.fk_sell_employee_id = this.cookiesUserInfo.userId;
    }
  },
  mounted() {
    if (!this.$route.params.type) this.getList();
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
  },
  methods: {
    getList(f = false) {
      this.isAdd = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          if (f) param.pageNum = 1;
          // let isJurisdiction = this.buttonPermissions.length ? (this.existence(this.buttonPermissions, 'ALL_OUTBOUND_NEW')) : false
          // param.isJurisdiction = isJurisdiction ? 1 : 0;
          param.isJurisdiction = this.permissionToCheck("ALL_OUTBOUND_NEW")
            ? 1
            : 0;
          API.query(param)
            .then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
            })
            .finally(() => {
              this.loading = false;
            });
          API.queryAll(param).then((res) => {
            if (res.data.length) this.allList = res.data[0];
          });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addOutbound.Show();
    },
    modify(item) {
      let params = {
        outbound_id: item.outbound_id,
      };
      API.getInfo(params)
        .then((data) => {
          this.isAdd = true;
          this.$refs.addOutbound.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if (item.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }

      let params = {
        outbound_id: item.outbound_id,
        fk_contract_detail_ids: item.fk_contract_detail_ids,
        fk_customer_contract_id: item.fk_customer_contract_id,
        fk_customer_id: item.fk_customer_id,
      };

      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
  },

  components: {
    AddOutbound,
    Pagination,
    TableView,
    MyDate,
  },
  computed: {
    ...mapGetters(["buttonPermissions","cookiesUserInfo"]),
  },
};
