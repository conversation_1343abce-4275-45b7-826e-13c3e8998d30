import API from '@/api/internalSystem/basicInfo/employee/employeeApi.js'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
export default {
  name: "employeeList",
  components: {
    TableView,
    Pagination
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      tableList: [{
          name: "工号",
          value: "employee_number"
        },
        {
          name: "姓名",
          value: "employee_name"
        },
        {
          name: "部门",
          value: "department_name"
        }
      ]
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "员工列表"
    }
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.getList();
        });
      // }, 300);
    },
    getList() {
      this.loading = true;
      let param = Object.assign(this.$refs.employee_pagination.obtain());
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.employee_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    //提交
    submitForm() {
      if (this.selectRecords.length != 1)
        return this.error("请选择一条记录");
      this.$emit("getInfo", this.selectRecords[0]);
      this.dialogCancel();
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.selectRecords = [];
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
  }
};