.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.leftJobTree {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #ccc;
  box-sizing: border-box;
}

.saveBtnBox {
  flex: 0 0 70px;
  position: relative;

  .saveBtn {
    height: 50px;
    width: 50px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.rightTree {

  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #ccc;
  border-left: 0;
  box-sizing: border-box;
  // margin-right: 40px;

  .headButton {
    flex: 0 0 auto;
    height: auto;
  }

  .main-tree {
    flex: 1;
  }
}