import API from '@/api/internalSystem/customerManage/customerInfo'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import FileList from "@/views/internalSystem/backstage/components/fileList/index.vue";
export default {
  name: "contractDetail",
  data() {
    return {
      loading: false,
      tableData: [],
      tableList: [{
          name: "销售合同",
          value: "contract_no",
          width: 140
        },
        // {
        //   name: "客户编号",
        //   value: "customer_no",
        //   width: 100
        // },
        {
          name: "客户名称",
          value: "customer_name",
          width:300
        },
        // {
        //   name: "软件版本",
        //   value: "software_version_format",
        //   width: 100
        // },
        {
          name: "软件授权码",
          value: "software_no",
          width: 140
        },
        {
          name: "增加端口数",
          value: "add_port",
          width: 100
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 100
        },
        // {
        //   name: "使用端口数量",
        //   value: "port_number",
        //   width: 100
        // },

        {
          name: "年维护费比例",
          value: "year_maintain_cost",
          width: 100
        },
        {
          name: "已收款金额",
          value: "amount_received",
          width: 100
        },
        // {
        //   name: "未收款金额",
        //   value: "ont_amount_received",
        //   width: 100
        // },
        {
          name: "已开票金额",
          value: "invoiced_amount",
          width: 100
        },
        {
          name: "软件到期",
          value: "maintain_stop_time",
          width: 100
        },
        // {
        //   name: "未开票金额",
        //   value: "ont_invoiced_amount",
        //   width: 100
        // },
        // {
        //   name: "合同金额",
        //   value: "contract_amount",
        //   width: 100
        // }
      ],
      totalCounts: 0, //总条数
      totalContractAmount: 0, //合同总金额
      totalAmountReceived: 0, //已收款总金额
      totalAmountOutstanding: 0, //未收款总金额
      totalAmountInvoiced: 0, //已开票总金额
      totalUninvoicedAmount: 0, //未开票总金额
      customer_contract_id: 0, // 销售合同单id，查看附件用
    };
  },
  props: {
    customer_id: {
      type: Number
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      API.contractList({
        customer_id: this.customer_id
      }).then(res => {
        this.tableData = res.data;
        if(this.tableData.length){
          this.tableData.map(item=>{
            item['year_maintain_cost'] = Number(item['year_maintain_cost']) && Number(item['year_maintain_cost'])?Number(item['year_maintain_cost']) + '%' : 0
          })
        }
        this.totalCounts = res.totalCount;
        res.data.forEach(item => {
          this.totalContractAmount = parseFloat(this.totalContractAmount) + parseFloat(item.contract_amount);
          this.totalAmountReceived = parseFloat(this.totalAmountReceived) + parseFloat(item.amount_received);
          this.totalAmountOutstanding = parseFloat(this.totalAmountOutstanding) + parseFloat(item.ont_amount_received);
          this.totalAmountInvoiced = parseFloat(this.totalAmountInvoiced) + parseFloat(item.invoiced_amount);
          this.totalUninvoicedAmount = parseFloat(this.totalUninvoicedAmount) + parseFloat(item.ont_invoiced_amount);
        });
      }).finally(() => {
        this.loading = false;
      });
    },
    fileShow(item) {
      this.customer_contract_id = item.customer_contract_id
      this.$refs.fileList.Show("contract", item.customer_contract_id, item)
    }
  },
  components: {
    TableView,
    FileList
  }
};