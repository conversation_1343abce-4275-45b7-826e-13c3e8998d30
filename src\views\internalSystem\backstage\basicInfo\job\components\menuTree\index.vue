<template>
  <div class="treeContainer">
    <div class="head">
      <el-button type="text" size="large" @click="addMenu()">添加一级菜单</el-button>
      <el-input placeholder="输入关键字进行过滤" v-model="filterText" style="margin-top:40px" />
    </div>
    <div class="main-tree">
      <el-tree ref="tree" class="filter-tree" :data="menuTreeList" node-key="menuId" highlight-current
        :expand-on-click-node="false" :show-checkbox="showCheckbox" :default-checked-keys="checkedKey"
        :props="defaultProps" default-expand-all check-strictly :filter-node-method="filterNode"
        @check-change="clickDeal">
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span>{{ node.label }}</span>
          <span>
            <el-button type="text" class="primary" size="mini" @click="chooseAll(data,true)">
              全选
            </el-button>
            <el-button type="text" class="primary" size="mini" @click="chooseAll(data,false)">
              全不选
            </el-button>
            <el-button type="text" class="success" size="mini" @click="addMenu(data)">
              添加子节点
            </el-button>
            <el-button type="text" class="primary" size="mini" @click="editMenu(data)">
              编辑
            </el-button>
            <el-button type="text" class="danger" size="mini" @click="delMenu(data)">
              删除
            </el-button>
          </span>
        </span>
      </el-tree>
    </div>

    <AddMenu ref="AddMenu" :menuId="menuId" :parentId="parentId" :parentName="parentName" @getMenuTree="getMenuTree" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
  @import "../index.scss";
</style>