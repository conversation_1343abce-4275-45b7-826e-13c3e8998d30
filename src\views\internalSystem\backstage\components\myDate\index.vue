<template>
  <div>
    <el-date-picker
      style="width: 100%"
      v-model="dateVal"
      @change="
        (e) => {
          $emit('input', e);
          $emit('changeTime');
        }
      "
      align="right"
      type="date"
      :placeholder="hint"
      value-format="yyyy-MM-dd"
      format="yyyy-MM-dd"
      :picker-options="pickerOptions"
      :disabled="disabled"
    >
    </el-date-picker>
  </div>
</template>

<script>
export default {
  name: "MyDate",
  props: {
    value: {},
    hint: {
      type: String,
      default: "请选择日期",
    },
    isAllDate: {
      type: Boolean,
      default() {
        return true;
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        this.dateVal = newValue;
      },
    },
  },
  data() {
    return {
      dateVal: "",
      pickerOptions: {
        disabledDate: (time) => {
          return this.isAllDate ? false : time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      },
    };
  },
  methods: {},
};
</script>

<style scoped lang="scss">
</style>
