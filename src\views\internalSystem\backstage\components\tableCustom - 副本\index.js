import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import brandApi from "@/api/internalSystem/basicManage/brand";
import BankList from "@/views/internalSystem/backstage/components/bankList/index.vue";
import CostList from "@/views/internalSystem/backstage/components/costList/index.vue";
import EmployeeList from "@/views/internalSystem/backstage/components/employeeList/index.vue";
import {
  dateFormat
} from "@/common/internalSystem/common.js";
export default {
  name: "tableCustom",
  components: {
    MyDate,
    BankList,
    CostList,
    EmployeeList
  },
  props: {
    tableCol: {
      type: Array,
      default () {
        return [{
          label: "模块名称",
          prop: "name"
        }, {
          label: "计量单位",
          prop: "unit"
        }, {
          label: "报价单位(元)",
          prop: "price"
        }]
      },
    },
    obj: {
      type: Object,
      default () {
        return {
          name: {
            value: "",
            type: "select",
            option: [{
              label: "选项一",
              value: "1"
            }]
          },
          unit: {
            value: "",
            type: "select",
            option: [{
              label: "选项一",
              value: "1"
            }]
          },
          price: {
            value: "",
            type: "input"
          },

        }
      }
    },
    isDel: {
      type: Boolean,
      default: true
    },
    itemFlag: {
      type: String,
      default: ''
    },
    special:{
      type: String,
      default: ''
    },
    quotationRateList: {
      type: Array,
      default () {
        return [];
      },
    },
    ruleForm: {
      type: Object,
      default () {
        return null
      }
    }
  },
  data() {
    return {
      form: {
        tableData: []
      },
      rules: {
        input: [{
          required: true,
          message: '请填写',
          trigger: 'blur'
        }],
        select: [{
          required: true,
          message: '请选择',
          trigger: 'change'
        }],
        number: [{
          required: true,
          message: '请输入合法的整数'
        }, {
          pattern: /^[1-9]\d*$/,
          message: '必须为整数',
          trigger: 'change'
        }],
        float: [{
          required: true,
          message: '请输入合法的数字'
        }, {
          pattern: /^(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*))$/,
          message: '请输入合法的数字'
        }],
        dialog: [{
          required: true,
          message: '请选择',
        }]
      },
      index: 0,
      loading:false
    }
  },
  methods: {
    /**
     * 合同产品同步功能模块
     */
    // proSameModule(proList) {
    //   this.form.tableData = []
    //   let item = {}
    //   proList.forEach(element => {
    //     // console.log(element)
    //     item = JSON.parse(JSON.stringify(this.obj))
    //     // console.log(item);
    //     item.contract_amount.value = element.contract_amount.value //合同金额
    //     item.measurement_unit.value = element.measurement_unit.value //计量单位 measurement_unit
    //     item.fk_brand_id.value = element.fk_brand_id.value //模块名称 fk_brand_id
    //     this.form.tableData.push(item)
    //   })
    // },
    //新增
    add() {
      this.form.tableData.push(JSON.parse(JSON.stringify(this.obj)))
    },
    //回填
    add2(obj) {
      this.form.tableData.push(JSON.parse(JSON.stringify(obj)))
    },
    //删除
    del(index) {
      let info = this.form.tableData[index.$index];
      this.$emit('del', info);
      this.form.tableData.splice(index.$index, 1);
    },
    //删除
    delBy(id, data) {
      let delIndex = ""
      this.form.tableData.map((item, index) => {
        if (item[id].value === data) {
          delIndex = index
        }
      })
      this.form.tableData.splice(delIndex, 1);
    },
    empty() {
      this.form.tableData = []
    },
    //获取数据
    getData() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid) => {
          if (valid) {
            resolve(this.form.tableData)
          } else {
            reject('表单验证未通过')
          }
        });
      })
    },
    //获取数据
    getData2() {
      return this.form.tableData;
    },
    priceFn(e, item, obj) {
      e = e || 0;
      switch (item.fn) {
        case "f1":
          if (obj.open_ticket_number.value) {
            let tax = parseFloat(e) * obj.invoice_tax_rate.value / 100;
            obj.tax.value = tax * obj.open_ticket_number.value;
            obj.buckle_open_ticket_unit.value = parseFloat(e) - tax;
            obj.open_ticket_money.value = obj.buckle_open_ticket_unit.value * obj.open_ticket_number.value;
            obj.leved_total.value = obj.open_ticket_money.value + tax * obj.open_ticket_number.value;
          }
          break;
        case "f2":
          if (obj.open_ticket_unit.value) {
            let tax = obj.invoice_tax_rate.value * obj.open_ticket_unit.value / 100;
            obj.tax.value = tax * parseFloat(e);
            obj.buckle_open_ticket_unit.value = obj.open_ticket_unit.value - tax;
            obj.open_ticket_money.value = obj.buckle_open_ticket_unit.value * parseFloat(e);
            obj.leved_total.value = obj.open_ticket_money.value + tax * parseFloat(e);
          }
          break;
        default:
          break;
      }
    },
    //选择
    choose(type, row) {
      this.index = row.$index;
      if (type === 'bank') {
        this.$refs.bankList.Show();
      } else if (type === 'cost') {
        this.$refs.costList.Show();
      } else if (type === 'employee') {
        this.$refs.employeeList.Show();
      }
    },
    getBankInfo(info = {}) {
      this.form.tableData[this.index].bank_name.value = info.bank_name;
      this.form.tableData[this.index].fk_bank_id.value = info.financial_bank_accout_id;
    },
    getCostInfo(info = {}) {
      this.form.tableData[this.index].cost_project_no.value = info.project_no;
      this.form.tableData[this.index].cost_project_name.value = info.project_name;
      this.form.tableData[this.index].cost_detail_no.value = info.cost_detail_no;
      this.form.tableData[this.index].cost_detail_name.value = info.cost_detail_name;
    },
    getEmployeeInfo(info = {}) {
      this.form.tableData[this.index].employee_name.value = info.employee_name;
      this.form.tableData[this.index].department_name.value = info.department_name;
    },
    clear() {
      this.form.tableData = []
    },
    putCustomerPort(params){
      if( this.form.tableData &&  this.form.tableData.length > 0){
        for(let i=0;i< this.form.tableData.length;i++){
          this.form.tableData[i].original_port_count.value = params.port_number
        }
      }
    },
    /**
     * 
     * @param {*} option 字典集合
     * @param {*} value 下拉所选的值
     * @param {*} prop  列名
     * @param {*} item  当前列
     */
    async selectChange(option, value, prop, item) {
      let row = this.form.tableData[item.$index]
      let d = new Date()
      //选择不同的下拉框，有不同的修改
      if (prop === 'fk_brand_id') {
        //选择产品
        let brandData = null
        if(value){
           brandData = await brandApi.getInfo({
            brand_id: value
          })
        }else{
          return
        }

        //赋值基础值
        row.contract_amount.value = brandData.data.brand_sell_price
        row.invoice_tax_rate.value = brandData.data.rate ? (brandData.data.rate + '') : ''
        row.detail_sell_type.value = brandData.data.detail_sell_type
        switch (value) {
          //金万维
          case 71:
            //当天开始顺延一年
            row.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
            d.setFullYear(d.getFullYear() + 1);
            row.new_maintain_stop_time.value = dateFormat("yyyy-MM-dd", new Date(d.getTime() )) // 去掉少一天 - 1000 * 60 * 60 * 24
            break;

          case 47: //吉勤企业管理平台（专业版）[吉勤企业管理平台]
          case 48: //新阳企业管理系统V1.0
            row.software_version.value = 1
            break;
            //如果是子模块，就要查询
          default:

            break;
        }
        //说明是子模块，有上级产品，查上级产品的结束时间
        let parentId = (option.filter(item => item.value === value)[0]).parentId
        if (parentId !== 0) {
          // if(!this.ruleForm.fk_customer_id){
          //   return this.error('选择子模块')
          // }
       let parentBrandObj =   await brandApi.getParentBrandObj({
            customer_id: this.ruleForm.fk_customer_id,
            parentId: parentId
          })
          if(parentBrandObj.data && parentBrandObj.data.length === 1){
            row.maintain_start_time.value = new Date()
            row.new_maintain_stop_time.value = parentBrandObj.data[0].new_maintain_stop_time
          }

        }
      } else if (prop === 'year_maintain_cost') {
        // console.log(row.year_maintain_cost.value)
        // console.log(row.maintain_start_time.value)
        // console.log(row.new_maintain_stop_time.value)
        //如果没有时间，就默认当天
        if (!row.maintain_start_time.value) {
          row.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
        }
        switch (value) {
          //如果是100% 默认三年  否则默认一年
          case '100':
            d.setFullYear(d.getFullYear() + 3);
            row.new_maintain_stop_time.value = dateFormat("yyyy-MM-dd", new Date(d.getTime() )) // 去掉少一天 - 1000 * 60 * 60 * 24
            break;

          default:
            d.setFullYear(d.getFullYear() + 1);
            row.new_maintain_stop_time.value = dateFormat("yyyy-MM-dd", new Date(d.getTime() )) // 去掉少一天 - 1000 * 60 * 60 * 24
            break;
        }

      }


    }
  },
}