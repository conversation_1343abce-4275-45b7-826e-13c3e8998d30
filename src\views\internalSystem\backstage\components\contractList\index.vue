<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    append-to-body
    @close="dialogCancel"
    width="66%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    v-dialogDrag
  >
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-input
          v-model="formSearch.customer_name"
          placeholder="请输入客户名称"
          filterable
          clearable
          style="width: 180px"
        ></el-input>
      </el-form-item>
      <el-form-item v-if="!isOwn">
        <el-select
          v-model="formSearch.fk_sell_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
          style="width: 160px"
          :disabled="oneselfFlag == true && !['总经理','财务经理','财务专员'].includes(cookiesUserInfo.role_name)"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.contract_anexo"
          placeholder="请选择上传附件"
          class="inputBox"
          filterable
          clearable
          style="width: 160px"
        >
          <el-option
            label="已上传附件"
            value="1"
          >
        </el-option>
        <el-option
            label="未上传附件"
            value="2"
          >
        </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.invoice_tax"
          placeholder="请选择发票税率"
          class="inputBox"
          filterable
          clearable
          style="width: 160px"
        >
          <el-option
            label="发票税率>0"
            value="1"
          >
        </el-option>
        <el-option
            label="发票税率=0"
            value="2"
          >
        </el-option>
        </el-select>
      </el-form-item>
                <!-- <el-form-item>
        <el-select
          v-model="formSearch.sell_type"
          placeholder="请选择销售类型"
          class="inputBox"
          filterable
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="item in sell_type"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item> -->


      <!-- <el-form-item>
        <el-select
          v-model="formSearch.sell_type"
          placeholder="请选择销售类型"
          class="inputBox"
          filterable
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="item in sell_type"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <my-date
          v-model="formSearch.startTime"
          hint="请选择开始时间"
          style="width: 160px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.endTime"
          hint="请选择结束时间"
          style="width: 160px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button type="primary" @click="submitForm">选择</el-button>
        <el-button type="primary" @click="dialogCancel">取 消</el-button>
      </el-form-item>
    </el-form>
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      :tableHeight="520"
      @getSelectRecords="getSelectRecords"
      :isSel="true"
      :isDblclick="true"
      @rowDblclick="rowDblclick"
    ></table-view>
    <Pagination ref="con_pagination" @success="getList" />
    <span slot="footer" class="dialog-footer">
      <!-- <el-button type="primary">选择</el-button>
      <el-button>取 消</el-button> -->
    </span>
  </el-dialog>
</template>
<script src="./index.js">
</script>