export default {
  name: "satisfactionInfo",
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        link_man: "",
        customer_name: "",
        telephone: "",
        add_features: "",
        advice: "",
        customer_rating_str: "",
        fk_sale_employee_name: "",
        problem_olving_str: "",
        response_speed_str: "",
        service_attitude_str: "",
        software_is_str: "",
        software_trial_str: ""
      }
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      if (data) {
        this.ruleForm = data;
      }
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        link_man: "",
        customer_name: "",
        telephone: "",
        add_features: "",
        advice: "",
        customer_rating_str: "",
        fk_sale_employee_name: "",
        problem_olving_str: "",
        response_speed_str: "",
        service_attitude_str: "",
        software_is_str: "",
        software_trial_str: ""
      }
    }
  }
};