import envConfig from "@/api/environment.js";
import environment from "@/store/environment.js";
import { Message } from "element-ui";
import Cookies from "js-cookie";
export function GetDownload(params = {}) {
  //导出文件get请求
  // backEndUrl
  let currPath = window.location.origin;
  let baseUrl = "";
  if (currPath.indexOf("test") === -1) {
    baseUrl = "http://yun.jiqinyun.com";
    // baseUrl = "http://192.168.0.26:5006"; //本地环境
  } else {
    baseUrl = "http://test.yun.jiqinyun.com";
  }
  let exportUrl = `${baseUrl}${envConfig.tradeAPI}excel/export?`;
  for (let key in params) {
    exportUrl += `${key}=`;
    let typeStr = typeof params[key];
    if (typeStr === "object") {
      exportUrl += `${JSON.stringify(params[key])}&`;
      
    } else {
      exportUrl += `${params[key]}&`;
    }
  }

  const token = Cookies.get(
    environment.state.defaultConfig[environment.state.env].cookiesKey
  ); //获取token

  exportUrl += `token=${token}`;
  exportUrl = exportUrl.replace("{", "%7b").replace("}", "%7d").replace("[", "%5B").replace("]", "%5D");
  Download(exportUrl);
}

export function Download(url = ``) {
  try {
    let elemIF = document.createElement(`iframe`);
    elemIF.src = url;
    elemIF.style.display = `none`;
    document.body.appendChild(elemIF);
  } catch (e) {
    Message.error("下载错误");
  }
}
