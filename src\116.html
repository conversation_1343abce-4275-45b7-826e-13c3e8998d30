<textarea onmousedown="rightEvent(this,event)" class="textareas_td"
        style="font-size: 30px; margin-bottom: 2px; height: 26px; font-weight: 700;margin-top:10px;"
        onchange="makeExpandingArea(this)">软 件 购 买 合 同 书</textarea>

<table width="100%" border="2" cellspacing="0" cellpadding="0" style="border-collapse: collapse;   ">
        <tbody>
                <tr>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)" style="font-size: 16px;">需方(甲方):</textarea>
                        </td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{contract.customerName}}</textarea></td>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)" style="font-size: 16px;">合同编号：</textarea>
                        </td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{contract.contractNo}}</textarea></td>
                </tr>
                <tr>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)" style="font-size: 16px;">供方(乙方):</textarea>
                        </td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{salesUnit.companyName}}</textarea></td>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)" style="font-size: 16px;">签订地点：</textarea>
                        </td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{salesUnit.operateCityName}}</textarea></td>
                </tr>
        </tbody>
</table>
<textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 36px; font-size: 16px;margin-top: 4px;"
        onchange="makeExpandingArea(this)">甲乙双方经友好协商一致，甲方向乙方购买"{{contractDetail.brandName}}"，根据《中华人民共和国合同法》及其他法律法规签订本合同，并由双方共同恪守.条款如下：  </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
        style="width: 100%; height: 20px; font-size: 17px; font-weight: 700;" rows="1"
        onchange="makeExpandingArea(this)">一、软件产品名称、规格、价格：  </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 20px; font-size: 15px;"
        rows="1"
        onchange="makeExpandingArea(this)">乙方向甲方销售的软件产品为"{{contractDetail.brandName}}"， 签订时间：{{contract.newTime}}  </textarea>
<script>
</script>
<table width="100%"   cellspacing="0" border="2"  cellpadding="0" style="border-collapse: collapse;  ">
        <tbody>
                <tr>
                        <td style="width:50px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">序号</textarea></td>
                        <td style="width:350px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">产品名称</textarea></td>
                        <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">版本</textarea></td>
                        <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">计量单位</textarea></td>
                        <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">合同数量</textarea></td>
                        <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">合同单价(元)</textarea></td>
                        <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">合同金额(元)</textarea></td>
                </tr>
                <start>
                        <tr>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 16px;">{{index}}</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="{{contractDetail.rows}}"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 16px;">{{contractDetail.brandName}}</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 16px;">{{contractDetail.brandVersion}}</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 16px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);">{{contractDetail.measurementUnitName}}</textarea>
                                </td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 16px;">{{contractDetail.contractCount}}</textarea>
                                </td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 16px;">{{contractDetail.contractPrice}}</textarea>
                                </td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 16px;">{{contractDetail.contractAmount}}</textarea>
                                </td>
                        </tr>
                </start>
                <tr>
                        <td colspan="3"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">共计人民币（大写）：{{contract.upCase}}</textarea></td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                        <td colspan="4"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">共计人民币（小写）：￥{{contractDetail.preAmount}}</textarea> </td>
                        <!-- <td colspan="2"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">共计人民币（小写）：</textarea></td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                        <td colspan="3"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">￥{{contractDetail.preAmount}}</textarea> </td> -->
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" 
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" 
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                </tr>
                <tr>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)" style="font-size: 16px;">备注</textarea></td>
                        <td colspan="6"><textarea onmousedown="rightEvent(this,event)"
                                        style="text-align: left; font-size: 16px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108); height: 26px;line-height: 26px;"
                                        class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"> {{contract.remark}} </textarea></td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                </tr>
        </tbody>
</table>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
        style="width: 100%; height: 20px; font-weight: 700; font-size: 17px;margin-top:4px;" rows="1"
        onchange="makeExpandingArea(this)">二、付款方式：  </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 40px; font-size: 16px;"
        rows="1"
        onchange="makeExpandingArea(this)">甲方应在合同生效后壹个工作日内向乙方支付{{contractDetail.prepaymentRate}}%款项,共计:人民币：￥{{contractDetail.preAmount}}元(人民币大写:{{contractDetail.upCasePreAmount}},此合同含增值税专用发票,全款到账授权正式版本。)   </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
        style="width: 100%; height: 20px; font-weight: 700; font-size: 17px;" rows="1"
        onchange="makeExpandingArea(this)">三、版权归属及保密条款：  </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
        style="width: 100%; height: 61px; font-size: 16px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);"
        onchange="makeExpandingArea(this)">本软件之版权归乙方，甲方经由乙方授权使用本软件，其数据版权归甲方。乙方对在本合同签订及履行过程中知悉的甲方的商业秘密(采购价格，采购渠道和供应商单位信息,销售价格，销售客户信息等所有数据)负责保密，保证不向任何第三方进行透露并使用.同时甲方需对本合同价格、软件功能签订的条款等保密，不得向其他用户透入。如其中一方违约则造成的所有损失由违约方承担，包括（但不限于）法院诉讼的费用，合理的律师酬金和费用，所有损失和损害等。   </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
        style="width: 100%; height: 20px; font-weight: 700; font-size: 17px;" rows="1"
        onchange="makeExpandingArea(this)">四、相关服务：（服务内容请根据公司政策调整）  </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 140px; font-size: 16px;"
        onchange="makeExpandingArea(this)">1、安装调试：软件的安装、调试均由乙方负责，除合同条款商定的费用以外，免收软件的安装、调试费用.
2、培训：乙方为甲方提供软件操作的免费培训，培训的方式为：远程培训，程度达到公司员工熟练操作。
   注意:免费维护期一年过后，第二年按软件购买金额的15%且不低于500/年收取软件维护费。
3、乙方对系统提供技术支持，免费维护期满后双方须签订有偿的《软件服务协议》。
A、乙方在国家正常工作日内随时为甲方以电话、传真、邮件等方式免费提供所买产品的服务与技术支持维护。
B、非正常使用造成软件损坏，乙方负责有偿保修;
C、具体内容：维护软件现有功能的正常使用，免费升级大众化功能。  
4、超出上述范围的服务另外收取费用。 </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
        style="width: 100%; height: 40px; font-weight: 700; font-size: 17px;" onchange="makeExpandingArea(this)"
        rows="1">五、许可软件升级乙方为甲方提供有偿许可软件升级服务。具体按照福州吉勤信息科技有限公司规定的全国统一办法执行。    </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
        style="width: 100%; height: 20px; font-weight: 700; font-size: 17px;" rows="1"
        onchange="makeExpandingArea(this)">六、争议解决方式：    </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
        style="width: 100%; height: 56px; font-size: 16px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);"
        onchange="makeExpandingArea(this)">1、双方合同履行过程中发生争议，双方应协商解决或请求调解，否则应提交合同签定地仲裁机关仲裁。
2、甲乙双方确定：以上合同签定，以乙方所在地为准。 
3、未尽事宜双方友好协商解决。    </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas"
        style="width: 100%; height: 20px; font-weight: 700; font-size: 17px;" onchange="makeExpandingArea(this)"
        rows="1">七、本合同正本一式贰份（传真纸有效），甲乙双方各执壹份，经双方签字盖章后生效。    </textarea>
<textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 20px; font-size: 16px;"
        onchange="makeExpandingArea(this)" rows="1">付款资料如下:    </textarea>
<table width="100%" border="2" cellspacing="0" cellpadding="0" style="border-collapse: collapse; ">
        <tbody>
                <tr>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">开 户 名：</textarea></td>
                        <td colspan="3"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{salesUnit.companyName}}</textarea></td>
                        <td width="100px;" style="display: none"><textarea onmousedown="rightEvent(this,event)"
                                        class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)"></textarea>
                        </td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                </tr>
                <tr>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">开 户 行：</textarea></td>
                        <td colspan="3"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{salesUnit.openingBank}}</textarea></td>
                        <td width="100px;" style="display: none"><textarea onmousedown="rightEvent(this,event)"
                                        class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)"></textarea>
                        </td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                </tr>
                <tr>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">帐     户：</textarea></td>
                        <td colspan="3"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{salesUnit.bankAccount}}</textarea></td>
                        <td width="100px;" style="display: none"><textarea onmousedown="rightEvent(this,event)"
                                        class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)"></textarea>
                        </td>
                        <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                        rows="1" onchange="makeExpandingArea(this)"></textarea></td>
                </tr>
                <tr>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">甲方盖章：</textarea></td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{contract.customerName}}</textarea></td>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">乙方盖章：</textarea></td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{salesUnit.companyName}}</textarea></td>
                </tr>
                <tr>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">签定地址：</textarea></td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)" style="font-size: 16px; height: 68px;">{{contract.address}}
  </textarea></td>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">签定地址：</textarea></td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)" style="font-size: 16px; height: 68px;">{{salesUnit.operateAddress}}
  </textarea></td>
                </tr>
                <tr>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">电     话：</textarea></td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 36px;line-height: 38px;">{{contract.customerTelephone}}</textarea>
                        </td>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">电     话：</textarea></td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 36px;line-height: 38px;">{{contract.sellTel}}</textarea></td>
                </tr>
                <tr>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">传     真：</textarea></td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;margin-top:4px;">{{contract.fax}}</textarea></td>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">传     真：</textarea></td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;margin-top:4px;">{{salesUnit.fax}}</textarea></td>
                </tr>
                <tr>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">代理人签字</textarea></td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{contract.linkMan}}</textarea></td>
                        <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                        rows="1" onchange="makeExpandingArea(this)"
                                        style="font-size: 16px; height: 32px;">代理人签 字</textarea></td>
                        <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1"
                                        onchange="makeExpandingArea(this)"
                                        style="font-size: 16px;">{{contract.sellEmployeeName}}</textarea></td>
                </tr>
        </tbody>
</table>

<div style="margin-top: {{marginTop}}px;">


        <textarea onmousedown="rightEvent(this,event)" class="" onchange="makeExpandingArea(this)"
                style="font-weight: 700;font-size: 30px;margin-bottom: 0px;text-align: center;height: 36px;">软 件 服 务 协 议</textarea>
        <table width="100%" border="2" cellspacing="0" cellpadding="0" style="border-collapse: collapse; ">
                <tbody>
                        <tr>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">甲方：</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{contract.customerName}}</textarea></td>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">乙方:</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{salesUnit.companyName}}</textarea></td>
                        </tr>
                </tbody>
        </table>
        <textarea onmousedown="rightEvent(this,event)" class="textareas" rows="1" onchange="makeExpandingArea(this)"
                style="height: 35px; font-size: 14px;margin-top:4px;">甲乙双方经友好协商就甲方"{{contractDetail.brandName}}"（以下统称"本软件"）的服务达成以下协议,并由双方共同恪守。条款如下：  </textarea>
        <textarea onmousedown="rightEvent(this,event)" class="textareas"
                style="font-weight: 700; font-size: 15px; height: 20px;" rows="1"
                onchange="makeExpandingArea(this)">一、为确保软件实施顺利进行、数据的安全及乙方维护时准确判断问题之所在，甲方应提供以下配合：  </textarea>
        <textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)"
                style="height: 66px; font-size: 14px;">1.为"{{contractDetail.brandName}}"提供所需的运行环境包含硬件及系统软件，建议服务器电脑配置双硬盘且操作系统为：Windows 2008 SERVER 或Windows 7/10  32/64位。
2.如需通过互联网远程访问则建议网络宽带为电信或联通，因部分第三方宽带运营商网络问题无法实现互联网访问。
3.做好服务器的管理工作，避免使用来历不明的光盘或U盘，以免感病毒。 
4.在实施过程中,安排专人配合乙方工作。  </textarea>
        <textarea onmousedown="rightEvent(this,event)" class="textareas"
                style="font-weight: 700; font-size: 14px; height: 18px;margin-top:6px;" rows="1"
                onchange="makeExpandingArea(this)">二、乙方为甲方提供以下售后服务：  </textarea>
        <textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)"
                style="height: 70px; font-size: 14px;">1.安装调试
  (1)乙方为甲方首次免费安装调试"{{contractDetail.brandName}}"，协助甲方录入基础数据、设置企业组织机构、建立用户、设置用户权限等工作；
  (2)乙方为甲方提供软件操作的免费培训，培训的方式为：远程培训或现场培训，程度达到公司员工熟练操作。
2.运行维护明细   </textarea>
        <table width="100%" border="2" cellspacing="0" cellpadding="0"
                style="border-collapse: collapse;text-align: right;">
                <tbody>
                        <tr>
                                <th width="12%"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td textareas_center" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px; font-weight: 700;">服务产品</textarea></th>
                                <th><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px; font-weight: 700;">服务内容</textarea></th>
                                <th width="18%"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td textareas_center" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px; font-weight: 700;">服务质量标准和规范</textarea></th>
                                <th width="16%"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td textareas_center" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px; font-weight: 700;">服务价格</textarea></th>
                        </tr>
                        <tr>
                                <td width="12%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="height: 68px; font-size: 14px;">热线咨询 
  0591-87893728</textarea><br></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                style="text-align: left; height: 66px; font-size: 14px;"
                                                onchange="makeExpandingArea(this)">1.通过服务热线诊断并解决日常使用过程中的各类问题。
2.每次电话问题请求保存到客服系统。
3.提交问题解决进度查询。
4.服务质量意见反馈。  </textarea></td>
                                <td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                onchange="makeExpandingArea(this)"
                                                style="height: 35px; font-size: 14px;">1、服务时间：工作日    2、响应时间：8小时</textarea>
                                </td>
                                <td width="14%"><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                onchange="makeExpandingArea(this)"
                                                style="height: 34px; font-size: 14px;">含在标准年服务费中 </textarea></td>
                        </tr>
                        <tr>
                                <td width="12%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">远程运维</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                style="text-align: left; height: 100px; font-size: 14px;"
                                                onchange="makeExpandingArea(this)">1.热线无法解决问题通过远程访问或分析数据方式锁定应用故障并进行排除，包括环境级问题、数据级问题、程序级问题处理。
2.每年协助调整2次业务流程和调整打印报表格式。
3.每年提供5次业务单据调入时找不到数据( 如出库调不到库存等)、查询数据等相关问题的处理。 
4.协助用户通过外网登录的设置问题。 </textarea></td>
                                <td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                onchange="makeExpandingArea(this)"
                                                style="height: 35px; font-size: 14px;">1、服务时间：工作日
2、响应时间：8小时</textarea>
                                </td>
                                <td width="14%"><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                onchange="makeExpandingArea(this)"
                                                style="height: 34px; font-size: 14px;">含在标准年服务费中 </textarea></td>
                        </tr>
                        <tr>
                                <td width="12%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);">对账服务</textarea>
                                </td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                style="text-align: left; height: 20px; font-size: 14px;" rows="1"
                                                onchange="makeExpandingArea(this)">1.每年提供1次有关库存、采购、销售、应收应付的数据对账。 </textarea>
                                </td>
                                <td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                onchange="makeExpandingArea(this)"
                                                style="height: 50px; font-size: 14px;">1、服务时间：工作日    2、响应时间：8小时</textarea>
                                </td>
                                <td width="14%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">含在标准年服务费中</textarea></td>
                        </tr>
                        <tr>
                                <td width="12%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">产品升级服务</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                style="text-align: left; height: 35px; font-size: 14px;"
                                                onchange="makeExpandingArea(this)">1.提供远程升级服务，确保安全成功升级，并提供新应用指导。
2.每季度进行一次软件版本升级。 </textarea></td>
                                <td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                onchange="makeExpandingArea(this)"
                                                style="height: 50px; font-size: 14px;">1、服务时间：工作日    2、响应时间：8小时</textarea>
                                </td>
                                <td width="14%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">含在标准年服务费中</textarea></td>
                        </tr>
                        <tr>
                                <td width="12%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">灾难救援</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                style="text-align: left; height: 35px; font-size: 14px;"
                                                onchange="makeExpandingArea(this)">1.用户系统出现影响全局应用问题，第一时间为客户解决问题。
2.排除用户正常应用过程中的出现故障、确保协同系统稳定运行。 </textarea><br></td>
                                <td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                onchange="makeExpandingArea(this)"
                                                style="height: 50px; font-size: 14px;">1、服务时间：工作日    2、响应时间：8小时</textarea>
                                </td>
                                <td width="14%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">含在标准年服务费中</textarea></td>
                        </tr>
                        <tr>
                                <td width="12%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">增值服务</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                style="text-align: left; height: 33px; font-size: 14px;"
                                                onchange="makeExpandingArea(this)">1.所有正常交纳软件维护费的用户均可免费使用增值服务功能，到期没有交纳维护费，则增值功能将不能使用。 </textarea>
                                </td>
                                <td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                onchange="makeExpandingArea(this)"
                                                style="height: 50px; font-size: 14px;">1、服务时间：工作日    2、响应时间：8小时</textarea>
                                </td>
                                <td width="14%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">含在标准年服务费中</textarea></td>
                        </tr>
                        <tr>
                                <td width="12%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">数据迁移</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                style="text-align: left; height: 33px; font-size: 14px;" rows="1"
                                                onchange="makeExpandingArea(this)">1.每年免费1次协助用户将原服务器数据迁移到指定的新服务器，保证系统迁移成功。  </textarea>
                                </td>
                                <td width="16%"><textarea onmousedown="rightEvent(this,event)" class="textareas"
                                                onchange="makeExpandingArea(this)"
                                                style="height: 50px; font-size: 14px;">1、服务时间：工作日    2、响应时间：8小时</textarea>
                                </td>
                                <td width="14%"><textarea onmousedown="rightEvent(this,event)" class="textareas_td"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">含在标准年服务费中</textarea></td>
                        </tr>
                </tbody>
        </table>
        <textarea onmousedown="rightEvent(this,event)" class="textareas" onchange="makeExpandingArea(this)"
                style="height: 66px; font-size: 14px;margin-top:4px;">3、维护费用
  (1)乙方对系统提供终身技术支持，维护费金额{{contractDetail.maintenanceFeeAmount}}元,收到服务费后提供相应的增值功能，（航天开票接口、合同改价单、吉勤工控通、产品面价更新等)
  (2) 维护时间：{{contract.maintainStartTime}} 到 {{contract.maintainStopTime}} </textarea>
        <textarea onmousedown="rightEvent(this,event)" class="textareas" rows="1" onchange="makeExpandingArea(this)"
                style="height: 17px; font-size: 14px;">付款资料如下: </textarea>
        <table width="100%" border="2" cellspacing="0" cellpadding="0" style="border-collapse: collapse; ">
                <tbody>
                        <tr>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);">开 户 名：</textarea>
                                </td>
                                <td colspan="3"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td textareas_center" rows="1"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{salesUnit.companyName}}</textarea></td>
                                <td style="display: none"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"></textarea></td>
                                <td style="display: none"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"></textarea></td>
                        </tr>
                        <tr>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);">开 户 行：</textarea>
                                </td>
                                <td colspan="3"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td textareas_center" rows="1"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{salesUnit.openingBank}}</textarea></td>
                                <td style="display: none"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"></textarea></td>
                                <td style="display: none"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"></textarea></td>
                        </tr>
                        <tr>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">帐      户：</textarea></td>
                                <td colspan="3"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td textareas_center" rows="1"
                                                onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{salesUnit.bankAccount}}</textarea></td>
                                <td style="display: none"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"></textarea></td>
                                <td style="display: none"><textarea onmousedown="rightEvent(this,event)"
                                                class="textareas_td" rows="1"
                                                onchange="makeExpandingArea(this)"></textarea></td>
                        </tr>
                </tbody>
        </table>
        <textarea onmousedown="rightEvent(this,event)" class="textareas" rows="1"
                style="font-weight: 700; font-size: 14px; height: 18px;margin-top:6px;"
                onchange="makeExpandingArea(this)">三、本协议作为销售合同的附加协议，壹式两份，甲乙双方各执壹份，具有同等法律效力。 </textarea>
        <table width="100%" border="2" cellspacing="0" cellpadding="0" style="border-collapse: collapse; ">
                <tbody>
                        <tr>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)">甲方盖章：</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{contract.customerName}}</textarea></td>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">乙方盖章：</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{salesUnit.companyName}}</textarea></td>
                        </tr>
                        <tr>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">代 理 人：</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{contract.linkMan}}</textarea></td>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">代 理 人：</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{contract.operatorEmployeeName}}</textarea>
                                </td>
                        </tr>
                        <tr>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">时      间：</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{contract.newTime}}</textarea></td>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">时      间：</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{contract.newTime}}</textarea></td>
                        </tr>
                        <tr>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">传      真：</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{contract.fax}}</textarea></td>
                                <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">传      真：</textarea></td>
                                <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td textareas_center"
                                                rows="1" onchange="makeExpandingArea(this)"
                                                style="font-size: 14px;">{{salesUnit.fax}}</textarea></td>
                        </tr>
                </tbody>
        </table>
</div>