<template>
  <div class="body-p10 congested-slid">
    <div class="header-info-list">
      <div class="info-item order cursor-pointer" @click="auditList">
        <div class="item-title">
          <p>未审核任务</p>
          <img :src="Img.statisticsOrder" height="40" width="50" />
        </div>
        <p class="number">{{ auditNum }}<span class="unit">单</span></p>
      </div>
      <div class="info-item user">
        <div class="item-title">
          <p>今日游览用户</p>
          <img :src="Img.statisticsUser" height="46" width="46" />
        </div>
        <p class="number">{{ userCount }}<span class="unit">位</span></p>
      </div>
      <div class="info-item up">
        <div class="item-title">
          <p>今日新增用户</p>
          <img :src="Img.statisticsUp" height="46" width="46" />
        </div>
        <p class="number">{{ addUserCount }}<span class="unit">位</span></p>
      </div>
    </div>
    <div class="row-list">
      <div class="col-item col-8">
        <div class="msg-content">
          <div class="msg-title">我的消息</div>
          <ul class="msg-list">
            <li class="msg-item" v-for="(item,index) in msgList" :key="index" @click="toTarget(item)">
              <div class="item-info">
                <p class="item-title" :class="{ readed:item.markRead === 1 }">{{ item.title }}</p>
                <div class="item-handle">
                  <i class="del iconfont" @click.stop="del(item)">&#xe6a2;</i>
                  <i class="type iconfont" v-if="item.markRead === 0">&#xe758;</i>
                </div>
              </div>
              <div class="item-other" :class="{ readed:item.markRead === 1 }">
                <p class="item-type">{{ returnType(item) }}</p>
                <div class="item-time">{{ item.gmtCreate }}</div>
              </div>
            </li>
            <div class="no-data" v-if="msgList.length === 0">
              <img :src="Img.noData" />
            </div>
          </ul>
        </div>
      </div>
      <div class="col-item col-16">
        <div class="panel-content">
          <div class="panel-title">游览用户量趋势</div>
          <div class="panel-list">
            <vue-charts :options="options" />
          </div>
        </div>
      </div>
    </div>
    <div class="row-list">
      <!--<div class="col-item col-8">-->
      <!--<div class="panel-content">-->
      <!--<div class="panel-title">商品游览排行</div>-->
      <!--<ul class="panel-list">-->
      <!--<li class="panel-item" v-for="(item,index) in proBrowseList" :key="index">-->
      <!--<p class="model">-->
      <!--<span class="serial">{{ index + 1 }}</span>-->
      <!--{{ item.keyword }}-->
      <!--</p>-->
      <!--<p class="num">{{ item.queryFrequencyCount }}</p>-->
      <!--</li>-->
      <!--</ul>-->
      <!--</div>-->
      <!--</div>-->
      <div class="col-item col-8">
        <div class="panel-content">
          <div class="panel-title">商品搜索排行</div>
          <ul class="panel-list">
            <li class="panel-item" v-for="(item,index) in proSearchList" :key="index">
              <p class="model">
                <span class="serial">{{ index + 1 }}</span>
                {{ item.keyword }}
              </p>
              <p class="num">{{ item.queryFrequencyCount }}</p>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import Img from '@/assets/images/mall_admin/index'
  import API from '@/api/internalSystem/common/index.js'
  import Echarts from 'vue-echarts'
  // 引入折线图等组件
  import 'echarts/lib/chart/line'
  // 引入提示框和title组件
  import 'echarts/lib/component/title'
  import 'echarts/lib/component/tooltip'
  import 'echarts/lib/component/legend'
  export default {
    name: 'StatisticsPage',
    components: {
      'vue-charts': Echarts
    },
    data() {
      return {
        Img,
        msgList: [],
        userCount: '',
        proSearchList: [],
        proBrowseList: [],
        addUserCount: '',
        options: {
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            label: {
              normal: {
                show: true,
                position: 'top'
              }
            },
            data: [],
            type: 'line'
          }]
        },
        auditNum: ''
      }
    },
    mounted() {
      this.getAuditCenter();
    },
    methods: {
      getAuditCenter() {
        API.getAuditCount({}).then(res => {
          this.auditNum = res.totalCount
        })
      },
      auditList() {
        if (!this.auditNum)
          return this.error("无未审核单据");
        this.$router.push({
          path: '/backstage/customerManage/auditCenter'
        });
      },
    }
  }
</script>

<style scoped lang="scss">
  .echarts {
    width: 100%;
    height: 100%;
  }

  .body-p10 {
    padding: -15px !important;
    // background-color: #f5f5f5;
  }

  .row-list {
    display: flex;
    margin: 0 -10px 20px -10px;

    .col-item {
      position: relative;
      padding: 0 10px;
      box-sizing: border-box;

      &.col-8 {
        width: 33.33%;
      }

      &.col-16 {
        width: 66.66%;
      }
    }
  }

  .header-info-list {
    display: flex;
    margin: 0 -10px 20px -10px;

    .info-item {
      position: relative;
      width: 33.33%;
      margin: 0 10px;
      height: 160px;
      box-sizing: border-box;
      padding: 30px;
      border-radius: 8px;
      color: white;

      &.order {
        background: linear-gradient(to right, #5773f4, #c080f6);
      }

      &.user {
        background: linear-gradient(to right, #6097e8, #6fc2e2);
      }

      &.up {
        background: linear-gradient(to right, #d85753, #ea9895);
      }

      .item-title {
        position :relative p {
          font-size: 20px;
        }

        img {
          width: auto;
          position: absolute;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
        }
      }
    }

    .number {
      position: absolute;
      left: 30px;
      bottom: 30px;
      font-size: 40px;
      letter-spacing: 0.2em;

      .unit {
        font-size: 16px;
      }
    }

    .money {
      position: absolute;
      right: 30px;
      bottom: 30px;
      font-size: 34px;
      letter-spacing: 0.2em;

      .unit {
        font-size: 16px;
      }
    }
  }

  .panel-content {
    font-size: 14px;
    color: #333;
    background-color: white;
    border-radius: 4px;
    box-shadow: 1px 1px 7px #d9d9d9;

    .panel-title {
      padding: 0 20px;
      border-bottom: 1px solid #e6e6e6;
      font-size: 16px;
      line-height: 50px;
    }

    .panel-list {
      height: 240px;
      overflow: auto;

      .panel-item {
        position: relative;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;

        &::after {
          display: block;
          content: "";
          height: 1px;
          position: absolute;
          left: 20px;
          right: 20px;
          bottom: 0;
          background-color: #e6e6e6;
        }

        .model {
          color: #333;

          .serial {
            color: #1a9fd3;
            font-weight: bold;
            margin-right: 15px;
          }
        }

        .num {
          color: #e65c5a;
        }
      }
    }
  }

  .msg-content {
    font-size: 14px;
    color: #333;
    background-color: white;
    border-radius: 4px;
    box-shadow: 1px 1px 7px #d9d9d9;

    .msg-title {
      padding: 0 20px;
      border-bottom: 1px solid #e6e6e6;
      font-size: 16px;
      line-height: 50px;
    }

    .msg-list {
      height: 240px;
      overflow: auto;

      .msg-item {
        position: relative;
        padding: 10px 20px;
        cursor: pointer;

        &:hover {
          background-color: #ebf2f7;

          .item-info {
            .item-handle {
              .del {
                display: block;
              }

              .type {
                display: none;
              }
            }
          }
        }

        &::after {
          display: block;
          content: "";
          height: 1px;
          position: absolute;
          left: 20px;
          right: 20px;
          bottom: 0;
          background-color: #e6e6e6;
        }

        .item-info {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .item-title {
            &.readed {
              color: #ccc;
              font-weight: normal;
            }

            font-weight: 500;
            line-height: 1.5;
          }

          .item-handle {
            display: flex;
            align-items: center;

            .del {
              color: #3b8fd4;
              margin-top: -2px;
              font-size: 14px;
              cursor: pointer;
              display: none;
            }

            .type {
              color: red;
            }
          }
        }

        .item-other {
          &.readed {
            color: #ccc;
          }

          margin-top :5px;
          color :#999;
          display :flex;
          align-items :center;
          justify-content: space-between;
        }
      }
    }
  }

  .no-data {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 30%;
    }
  }
</style>