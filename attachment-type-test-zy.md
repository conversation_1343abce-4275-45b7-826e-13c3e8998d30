# 附件类型功能测试说明 (zy_front_end 分支)

## 功能改动说明

在现有的附件列表上传功能基础上，增加了附件类型选择功能。用户必须先选择附件类型后才能上传文件。

### 修改的文件：
1. `/src/views/internalSystem/backstage/components/fileList/index.vue` - 模板文件
2. `/src/views/internalSystem/backstage/components/fileList/index.js` - 逻辑文件

### 主要改动：

1. **UI 改动**：
   - 添加了附件类型下拉选择框
   - 附件类型包含三个选项：合同附件、发票附件、其他附件
   - 未选择类型时，上传按钮禁用，并显示红色提示文字
   - 在文件列表表格中增加了"附件类型"列

2. **数据结构改动**：
   - 新增 `selectedFileType` 数据字段存储当前选中的附件类型
   - 新增 `fileTypeMap` 映射对象，用于类型值和显示文本的转换
   - 表格配置中增加了附件类型列

3. **逻辑改动**：
   - `checkFile` 方法增加了类型选择验证
   - `upLoadSuccess` 方法中的上传参数增加了 `file_type` 字段
   - `getList` 方法处理返回数据时，添加了 `file_type_text` 显示字段
   - 对话框打开和关闭时会重置类型选择

### 需要后端配合的接口改动：

所有的附件添加接口都需要支持新增的 `file_type` 参数：

1. **实施单附件上传接口**：
   - 接口：`POST ${environment.internalSystemAPI}implementation/addFile`
   - 新增参数：`file_type` (值为: contract/invoice/other)

2. **合同单附件上传接口**：
   - 接口：`POST ${environment.internalSystemAPI}customerContract/addFile`
   - 新增参数：`file_type` (值为: contract/invoice/other)

3. **开票单附件上传接口**：
   - 接口：`POST ${environment.internalSystemAPI}openTicket/addFile`
   - 新增参数：`file_type` (值为: contract/invoice/other)

4. **产品信息附件上传接口**：
   - 接口：`POST ${environment.internalSystemAPI}brand/addFile`
   - 新增参数：`file_type` (值为: contract/invoice/other)

### 列表接口返回数据改动：

所有的附件列表接口返回的数据需要包含 `file_type` 字段：

1. `POST ${environment.internalSystemAPI}implementation/fileList`
2. `POST ${environment.internalSystemAPI}customerContract/fileList`
3. `POST ${environment.internalSystemAPI}openTicket/fileList`
4. `POST ${environment.internalSystemAPI}brand/fileList`

返回数据示例：
```json
{
  "data": [{
    "anexo_id": 1,
    "file_name": "合同文件.pdf",
    "file_size": "1.2MB",
    "file_url": "http://...",
    "file_type": "contract"  // 新增字段
  }],
  "totalCount": 10
}
```

## 测试步骤

1. 在任意使用附件功能的模块（合同管理、实施管理、开票管理、品牌管理）中点击"附件"按钮
2. 确认上传按钮默认是禁用状态
3. 选择附件类型后，上传按钮变为可用
4. 上传文件，确认文件列表中显示正确的附件类型
5. 关闭再打开对话框，确认附件类型选择被重置

## 注意事项

此版本基于 zy_front_end 分支，包含了额外的功能如邮件发送、文件预览等。