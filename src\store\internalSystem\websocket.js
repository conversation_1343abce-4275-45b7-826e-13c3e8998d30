  import environment from "@/api/environment";
const state = {
  ws: null // websocket监听对象
}
const getters = {
  ws: state => state.ws
}
const mutations = {
  SET_WS(state, ws = null) {
    if (!ws) return
    state.ws = ws
  }
}
const actions = {
  INIT_WS: (store) => {
    let protocol = location.protocol === 'https:'?'wss':'ws';
    console.log(`${protocol}${environment.internalsystemWebsocket}`);
    let ws = new WebSocket(`${protocol}${environment.internalsystemWebsocket}`)//先注释掉
    ws.onopen = () => {
      store.commit('SET_WS', ws)
    };
    ws.onclose = () => {
      store.dispatch("INIT_WS")
    };
    ws.onerror = () => {
      console.log("连接出错")
      store.dispatch("INIT_WS")
    }
  },

}
export default {
  state,
  getters,
  mutations,
  actions
}