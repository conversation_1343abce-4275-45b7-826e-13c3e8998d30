<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>吉勤科技</title>
    <style>
      .headForm-li {
        list-style-type: none;
        text-align: center;
        margin-top: 30px;
        width: 100%;
        height: 45px;
        display: flex;
        float: left;
      }

      .labelBox {
        width: 140px;
        flex: 0 0 140px;
        height: 100%;
        text-align: center;
        overflow: hidden;
      }

      .labelName {
        display: inline-block;
        width: 100%;
        line-height: 20px;
        font-size: 14px;
        overflow: hidden;
      }

      .headFormData {
        line-height: 22px;
        width: calc(100% - 140px);
        height: 100%;
        flex: 1;
        border-bottom: 1px solid #000;
        display: flex;
        align-items: center;
      }

      .headFormData-span {
        width: 100%;
        word-wrap: break-word;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        -webkit-box-align: stretch;
      }

      .tr-td {
        text-align: center;
        min-height: 30px;
        height: 50px;
        min-width: 150px;
        padding: 0 10px;
        border-bottom: 1px solid #999;
        border-right: 1px solid #999;
      }
    </style>
  </head>
  <body>
    <h2 style="margin-top: 20px;font-size: 1.6rem; text-align: center">
      装箱单
    </h2>
    <ul
      style="width: 95%;margin: 40px auto; list-style: none; padding-left: 0; height: 500px;"
    >
      <li class="headForm-li">
        <div class="labelBox">
          <label class="labelName">收件人</label>
          <label class="labelName">Conslgeen:</label>
        </div>
        <div class="headFormData">
          <span class="headFormData-span">&&conslgeen@@</span>
        </div>
      </li>
      <li class="headForm-li">
        <div class="labelBox">
          <label class="labelName">公司名称</label>
          <label class="labelName">Company Name:</label>
        </div>
        <div class="headFormData">
          <span class="headFormData-span">&&company_name@@</span>
        </div>
      </li>
      <li class="headForm-li">
        <div class="labelBox">
          <label class="labelName">地址</label>
          <label class="labelName">Address:</label>
        </div>
        <div class="headFormData">
          <span class="headFormData-span">&&address@@</span>
        </div>
      </li>
      <li class="headForm-li">
        <div class="labelBox">
          <label class="labelName">城市/城区号码</label>
          <label class="labelName">Town/Area Num:</label>
        </div>
        <div class="headFormData">
          <span class="headFormData-span">&&area_num@@</span>
        </div>
      </li>
      <li class="headForm-li">
        <div class="labelBox">
          <label class="labelName">电话/邮箱</label>
          <label class="labelName">Phone/Email:</label>
        </div>
        <div class="headFormData">
          <span class="headFormData-span">&&phone@@/&&email@@</span>
        </div>
      </li>
      <li class="headForm-li">
        <div class="labelBox">
          <label class="labelName">电话/传真</label>
          <label class="labelName">Phone/Fax:</label>
        </div>
        <div class="headFormData">
          <span class="headFormData-span">&&fax@@</span>
        </div>
      </li>

      <li class="headForm-li">
        <div class="labelBox">
          <label class="labelName">省市/国家:</label>
          <label class="labelName">State/Country:</label>
        </div>
        <div class="headFormData">
          <span class="headFormData-span">&&state@@/&&country@@</span>
        </div>
      </li>
      <li class="headForm-li"></li>
    </ul>

    <div style="width: 100%; overflow-x: auto; height: auto;">
      <table
        style="margin-bottom: 30px; border-top: 1px solid #999; border-left: 1px solid #999;"
        cellspacing="0"
        cellpadding="0"
      >
        <thead>
          <tr>
            <td class="tr-td">
              入库单号
            </td>
            <td class="tr-td">
              品牌
            </td>
            <td class="tr-td">
              型号
            </td>
            <td class="tr-td">
              品名
            </td>
            <td class="tr-td">
              计量单位
            </td>
            <td class="tr-td">
              入库数量
            </td>
            <td class="tr-td">
              销售合同
            </td>
          </tr>
          <tr>
            <td class="tr-td">
              storage number
            </td>
            <td class="tr-td">
              brand
            </td>
            <td class="tr-td">
              model
            </td>
            <td class="tr-td">
              name
            </td>
            <td class="tr-td">
              measure unit
            </td>
            <td class="tr-td">
              public int count
            </td>
            <td class="tr-td">
              sales agreement
            </td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="tr-td">
              &&storageNumber@@
            </td>
            <td class="tr-td">
              &&brand@@
            </td>
            <td class="tr-td">
              &&model@@
            </td>
            <td class="tr-td">
              &&name@@
            </td>
            <td class="tr-td">
              &&measureUnit@@
            </td>
            <td class="tr-td">
              &&publicIntCount@@
            </td>
            <td class="tr-td">
              &&salesAgreement@@
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </body>
</html>
