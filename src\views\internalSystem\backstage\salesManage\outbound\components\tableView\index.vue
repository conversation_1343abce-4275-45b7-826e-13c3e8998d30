<template>
  <div style="flex: 1">
    <vxe-table
      column-key
      border
      resizable
      show-overflow
      highlight-hover-row
      highlight-current-row
      auto-resize
      ref="xTable"
      height="100%"
      align="center"
      :checkbox-config="{
        reserve: true,
        showHeader: true,
        trigger: 'row',
      }"
      :custom-config="{
        storage: {
          visible: true,
        },
      }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        autoClear: false, //是否立即释放焦点
        showStatus: false,
      }"
      :mouse-config="{ selected: true }"
      :data.sync="tableData"
      :expand-config="{
        expandAll: true,
      }"
      :keyboard-config="{
        isArrow: true,
        isDel: true,
        isEnter: true,
        isTab: true,

        isChecked: true,
        enterToTab: true,
      }"
      @keydown="keyDownMethods"
    >
      <vxe-table-column type="selection" width="40" v-if="isSel">
      </vxe-table-column>
      <vxe-table-column type="index" width="50" title="序号">
      </vxe-table-column>

      <vxe-table-column title="合同单号" field="contract_no">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.contract_no"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column title="产品服务" field="brandName">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.brandName"> </el-input>
        </template>
      </vxe-table-column>

      <vxe-table-column title="合同数量" field="contract_count">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.contract_count"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column title="合同单价" field="contract_price">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.contract_price"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column title="计量单位" field="measurement_unit_name">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.measurement_unit_name"> </el-input>
        </template>
      </vxe-table-column>
      <!-- <vxe-table-column title="已出库金额" field="has_contract_amount">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.has_contract_amount"> </el-input>
        </template>
      </vxe-table-column> -->
      <!-- <vxe-table-column title="未出库金额" field="no_contract_amount">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.no_contract_amount"> </el-input>
        </template>
      </vxe-table-column> -->
      <!-- <vxe-table-column
        title="出库金额"
        field="amount"
        :edit-render="{ name: 'input' }"
      >
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.amount" :disabled="!isOperation">
          </el-input>
        </template>
      </vxe-table-column> -->
      <vxe-table-column title="原有端口数" field="original_port_count">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.original_port_count"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column title="新增端口数" field="add_port_count">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.add_port_count"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column title="新维护结束时间" field="new_maintain_stop_time">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.new_maintain_stop_time"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="出库备注"
        field="outbound_remark"
        :edit-render="{ name: 'input' }"
      >
        <template v-slot:edit="scope">
          <el-input
            v-model="scope.row.outbound_remark"
            :disabled="!isOperation"
          >
          </el-input>
        </template>
      </vxe-table-column>

      <!--        v-if="(isEdit || isDel || isThrid) " -->
      <vxe-table-column
        fixed="right"
        title="操作"
        :width="handleWidth"
        v-if="isOperation"
      >
        <template slot-scope="scope">
          <el-button
            @click="del(scope.row)"
            type="text"
            size="small"
            class="danger ml30"
            >删除</el-button
          >
        </template>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>
<script src="./index.js"></script>
<style scoped lang="scss">
.danger {
  color: #f56c6c;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
</style>
