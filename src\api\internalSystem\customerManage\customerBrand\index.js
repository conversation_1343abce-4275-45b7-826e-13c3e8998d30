import Axios from "@/api/index";
import environment from "@/api/environment";
const prefix = `${environment.internalSystemAPI}customerBrand/`;

export default {
  // 查询
  query: (params) => Axios.post(`${prefix}query`, params),
  // 查询产品列表
  getCustomerBrand: (params) => Axios.post(`${prefix}getCustomerBrand`, params),
  // 保存
  saveOrUpdate: (params) => Axios.post(`${prefix}saveOrUpdate`, params),
  // 删除
  remove: (params) => Axios.post(`${prefix}remove`, params),
  // 编辑
  update: (params) => Axios.post(`${prefix}update`, params),
  // 获取单条信息
  getInfo: (params) => Axios.post(`${prefix}getInfo`, params),
  // 获取单条客户财务信息
  getFinanceInfo: (params) => Axios.post(`${prefix}getFinanceInfo`, params),
  // 获取客户的产品信息
  getBrandSoftwareNo: (params) => Axios.post(`${prefix}getBrandSoftwareNo`, params),
};
