import store from '@/store/internalSystem/index.js';


export const getOptions = (tbName = "", tbCode = "") => {
  let s = [];
  if (!store.getters.baseData || !store.getters.baseData.length) return [];
  store.getters.baseData.forEach(item => {
    if (item.tbName == tbName && item.tbCode == tbCode)
      s.push(item)
  });
  return s;
}

// 身份号码获取出生日期
export const getBirthday = (idCard = "") => {
  let birthday = ''
  if (idCard !== null && idCard !== '') {
    if (idCard.length === 15) {
      birthday = '19' + idCard.substr(6, 6)
    } else if (idCard.length === 18) {
      birthday = idCard.substr(6, 8)
    }
    birthday = birthday.replace(/(.{4})(.{2})/, '$1-$2-')
  }
  return birthday
}
//日期转换
export const dateFormat = (format, date) => {
  if (!date) {
    date = new Date();
  }
  const o = {
    "y+": date.getYear(), //year
    "M+": date.getMonth() + 1, //month
    "d+": date.getDate(), //day
    "h+": date.getHours(), //hour
    "H+": date.getHours(), //hour
    "m+": date.getMinutes(), //minute
    "s+": date.getSeconds(), //second
    "q+": Math.floor((date.getMonth() + 3) / 3), //quarter
    S: date.getMilliseconds() //millisecond
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp("(" + k + ")").test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
    }
  }
  return format;
}
