<template>
  <div class="body-p10">
    <el-form
      :inline="true"
      :model="formSearch"
      size="small"
      v-if="!isAdd && !isAudit"
    >
      <el-form-item label="查询条件"> </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.send_user_id"
          placeholder="请选择推送人员"
          class="inputBox"
          filterable
          clearable
          :disabled="!['总经理'].includes(cookiesUserInfo.role_name)"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item>
        <el-select
          v-model="formSearch.fk_template_id"
          placeholder="请选择推送模板"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in templateOption"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <my-date v-model="formSearch.startTime" hint="请选择开始时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.endTime" hint="请选择结束时间"></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <!-- <el-button
          type="primary"
          @click="add"
          v-permit="'ADD_CUSTOMER_PUSH_NEW'"
          >制单</el-button
        > -->
      </el-form-item>
    </el-form>
    <!--       isThrid="SEND_CUSTOMER_PUSH_NEW" 
          :thridTitle="'推送'"
    -->
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd && !isAudit"
      @modify="modify"
      @del="del"
      isEdit="SHOW_SATISFACTION_VISIT_LOG_NEW"
    ></table-view>
    <Pagination
      ref="pagination"
      @success="getList"
      v-show="!isAdd && !isAudit"
    />
    <!-- 新增客户回访单 -->
    <add-customer-push
      ref="addCustomerPush"
      @selectData="getList"
      :templateOption="templateOption"
    />
    <!-- 审核 -->
    <!-- <VisitingAudit ref="visitingAudit" v-show="isAudit" @selectData="getList" /> -->
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.moneyTitle {
  font-size: 14px;
  font-weight: bold;
}

@import "@/assets/css/element/font-color.scss";
</style>