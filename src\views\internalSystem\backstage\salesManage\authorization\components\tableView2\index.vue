<template>
  <div style="flex: 1">
    <vxe-table
      :id="tableId"
      column-key
      border
      resizable
      show-overflow
      show-header-overflow
      highlight-hover-row
      highlight-current-row
      auto-resize
      ref="xTable"
      height="100%"
      :loading="loading"
      :default-expand-all="true"
      align="center"
      :checkbox-config="{
        reserve: true,
        showHeader: true,
        trigger: 'row',
      }"
      :custom-config="{
        storage: {
          visible: true,
        },
      }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        autoClear: false, //是否立即释放焦点
        showStatus: false,
      }"
      :mouse-config="{ selected: true }"
      :edit-rules="editRules"
      :data="tableData"
      :expand-config="{
        expandAll: true,
      }"
      :keyboard-config="{
        isArrow: true,
        isDel: true,
        isEnter: true,
        isTab: true,
        isChecked: true,
        enterToTab: true,
      }"
      @keydown="keyDownMethods"
      :context-menu="{ header: { options: headerMenus } }"
      @context-menu-click="contextMenuClickEvent"
    >
      <template>
        <vxe-table-column
          title="产品服务"
          field="brand_name"
          width="300"
        >
        </vxe-table-column>
        <vxe-table-column
          title="原端口数量"
          field="original_port_count_old"
          width="140"
        >
          <template v-slot:edit="scope">
            <el-input v-model="scope.row.original_port_count_old" :disabled="!isOperation"> </el-input>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新端口数量"
          field="original_port_count_new"
          :width="140"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template v-slot:edit="scope">
            <el-input
            :disabled="!isOperation"
              v-model="scope.row.original_port_count_new"
              @focus="selectValue($event)"
            >
            </el-input>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="原维护结束时间"
          field="new_maintain_stop_time_old"
          width="170"
        >
          <template v-slot:edit="scope">
            <my-date
            :disabled="!isOperation"
              v-model="scope.row.new_maintain_stop_time_old"
              :isAllDate="true"
              hint="请选择原维护结束时间"
            >
            </my-date>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新维护结束时间"
          field="new_maintain_stop_time_new"
          width="170"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template v-slot:edit="scope">
            <my-date
            :disabled="!isOperation"
              v-model="scope.row.new_maintain_stop_time_new"
              :isAllDate="true"
              hint="请选择新维护结束时间"
            >
            </my-date>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="新软件序列号"
          field="software_no"
          width="200"
          :edit-render="{ name: 'input', enabled: true }"
          v-if="checkExist()"
        >
          <template v-slot:edit="scope">
            <el-input
            :disabled="!isOperation"
              v-model="scope.row.software_no"
              @focus="selectValue($event)"
            >
            </el-input>
          </template>
        </vxe-table-column>
      </template>

      <vxe-table-column
        fixed="right"
        title="操作"
        :width="handleWidth"
        v-if="isDel && isOperation"
      >
        <template slot-scope="scope">
          <el-button
            @click="del(scope.row, scope.index)"
            type="text"
            size="small"
            class="danger ml30"
            >删除</el-button
          >
        </template>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>
<script src="./index.js"></script>
<style scoped lang="scss">
.danger {
  color: #f56c6c;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
@import "@/assets/css/element/font-color.scss";
</style>
