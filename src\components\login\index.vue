<template>
  <div>
    <div class="logincontainer">
      <div class="loginBox">
        <div class="leftImg">
          <div class="logo-box">
            <img class="logoImg" :src="logoImg" alt="" />
          </div>
          <div class="erpText-box" v-if="!title">
            <img class="erpImg" :src="ERP" alt="" />
          </div>
          <div class="erpText-box" v-else>
            {{ title }}
          </div>
        </div>
        <div class="rightBox">
          <div class="mainContent">
            <div class="user input-box mt90">
              <span class="icon-box">
                <i class="el-icon-user icon"></i>
              </span>
              <span class="labelName" v-if="isinternal"> 工&emsp;号: </span>
              <span class="labelName" v-else> 手机号: </span>
              <input
                class="ml30"
                oninput="value=value.replace(/[^\d]/g,'')"
                maxlength="11"
                v-model="userName"
                
                :placeholder="`请输入${isinternal ? '工号' : '手机号'}`"
                @mousewheel.native.prevent
              />
            </div>
            <div class="user input-box mt30">
              <span class="icon-box">
                <i class="el-icon-lock icon"></i>
              </span>
              <span class="labelName"> 密&emsp;码: </span>
              <input
                class="ml30"
                v-model="passWord"
                type="passWord"
                placeholder="请输入密码"
                @keyup.13="userLogin"
              />
            </div>
            <div v-if="rememb" class="user check-Box mt20">
              <span>
                <el-checkbox v-model="rememberPass">记住密码</el-checkbox>
              </span>
            </div>
            <div class="loginButton mt20 input-box">
              <el-button
                type="primary"
                class="w100"
                size="langer"
                @click="userLogin"
                :loading="isLogin"
                >登 录
              </el-button>
            </div>
            <div class="mt20 register" v-show="isRegister" @click="goRegister">
              注册新账号
            </div>
          </div>
        </div>
      </div>
      <div class="cName">
        <p class="gsName">福州吉勤信息科技有限公司版权所有©2019</p>
      </div>
    </div>
    <el-button
      type="primary"
      element-loading-text="正在加载免密登陆"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      v-loading.fullscreen.lock="fullscreenLoading"
    />
  </div>
</template>

<script>
import md5 from "js-md5";
import { mapGetters, mapMutations } from "vuex";
import API from "@/api/user/login";
import Cookies from "js-cookie";

export default {
  props: {
    rememb: {
      type: Boolean,
      default: true,
    },
    isRegister: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    isinternal: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters(["token", "defaultConfig", "env", "userLoginInfo"]),
  },
  data() {
    return {
      logoImg:
        "https://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/DH6yACjR_2052_system_logo.png",
      ERP:
        "https://trade-erp.oss-cn-beijing.aliyuncs.com/%E7%A6%8F%E5%B7%9E%E5%90%89%E5%8B%A4%E4%BF%A1%E6%81%AF%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/XSDD/T63mNWTD_2631_system_font.png",
      userName: "",
      passWord: "",
      fullscreenLoading: false,
      isLogin: false,
      rememberPass: false,
      callBackPath: "",
    };
  },
  mounted() {
    let query = this.$route.query;
    this.callBackPath = query.callBackPath || "";
    this.getToken(query.token);
    // if (query.token) return this.$router.replace(`/${this.env}/#/login`);
  },

  methods: {
    getToken(queryToken) {
      let cookieToken = Cookies.get(this.defaultConfig.cookiesKey);
      //store的getter方法里通过cookie取的值貌似有缓存，现在改为在登录组件内自己去获取
      let token = queryToken || cookieToken;

      if (!token) return;
      this.$router.replace(this.$route.path === "/" ? "/" : "/login");
      this.getUserInfo(token);
    },
    getUserInfo(token) {
      this.SET_TOKEN(token);
      this.fullscreenLoading = true;
      API.freeLogin({
        systemId: this.defaultConfig.systemId,
      })
        .then((res) => {
          res.callBackPath = this.callBackPath;
          this.$emit("userLogin", res);
        })
        .catch(() => {
          this.REMOVE_TOKEN();
          this.$router.replace(`/login`);
        })
        .finally(() => {
          setTimeout(() => {
            this.fullscreenLoading = false;
          }, 1000);
        });
    },
    ...mapMutations([
      "SET_USER_LOGIN_INFO",
      "REMOVE_USER_LOGIN_INFO",
      "SET_TOKEN",
      "REMOVE_TOKEN",
      "SET_USERINFO",
    ]),
    getUserLoginInfo() {
      if (!this.userLoginInfo) return;
      this.SetUserLoginInfo(
        this.userLoginInfo.userName,
        window.atob(this.userLoginInfo.passWord),
        true
      );
    },
    userLogin() {
      if (this.userName === "" || this.passWord === "")
        return this.error("用户名或密码未填写");
      let passMd5 = md5(this.passWord.trim());
      let params = {
        phone: this.userName,
        password: passMd5,
        systemId: this.defaultConfig.systemId,
      };
      this.isLogin = true;
      let loginViewInfo = {
        userName: this.userName,
        passWord: this.passWord,
        rememberPass: this.rememberPass,
      };
      let flag = loginViewInfo.rememberPass;
      let userLoginInfo = {
        userName: loginViewInfo.userName,
        passWord: window.btoa(loginViewInfo.passWord),
      };

      API[this.isinternal ? "internalLogin" : "login"](params)
        .then((res) => {
          this.SET_USERINFO(res.data);
          this.SET_TOKEN(res.data.token);

          this.$emit("userLogin", res);
        })
        .catch(() => {
          flag = false;
          this.REMOVE_TOKEN();
          this.SetUserLoginInfo(userLoginInfo.userName, "");
        })
        .finally(() => {
          this.CancelIsLogin();
          if (!flag) userLoginInfo.passWord = "";
          this.SET_USER_LOGIN_INFO(userLoginInfo);
        });
    },
    goRegister() {
      this.$emit("goRegister");
    },
    GetUserLoginInfo() {
      return {
        userName: this.userName,
        passWord: this.passWord,
        rememberPass: this.rememberPass,
      };
    },
    SetUserLoginInfo(
      userName = this.userName,
      passWord = this.passWord,
      rememberPass = this.rememberPass
    ) {
      this.userName = userName;
      this.passWord = passWord;
      this.rememberPass = rememberPass;
    },
    CancelIsLogin() {
      this.isLogin = false;
    },
  },
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
