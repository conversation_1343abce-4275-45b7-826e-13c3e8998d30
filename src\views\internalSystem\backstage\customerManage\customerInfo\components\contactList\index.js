import API from '@/api/internalSystem/customerManage/customerInfo'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddContact from "./components/addContact/index.vue";
export default {
  name: "contactList",
  data() {
    return {
      loading: false,
      tableData: [],
      tableList: [{
          name: "联系人名称",
          value: "linkman_name"
        },
        {
          name: "联系人性别",
          value: "linkman_gender_name",
          width: 100
        },
        {
          name: "电话",
          value: "phone"
        },
        {
          name: "QQ号码",
          value: "qq"
        },
        {
          name: "微信",
          value: "we_chat"
        },
        {
          name: "职位",
          value: "linkman_position"
        },
        {
          name: "邮箱",
          value: "email"
        },
        {
          name: "传真",
          value: "fax"
        },
        {
          name: "类型",
          value: "linkman_type_name"
        },
        {
          name: "地址",
          value: "link_address"
        },
        {
          name: "操作时间",
          value: "update_time",
          width: 120
        }
      ]
    };
  },
  props: {
    customer_id: {
      type: Number
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      let param = Object.assign(this.$refs.contact_pagination.obtain());
      param.customer_id = this.customer_id;
      API.customerLinkmanList(param).then(res => {
        this.tableData = res.data;
        this.$refs.contact_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    add() {
      this.$refs.addContact.Show();
    },
    modify(item) {
      let params = {
        customer_linkman_id: item.customer_linkman_id
      };
      API.getCustomerLinkmanInfo(params)
        .then(data => {
          this.$refs.addContact.Show(data.data);
        })
        .catch(() => {});

    },
    del(item) {
      let params = {
        customer_linkman_id: item.customer_linkman_id
      };
      API.delCustomerLinkman(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },
  components: {
    TableView,
    Pagination,
    AddContact
  }
};