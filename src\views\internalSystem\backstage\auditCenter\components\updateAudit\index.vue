<template>
  <div v-if="dialogVisible" style="overflow-y: auto; overflow-x: hidden">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form :model="ruleForm" ref="ruleForm" label-width="70px" class="mt10" label-position="left" style="margin-left: 20px;margin-right:30px;">
      <el-row :gutter="40">
        <el-col :span="8">
          <el-form-item label="客户编号" prop="customer_no">
            <el-input
              disabled
              v-model="ruleForm.customer_no"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" >
          <el-form-item label="客户阶段" prop="customer_stage">
            <el-select
              disabled
              v-model="ruleForm.customer_stage"
              placeholder="请选择客户阶段"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in params_constant_customer_stage"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售员">
            <el-input
              v-model="fkSaleEmployeeUserInfo"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="8" >
          <el-form-item label="销售员工号" prop="fk_sale_employee_id_number">
            <el-input disabled v-model="ruleForm.fk_sale_employee_id_number" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input
              disabled
              v-model="ruleForm.customer_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="法定名称" prop="customer_legal_name">
            <el-input
              disabled
              v-model="ruleForm.customer_legal_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户法人" prop="customer_legal_person">
            <el-input
              disabled
              v-model="ruleForm.customer_legal_person"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="联系人" prop="link_man">
            <el-input disabled v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="formItem2">
          <el-form-item label="电话" prop="phone">
            <el-input disabled v-model="ruleForm.phone" clearable></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销售员姓名" prop="fk_sale_employee_id_name">
            <el-input
              disabled
              v-model="ruleForm.fk_sale_employee_id_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="邮箱" prop="email">
            <el-input disabled v-model="ruleForm.email" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户简介" prop="customer_synopsis">
            <el-input
              disabled
              v-model="ruleForm.customer_synopsis"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="手机号" prop="telephone">
            <el-input
              disabled
              v-model="ruleForm.telephone"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销售员部门" prop="department_name">
            <el-input
              disabled
              v-model="ruleForm.department_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="公司网站" prop="company_site">
            <el-input disabled v-model="ruleForm.company_site" clearable></el-input>
          </el-form-item>
        </el-col> -->

        <el-col :span="8" class="formItem2">
          <el-form-item label="QQ号码" prop="qq">
            <el-input
              disabled
              
              v-model="ruleForm.qq"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="人员规模">
            <el-input disabled  v-model="ruleForm.personnel_scale_low" style="width:46%;" clearable>
            </el-input>
            <el-button type="text" class="toLabel">-</el-button>
            <el-input disabled  v-model="ruleForm.personnel_scale_high" style="width:46%;" clearable>
            </el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="客户类型" prop="customer_type">
            <el-select disabled v-model="ruleForm.customer_type" placeholder="请选择客户类型" filterable clearable>
              <el-option v-for="item in params_constant_customer_type" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="传真" prop="fax">
            <el-input disabled v-model="ruleForm.fax" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="企业规模">
            <el-input disabled  v-model="ruleForm.company_scale_low" style="width:46%;" clearable>
            </el-input>
            <el-button type="text" class="toLabel">-</el-button>
            <el-input disabled  v-model="ruleForm.company_scale_high" style="width:46%;" clearable>
            </el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所属行业" prop="belong_industry">
            <el-select disabled v-model="ruleForm.belong_industry" placeholder="请选择所属行业" filterable clearable>
              <el-option v-for="item in params_constant_belong_industry" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="邮政编码" prop="postal_code">
            <el-input disabled  v-model="ruleForm.postal_code" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所在省份" prop="province_name">
            <el-input disabled v-model="ruleForm.province_name" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户来源" prop="customer_source">
            <el-select
              disabled
              v-model="ruleForm.customer_source"
              placeholder="请选择客户来源"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in params_constant_customer_source"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="重要等级" prop="important_rank">
            <el-select disabled v-model="ruleForm.important_rank" placeholder="请选择重要等级" filterable clearable>
              <el-option v-for="item in params_constant_important_rank" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所在城市" prop="city_name">
            <el-input disabled v-model="ruleForm.city_name" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="介绍人/公司" prop="introducer_name">
            <el-input disabled v-model="ruleForm.introducer_name" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="详细地址" prop="link_address">
            <el-input
              disabled
              v-model="ruleForm.link_address"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="formItem2">
          <el-form-item label="备案日期">
            <el-date-picker
              style="width: 100%"
              v-model="ruleForm.add_time"
              align="right"
              type="date"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="formItem2">
          <el-form-item label="成交日期">
            <el-date-picker
              style="width: 100%"
              v-model="ruleForm.deal_time"
              align="right"
              type="date"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-checkbox disabled v-model="checked">是否新增客户财务资料</el-checkbox>
    </el-form>
    <el-form
      :model="financeForm"
      ref="financeForm"
       label-width="80px"
      class="mt10"
    >
      <div v-if="checked">
        <div class="add_customer_finance">客户财务资料</div>
        <el-row :gutter="40">
          <el-col :span="8">
            <el-form-item label="客户税号" prop="customer_tax_number">
              <el-input
                disabled
                v-model="financeForm.customer_tax_number"
                clearable
                        onkeyup="this.value=this.value.replace(/[, ]/g,'')" 
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户行" prop="opening_bank">
              <el-input
                disabled
                v-model="financeForm.opening_bank"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收票人" prop="receive_ticket_person">
              <el-input
                disabled
                v-model="financeForm.receive_ticket_person"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="开票地址" prop="open_ticket_address">
              <el-input
                disabled
                v-model="financeForm.open_ticket_address"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" class="formItem2">
            <el-form-item label="银行账号" prop="customer_account">
              <el-input
                disabled
                v-model="financeForm.customer_account"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="收票电话" prop="receive_ticket_phone">
              <el-input
                disabled
                v-model="financeForm.receive_ticket_phone"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="开票电话" prop="open_ticket_phone">
              <el-input
                disabled
                v-model="financeForm.open_ticket_phone"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" class="formItem2">
            <el-form-item label="收票地址" prop="receive_ticket_address">
              <el-input
                disabled
                v-model="financeForm.receive_ticket_address"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisibleAudit"
      append-to-body
      @close="dialogCancelAudit"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="auditForm"
        :rules="rules"
        ref="auditForm"
        label-width="100px"
      >
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select
            v-model="auditForm.auditState"
            placeholder="请选择审核状态"
            filterable
            clearable
          >
            <template v-for="item in contract_auditStateList">
              <el-option :key="item.id" :label="item.label" :value="item.value">
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="auditRemark">
          <el-input
            v-model="auditForm.auditRemark"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('auditForm')"
          :loading="loading"
          >保 存</el-button
        >
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
.toLabel {
  width: 8%;
  color: black;
  cursor: default;
}

.add_customer_finance {
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
  background: #70676733;
}

@import "@/assets/css/element/font-color.scss";
</style>