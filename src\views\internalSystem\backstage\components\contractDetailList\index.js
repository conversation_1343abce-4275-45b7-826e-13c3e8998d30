import API from '@/api/internalSystem/salesManage/contract'
import brandAPI from '@/api/internalSystem/basicManage/brand'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
export default {
  name: "contractDetailList",
  components: {
    TableView,
    Pagination,
    MyDate
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        fk_brand_id: ""
      },
      tableList: [{
          name: "编号",
          value: "contract_no",
          width: 112
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "产品",
          value: "brandName"
        },
        {
          name: "软件版本",
          value: "software_version_name",
          width: 70
        },
        {
          name: "计量单位",
          value: "measurement_unit_name",
          width: 70
        },
        {
          name: "合同数量",
          value: "contract_count",
          width: 70
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 70
        },
        {
          name: "原有端口数",
          value: "original_port_count",
          width: 82
        },
        {
          name: "新增端口数",
          value: "add_port_count",
          width: 82
        },
        {
          name: "发票税率",
          value: "invoice_tax_rate",
          width: 70
        },
        // {
        //   name: "预付款比例(%)",
        //   value: "prepayment_rate",
        //   width: 100
        // },
        {
          name: "年维护费比例(%)",
          value: "year_maintain_cost",
          width: 112
        },
        {
          name: "收款金额",
          value: "receivable_amount",
          width: 82
        },
        {
          name: "收款比例",
          value: "receivable_rate",
          width: 82
        },
        {
          name: "预付款期限",
          value: "prepayment_time_limit",
          width: 82
        },
        {
          name: "客户成交日期",
          value: "fixtrue_time",
          width: 96
        },
        {
          name: "维护起始日期",
          value: "maintain_start_time",
          width: 96
        },
        {
          name: "新维护结束日期",
          value: "new_maintain_stop_time",
          width: 106
        },
        {
          name: "原维护结束日期",
          value: "original_maintain_stop_time",
          width: 106
        }
      ],
      brandList: []
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "合同明细列表"
    },
    detail_type: {
      type: Number
    },
    customer_no: {
      type: String
    },
    outboundState:{
      type:Number,
      default: null
    },
    contract_detail_ids: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          brandAPI.query({
            brand_classify: 1
          }).then(res => {
            this.brandList = res.data;
          }).finally(() => {});
          this.getList();

        });
      // }, 300);
    },
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.con_pagination.obtain());
      if (f)
        param.pageNum = 1;
      param.detail_type = this.detail_type;
        param.outboundState = [0,1].includes(this.outboundState)?this.outboundState:null
    
      API.detailList(param).then(res => {
        this.tableData = res.data;
        this.$refs.con_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    //提交
    submitForm() {
      if (this.selectRecords.length != 1)
        return this.error("请选择一条记录");
      let cur_customer_no;
      if (!this.customer_no){
        cur_customer_no = this.selectRecords[0].customer_no;
      }else{
        cur_customer_no = this.customer_no;
      }


      if (this.selectRecords[0].customer_no != cur_customer_no) {
        return this.error("请选择同一客户的记录");
      }
      if (this.contract_detail_ids.some(eItem => {
          return eItem == this.selectRecords[0].contract_detail_id
        })) {
        return this.error("不可重复调入合同");
      }
      this.$emit("getInfo", this.selectRecords[0]);
      this.dialogCancel();
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.selectRecords = [];
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
    rowDblclick(row, column, event){
      this.selectRecords=[]
      this.selectRecords.push(row)
      this.submitForm()
    }
  }
};