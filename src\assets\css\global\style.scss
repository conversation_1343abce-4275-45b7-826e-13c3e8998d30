
* {
  margin: 0;
  padding: 0;
}

.height-auto {
  width: 100%;
  height: auto;
  flex: 0 0 auto;
}

body {
  color: #606266;
  font-weight: 400;
  font-family: PingFangSC-Regular, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  overflow: hidden;
}

.heightAuto {
  width: 100%;
  height: auto;
}

.widthAuto {
  height: 100%;
  width: auto;
  flex: 0 0 auto;
}

.flexRow {
  display: flex;
}

.flexColumn {
  display: flex;
  flex-direction: column;
}

.congested {
  width: 100%;
  height: 100%;
  overflow: hidden;
}


// 去掉点击后的蓝色边框
input, button, a {
  outline: 0 none !important;
  blr: expression(this.onFocus=this.blur());
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"]{
  -moz-appearance: textfield;
}

img {
  width: 100%;
  height: auto;
}

ul li, ol li {
  list-style-type: none;
}

input, textarea, input:focus, textarea:focus, button {
  outline: none;
  // background: none;
}

a {
  text-decoration: none;
}

.mt5 {
  margin-top: 5px;
}

.mt20 {
  margin-top: 20px !important;
}

.h28 {
  height: 28px !important;
}
.ml5 {
  margin-left: 5px;
}
.ml15 {
  margin-left: 15px;
}

.mt10 {
  margin-top: 10px;
}

.mb20 {
  margin-bottom: 20px;
}
.mb15 {
  margin-bottom: 15px;
}
.mb40 {
  margin-bottom: 40px;
}

.cursor-pointer {
  cursor: pointer;
}

.mb10 {
  margin-bottom: 10px;
}

.mt30 {
  margin-top: 30px !important;
}


.mt40 {
  margin-top: 40px !important;
}


.mt60 {
  margin-top: 60px;
}
.mt69 {
  margin-top: 69px;
}

.fz16{
  font-size: 16px;
}
.fz32{
  font-size: 32px;
}
.mt80 {
  margin-top: 80px;
}

.mt100 {
  margin-top: 100px;
}
.mt90 {
  margin-top: 90px;
}
.pt60 {
  padding-top: 60px;
}
.pt20 {
  padding-top: 20px;
}

.pt30 {
  padding-top: 30px;
}
.pb60 {
  padding-bottom: 60px;
}


.pb30 {
  padding-bottom: 30px;
}

.pb20 {
  padding-bottom: 20px;
}

.mr5 {
  margin-right: 5px;
}

.mr10 {
  margin-right: 10px;
}

.mr20 {
  margin-right: 20px;
}
.mr15 {
  margin-right: 15px;
}
.mt15 {
  margin-top: 15px;
}

.mr30 {
  margin-right: 30px;
}

.mr35 {
  margin-right: 35px;
}
.mr50 {
  margin-right: 50px;
}
.ml20 {
  margin-left: 20px;
}
.ml5{
  margin-left: 5px;
}
.ml10 {
  margin-left: 10px;
}

.ml5 {
  margin-left: 5px;
}

.ml30 {
  margin-left: 30px;
}
.ml50 {
  margin-left: 50px;
}
.ml80 {
  margin-left: 80px;
}
.ml60 {
  margin-left: 60px;
}

.mb30 {
  margin-bottom: 30px;
}
.mb60 {
  margin-bottom: 60px;
}
.pl-0{
padding-left: 0;
}
.pl10{
  padding-left: 10px;
}


.w100 {
  width: 100%;
  flex: 1;
  height: auto;
}

.background-color-white {
  background-color: #fff;
}

.h100 {
  height: 100%;
}


.text-align-center {
  text-align: center;
}

.text-align-right {
  text-align: right;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}




// 自适应
.self-adaption {
  width: auto;
  flex: 0 0 auto;
  height: auto;
}


.tac{
  text-align: center;
}

.tal{
  text-align: left;
}
.tar{
  text-align: right;
}

.rotate90{
  transform: rotate(90deg);
}

.el-menu-item span:hover {
 

  color: #409eff;

}