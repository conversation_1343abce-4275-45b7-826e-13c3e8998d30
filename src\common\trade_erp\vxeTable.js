import { Message } from "element-ui";

/**
 * 表格必填字段校验
 * @param {Object} xTable  表格的引用 例如 this.$refs.xTable
 * @param {String} tableName  表格名，选填
 */
function tableValid(xTable, tableName = "表格") {
  return new Promise((resolve, reject) => {
    xTable.fullValidate((valid, errMap) => {
      if (valid) {
        resolve();
      } else {
        let msgList = [];

        Object.values(errMap).forEach(errList => {
          errList.forEach(params => {
            let { rowIndex, column, rules } = params;

            rules.forEach(rule => {
              msgList.push(
                `${tableName}第 ${rowIndex + 1} 行 ${column.title} 校验错误：${
                  rule.message
                }`
              );
            });
          });
        });

        // let msgTops = "";
        // msgList.map(msg => {
        //   msgTops += `<div class="mt10">${msg}</div>`;
        // });
        let msgTopsOne=msgList[0];  //只报第一条的消息
        Message.error({
          dangerouslyUseHTMLString: true,
          duration: 5000,
          showClose: true,
          offset: 40,
          message: msgTopsOne
        });
        reject("表格必填项校验失败");
      }
    });
  });
}

/**
 * 重新计算表格隐藏字段
 * @param {array} customColumns  绑定的隐藏数组
 * @param {array} defaultHiddenColumns  需要重新计算的数组
 * @param {Object} xTable 表格组件的引用
 */

function resetColumns(customColumns, defaultHiddenColumns, xTable) {
  customColumns.forEach(column => {
    column.visible = defaultHiddenColumns.includes(column.property)
      ? false
      : true;
  });

  xTable.refreshColumn();
}

/**
 * 刷新表格序号
 * @param {array} tableData
 */
function serialRefreshs(tableData = []) {
  tableData.forEach((item, index) => {
    item.num = index + 1;
  });
}
export { tableValid, resetColumns, serialRefreshs };
