import API from "@/api/internalSystem/salesManage/contract";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import AddContract from "./components/addContract/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import AuditDetail from "@/mixins/auditDetail.js";
import { mapGetters } from "vuex";

export default {
  name: "contract",
  mixins: [AuditDetail],
  data() {
    return {
      title: "销售合同单",
      loading: false,
      tableData: [],
      formSearch: {
        customer_name: "",
        fk_sell_employee_id: "",
        add_user_id: "",
        sell_type: "",
        audit_state: '',
        startTime: "",
        endTime: "",
        maintain_start_time: "",
        maintain_stop_time: "",
        has_receivables: "",
        not_has_receivables: "",
        has_open_ticket: "",
        not_has_open_ticket: "",
      },
      tableList: [
        {
          name: "完成情况",
          value: "state_name",
          width: 70,
        },
        {
          name: "审核状态",
          value: "audit_state_name",
          width: 82,
        },
        {
          name: "编号",
          value: "contract_no",
          width: 116,
        },
        {
          name: "客户名称",
          value: "customer_name",
          width: 200,
        },
        {
          name: "销货单位",
          value: "sales_unit_id_format",
        },
        {
          name: "维护起始时间",
          value: "maintain_start_time",
          width: 96,
        },
        {
          name: "维护结束时间",
          value: "maintain_stop_time",
          width: 96,
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 72,
        },
        {
          name: "已收款金额",
          value: "receivables_amount",
          width: 82,
        },
        {
          name: "未收款金额",
          value: "not_receivables_amount",
          width: 82,
        },
        {
          name: "已开票金额",
          value: "open_ticket_amount",
          width: 82,
        },
        {
          name: "未开票金额",
          value: "not_open_ticket_amount",
          width: 82,
        },
        {
          name: "单据备注",
          value: "remark",
        },
        {
          name: "付款方式",
          value: "pay_type_name",
          width: 106,
        },
        {
          name: "培训方式",
          value: "train_type_name",
          width: 70,
        },
        {
          name: "销售类型",
          value: "sale_type_name",
          width: 82,
        },
        {
          name: "推荐员工",
          value: "fk_recommend_employee_name",
          width: 70,
        },
        {
          name: "操作员工",
          value: "add_user_name",
          width: 70,
        },
        {
          name: "操作部门",
          value: "fk_operator_department_name",
          width: 70,
        },
        {
          name: "手机",
          value: "phone",
          width: 100,
        },
        {
          name: "客户传真",
          value: "fax",
          width: 100,
        },
        {
          name: "联系人",
          value: "link_man",
          width: 70,
        },
        {
          name: "介绍人",
          value: "introducer_format",
        },
        {
          name: "介绍合同",
          value: "introducer_contract_format",
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 70,
        },
        {
          name: "销售部门",
          value: "fk_sell_department_name",
          width: 70,
        },
        {
          name: "单据日期",
          value: "add_time",
          width: 90,
        },
        {
          name: "客户地址",
          value: "address",
        },
        {
          name: "联系人QQ",
          value: "link_qq",
          width: 110,
        },
        {
          name: "软件序列号",
          value: "software_no",
          width: 100,
        },
      ],
      employeeList: [],
      isAdd: false,
      allList: {
        contract_amount: 0,
        receivables_amount: 0,
        open_ticket_amount: 0,
        not_receivables_amount: 0,
        not_open_ticket_amount: 0,
      },
    };
  },

  mounted() {
    this.getList();
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
  },
  methods: {
    getList(f = false) {
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          if (f) param.pageNum = 1;

          param.isJurisdiction = this.permissionToCheck("all") ? 1 : 0;
          API.query(param)
            .then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
            })
            .finally(() => {
              this.loading = false;
            });
          API.queryAll(param).then((res) => {
            if (res.data.length) this.allList = res.data[0];
          });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addContract.Show();
    },
    getInfo(customer_contract_id) {
      let params = {
        customer_contract_id: customer_contract_id,
      };
      API.getInfo(params)
        .then((data) => {
          this.isAdd = true;
          this.$refs.addContract.Show(data.data);
        })
        .catch(() => {});
    },
    modify(item) {
      this.getInfo(item.customer_contract_id);
    },
    del(item) {
      if (item.audit_state == 1 || item.audit_state == 0)
        return this.error("该单据已发出审核，不允许删除");
      let params = {
        customer_contract_id: item.customer_contract_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
  },

  components: {
    AddContract,
    Pagination,
    TableView,
    MyDate,
  },
  computed: {
    ...mapGetters([
      "sell_type",
      'customer_contract_audit',
      "sale_type"
    ]),
  },
};
