<template>
  <div class="listContainer">
    <div class="headProject">
      <el-select
        style="width: 400px"
        class="no-border-input"
        v-model="current.brand_id"
        clearable
        @change="(value) => leftModuleDataChange(value)"
        placeholder="请选择所属产品"
      >
        <el-option
          v-for="(item, index) in systemList"
          :key="index"
          :label="item.brand_type"
          :value="item.brand_id"
        >
        </el-option>
      </el-select>
      <el-cascader
        style="width: 400px"
        class="ml10"
        :options="menuTreeList"
        :props="{
          checkStrictly: true,
          value: 'module_id',
          label: 'module_name',
        }"
        @change="moduleChange"
        placeholder="请选择所属模块"
        v-model="treeSelectData"
        clearable
      ></el-cascader>
    </div>

    <div class="headTipList">
      <div class="left">
        <!-- <div class="label-angle mr10">
          {{ current.module_name || "所有模块" }}
        </div> -->
        <div
          class="left-item"
          :class="current.sate_str === item.id ? 'fw' : ''"
          v-for="(item, index) in stateList"
          :key="index"
          @click="stateClick(item.id)"
        >
          {{ item.name }}
        </div>
      </div>
      <div v-if="!outside">
        <el-button type="primary" icon="el-icon-share" @click="share"
          >分享页面
        </el-button>
        <el-button type="primary" icon="el-icon-plus" @click="addBug"
          >提BUG</el-button
        >
      </div>
    </div>
    <div class="listBody">
      <!-- <div class="left" :class="[leftIndent ? 'left-indent' : '']">
        <LeftModule
          :leftIndent="leftIndent"
          ref="leftModule"
          @search="moduleChange"
          @indent="leftIndentChange"
        />
      </div> -->
      <div class="right">
        <ShowTable ref="table" isCheck :outside="outside" />
      </div>
    </div>
  </div>
</template>

<script>
import CommonAPI from "@/api/internalSystem/bugManage/common/index.js";
import API from "@/api/internalSystem/bugManage/module/index.js";

import ShowTable from "./components/table/index.vue";
import LeftModule from "./components/leftModule/index.vue";
export default {
  props: {
    outside: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      leftIndent: false,
      systemList: [],
      menuTreeList: [],
      stateList: [
        { name: "未关闭", id: 1 },
        { name: "所有", id: "" },
        { name: "指派给我", id: 2 },
        { name: "由我创建", id: 3 },
        { name: "由我解决", id: 4 },
        { name: "未确认", id: 5 },
        { name: "未解决", id: 6 },
        { name: "待关闭", id: 7 },
        { name: "过期Bug", id: 8 },
      ],
      current: {
        module_name: "",
        sate_str: 1,
        module_id: "",
        brand_id: "",
      },
      treeSelectData: [],
    };
  },
  components: {
    LeftModule,
    ShowTable,
  },
  mounted() {
    if (this.outside) {
      this.stateList = [
        { name: "未关闭", id: 1 },
        { name: "所有", id: "" },
        // { name: "指派给我", id: 2 },
        // { name: "由我创建", id: 3 },
        // { name: "由我解决", id: 4 },
        { name: "未确认", id: 5 },
        { name: "未解决", id: 6 },
        { name: "待关闭", id: 7 },
        { name: "过期Bug", id: 8 },
      ];
    }
  },
  activated() {
    this.getSystemList();
  },
  methods: {
    leftIndentChange() {
      this.leftIndent = !this.leftIndent;
    },
    addBug() {
      this.$router.push({
        name: "addBug",
      });
    },
    share() {
      let origin = window.location.origin;

      this.$copyText(
        `${origin}/#/outside/outsideBugList?brand_id=${this.current.brand_id}`
      ).then(
        (res) => {
          this.success("链接已复制到剪切板，快去分享吧");
        },
        (err) => {
        }
      );
    },
    stateClick(id) {
      this.current.sate_str = id;
      this.$refs.table.SetParams(this.current);
    },
    moduleChange(item) {
      // this.current.module_name = item.module_name;
      // this.current.module_id = item.module_id;

      // this.$refs.table.SetParams(this.current);
      if (this.treeSelectData.length) {
        let last = this.treeSelectData[this.treeSelectData.length - 1];
        this.current.module_id = last;

        this.$refs.table.SetParams(this.current);
      }
    },
    getSystemList() {
      if (this.current.brand_id && !this.$route.query.brand_id) return;
      CommonAPI.getSystemQuery({ brand_classify: 1 }).then((res) => {
        this.systemList = res.data;

        if (this.$route.query.brand_id) {
          let brand_id = this.$route.query.brand_id;
          let filterArr = this.systemList.filter(
            (item) => item.brand_id == brand_id
          );
          if (filterArr.length && filterArr[0].brand_id) {
            this.current.brand_id = +brand_id;
            this.leftModuleDataChange(brand_id);

            this.$refs.table.SetParams(this.current);
            return;
          }
        }
        if (this.systemList.length) {
          this.current.brand_id = this.systemList[0].brand_id;
          this.leftModuleDataChange(this.systemList[0].brand_id);
          this.$refs.table.SetParams(this.current);
        }
      });
    },
    getMenuTree({ brand_type = "", brand_id = "" }) {
      // 获取模块列表
      // if (!brand_id) return;
      // this.brand_id = brand_id;
      // this.currentId=''
      // this.title=brand_type
      if (!this.current.brand_id) {
        return;
      }
      API.query({
        brand_id: this.current.brand_id,
      })
        .then((data) => {
          this.menuTreeList = data.data;
        })
        .catch(() => {});
    },
    leftModuleDataChange(value) {
      this.getMenuTree("", this.current.brand_id);
      this.current.module_id = "";
      this.current.module_name = "";
      this.treeSelectData = [];
      this.$refs.table.SetParams(this.current);
      // let filterArr = this.systemList.filter((item) => item.brand_id == value);
      // if (filterArr.length && filterArr[0].brand_id)
      //   this.$refs.leftModule.getMenuTree({
      //     brand_id: this.current.brand_id,
      //     brand_type: filterArr[0].brand_type,
      //   });
    },
  },
};
</script>

<style lang="scss" scoped>
.fw {
  font-weight: bold;
}
.listContainer {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .headProject {
    background-color: #f5f5f5;
    padding: 5px 3px;
  }
  .headTipList {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ddd;
    padding: 5px;

    .left {
      display: flex;
      justify-content: flex-start;

      .left-item {
        font-size: 14px;
        padding: 0 5px;
        cursor: pointer;
        line-height: 32px;
        color: #141414;
      }
      .left-item:hover {
        background: #ddd;
        color: #333;
      }
      .left-item-active {
        font-weight: bold;
        color: #333;
        background: 0;
      }
    }
    .label-angle {
      background-color: #d3dff7;
      line-height: 30px;
      padding: 0 5px;
      position: relative;
      top: 2px;
      border: 1px solid #c6d2eb;
      margin-right: 18px;
      font-size: 13px;
      transition: margin 0.2s, padding 0.2s;
      color: #114f8e;
    }
    .label-angle:before,
    .label-angle:after {
      content: " ";
      display: block;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 13px 0 20px 13px;
      border-color: transparent transparent transparent #c6d2eb;
      position: absolute;
      right: -13px;
      top: -1px;
      z-index: 1;
    }
    .label-angle:after {
      border-width: 12px 0 12px 12px;
      top: 0;
      z-index: 2;
      right: -11px;
      border-color: transparent transparent transparent #d3dff7;
    }
  }

  .listBody {
    flex: 1;
    display: flex;
    width: 100%;
    height: 100%;
    .left {
      flex: 0 0 300px;
      margin-top: 35px;
      transition: all 0.3s ease;
      //   padding-top: 20px;
    }
    .left-indent {
      flex: 0 0 30px;
      overflow: hidden;
    }
    .right {
      flex: 1;
      padding-left: 10px;
      overflow-x: hidden;
      padding-top: 35px;
      padding-right: 10px;
    }
  }
}
</style>
