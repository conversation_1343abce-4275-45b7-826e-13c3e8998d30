$searchColor: #00b38b;
	$bgColor: #f5f5f5;
	.technicalSupport {
		width: 100%;
		height: 30px;
		flex: 0 0 30px;
		left: 0;
		text-align: center;
		font-size: 12px;
		line-height: 30px;
		color: #666;
		bottom: 0;
		background-color: $bgColor;
	}
	.container {
		background-color: #f5f5f5;
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.top-nav {
		flex: 0 0 auto;
	}

	.search-container {
		flex: 0 0 auto;
		height: 70px;
		display: flex;
		align-items: center;
		background-color: #f7f7f7;
		.search-input-group {
			height: 33px;
			flex: 1;
			display: flex;
            margin-left: 30px;
            border: 0.5px solid #d8d8d8;
			align-items: center;
            box-sizing: border-box;

			.search-input {
				height: 100%;
				width: 100%;
                background-color: transparent;
				outline: none;
				font-size: 16px;
				border: 0;
                margin: 0  10px;

			}

		}
        .search-btn {
				width: 80px;
                margin-right: 30px;
				background-color: $searchColor;
				font-size: 16px;
				color: white;
				height: 33px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
	}

	.good-list {
		flex: 1;
		overflow-y: scroll;

		.good-item {
			background-color: white;
			padding: 10px;
			border-bottom: 10px solid $bgColor;
			display: flex;
			align-items: center;

			.good-img {
				width: 160px;
				height: 160px;
				background-color: #f7f7f7;
				display: flex;
				justify-content: center;
				align-items: center;

				.iconfont {
					font-size: 48px;
					color: #ccc;
				}
			}

			.good-det {
				/*height: 160rem;*/
				flex: 1;
				margin-left: 30px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.good-brand {
					font-size: 14px;
					background: $bgColor;
					display: inline-block;
					border-radius: 4px;
					color: #999;
					padding: 4px 10px;
					margin-right: 10px;
				}

				.good-name {
					font-size: 12px;
					color: #333;
					font-weight: 500;
				}

				.param-item {
					font-size: 12px;
					line-height: 22px;
					color: #333;

					.param-label {
						color: #999;
					}
				}
			}
			.more-icon{
				color: #cccccc;
				display: flex;
				align-items: center;
				font-size: 14px;
				.iconfont{
					transform: rotate(180deg);
					font-size: 18px;
					margin-left: 2px;
				}
			}
		}

		.empty-container {
			height: 100%;
			background-color: white;
		}
	}
