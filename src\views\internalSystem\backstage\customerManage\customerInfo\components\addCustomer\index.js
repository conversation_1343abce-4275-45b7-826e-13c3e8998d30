import API from '@/api/internalSystem/customerManage/customerInfo'
import CustomerList from "@/views/internalSystem/backstage/components/customerList/index.vue";
import comAPI from '@/api/internalSystem/common/index.js'
import validationRules from "@/common/internalSystem/validationRules.js"
import {
  mapGetters
} from 'vuex'
export default {
  name: "addCustomer",
  components: {
    CustomerList
  },
  data() {
    return {
      ruleForm: {
        fk_sale_employee_id: "",
        link_man: "",
        fk_sale_employee_number: "",
        customer_name: "",
        phone: "",
        fk_sale_employee_name: "",
        customer_synopsis: "",
        telephone: "",
        fk_sale_employee_depot: "",
        customer_legal_name: "",
        email: "",
        company_site: "",
        customer_legal_person: "",
        qq: "",
        personnel_scale_low: "",
        personnel_scale_high: "",
        customer_type: 0,
        fax: "",
        company_scale_low: "",
        company_scale_high: "",
        belong_industry: 0,
        postal_code: "",
        province: "",
        customer_source: "",
        important_rank: 0,
        city: "",
        introducer: "",
        introducerName: "",
        link_address: "",
        customer_stage_remark:"",
        customer_stage: 1
      },
      rules: {
        link_man: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        customer_name: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        // telephone: [{
        //   required: true,
        //   validator: validationRules.checkPhone,
        //   trigger: "blur"
        // }],
        // customer_legal_name: [{
        //   required: true,
        //   message: " ",
        //   trigger: "blur"
        // }],
        // customer_type: [{
        //   required: true,
        //   message: "请选择客户类型",
        //   trigger: "change"
        // }],
        // belong_industry: [{
        //   required: true,
        //   message: "请选择所属行业",
        //   trigger: "change"
        // }],
        // province: [{
        //   required: true,
        //   message: "请选择所在省份",
        //   trigger: "change"
        // }],
        customer_source: [{
          required: true,
          message: " ",
          trigger: "change"
        }],
        // city: [{
        //   required: true,
        //   message: "请选择所在城市",
        //   trigger: "change"
        // }],
        // email: [{
        //   required: true,
        //   message: " ",
        //   trigger: "change"
        // }],
        // qq: [{
        //   required: true,
        //   message: " ",
        //   trigger: "blur"
        // }],
        link_address: [{
          required: true,
          message: " ",
          trigger: "blur"
        }]
      },
      financeForm: {
        opening_bank: "",
        customer_account: "",
        customer_tax_number: "",
        open_ticket_address: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: ""
      },
      financeRules: {
        opening_bank: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        customer_account: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        customer_tax_number: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        open_ticket_address: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        open_ticket_phone: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        receive_ticket_address: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        receive_ticket_person: [{
          required: true,
          message: " ",
          trigger: "blur"
        }],
        receive_ticket_phone: [{
          required: true,
          message: " ",
          trigger: "blur"
        }]
      },
      provinceList: [], //省
      cityList: [], //市
      loading: false,
      checked: true,
      update_time: new Date()
    };
  },
  mounted() {
    this.getProvinceList();
    this.ruleForm.fk_sale_employee_id = this.userInfo.employeeId;
    this.ruleForm.fk_sale_employee_number = this.userInfo.employeeNumber;
    this.ruleForm.fk_sale_employee_name = this.userInfo.fullName;
    this.ruleForm.fk_sale_employee_depot = this.userInfo.department_name;
  },
  methods: {
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.resetForm('financeForm');
      this.clearData();
      this.$emit("selectData");
    },
    save() {
      let params = this.ruleForm;
      let flag = true;
      if (this.checked) {
        this.$refs['financeForm'].validate(valid => {
          if (!valid) {
            flag = false;
          } else {
            params = Object.assign(params, this.financeForm);
          }
        });
      }


      // if(!params.email){
      //   return this.error('请填写邮箱')
      // }
      if (!flag) return;
      if (this.ruleForm.personnel_scale_low && this.ruleForm.personnel_scale_high){
        params.personnel_scale = this.ruleForm.personnel_scale_low + "-" + this.ruleForm.personnel_scale_high;
      }
      if (this.ruleForm.company_scale_low && this.ruleForm.company_scale_high){
        params.company_scale = this.ruleForm.company_scale_low + "-" + this.ruleForm.company_scale_high;
      }
      
      if(!params.customer_legal_name){
        params.customer_legal_name = params.customer_name
      }
      params.is_add_finance = this.checked ? 1 : 0;
      params.customer_name = params.customer_name.replace(/\s*/g, "");
      this.loading = true;
      API.add(params)
        .then(() => {
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    //选择介绍人
    chooseIntroducer() {
      this.$refs.customerList.Show();
    },
    //获取介绍人
    getInfo(info = {}) {
      this.ruleForm.introducer = info.customer_id;
      this.ruleForm.introducerName = info.customer_name;
    },
    //获取省列表
    getProvinceList() {
      comAPI
        .queryAreaCode({
          level: 1
        })
        .then(data => {
          this.provinceList = data.data;
        })
        .catch(() => {});
    },
    //更换省
    changeProvince() {
      this.cityList = [];
      this.ruleForm.city = "";
      this.getCityList();
    },
    //获取市列表
    getCityList() {
      if (!this.ruleForm.province) return;
      comAPI
        .queryAreaCode({
          level: 2,
          province: this.ruleForm.province
        })
        .then(data => {
          this.cityList = data.data;
        })
        .catch(() => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_id:"",
        customer_no:"",
        fk_sale_employee_id: "",
        link_man: "",
        fk_sale_employee_number: "",
        customer_name: "",
        phone: "",
        fk_sale_employee_name: "",
        customer_synopsis: "",
        telephone: "",
        fk_sale_employee_depot: "",
        customer_legal_name: "",
        email: "",
        company_site: "",
        customer_legal_person: "",
        qq: "",
        personnel_scale_low: "",
        personnel_scale_high: "",
        customer_type: 0,
        fax: "",
        company_scale_low: "",
        company_scale_high: "",
        belong_industry: 0,
        postal_code: "",
        province: "",
        customer_source: "",
        important_rank: 0,
        city: "",
        introducer: "",
        introducerName: "",
        link_address: "",
        customer_stage_remark:"",
        customer_stage: "0"
      }
      this.financeForm= {
        opening_bank: "",
        customer_account: "",
        customer_tax_number: "",
        open_ticket_address: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: ""
      }
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'params_constant_customer_stage',
      'params_constant_customer_type',
      'params_constant_belong_industry',
      'params_constant_customer_source',
      'params_constant_important_rank'
    ]),
    fkSaleEmployeeUserInfo() {
      const name =  this.ruleForm.fk_sale_employee_name + '-' +
      this.ruleForm.fk_sale_employee_number + '-' + 
       this.ruleForm.fk_sale_employee_depot 
       return name
      }
  },
};