.el-dialog__body {
  padding-top: 8px !important;
}

/*对话框样式修改*/
.el-dialog__header {
  background-color: #f8f8f9;
  padding: 10px;
}

.el-dialog__title {
  line-height: 20px;
  font-size: 16px;
}

.el-dialog__headerbtn {
  top: 10px;
}

/*弹窗主体样式  表格类padding：10px 15px; 非表格类需要外加新样式，左右内边距得到30px;*/
.el-dialog__body {
  padding: 10px 15px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

/*非表格类弹窗在弹窗的主体部分用div.noTable-dialogBody包裹，这样相加左右缩近就能到30px*/
.noTable-dialogBody {
  padding: 0px 15px;
}

.el-dialog__footer {
  background-color: #f8f8f9;
  padding: 5px 30px;
}