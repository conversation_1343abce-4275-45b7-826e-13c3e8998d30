<template>
  <div
    class="body-p10 orderH100"
    style="overflow-y: auto; overflow-x: hidden"
    v-if="dialogVisible"
  >
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="addNew">新增</el-button>
      <el-button
        :disabled="
          !!ruleForm.authorization_id || !!ruleForm.customer_contract_id
        "
        type="primary"
        @click="chooseContract"
        >调入合同
      </el-button>
      <el-button
        :disabled="
          !!ruleForm.authorization_id || !!ruleForm.customer_contract_id
        "
        type="primary"
        @click="chooseCustomerBrand"
        >调入客户产品
      </el-button>
      <el-button
        :disabled="!!ruleForm.authorization_id"
        type="primary"
        @click="submitForm('ruleForm')"
        :loading="loading"
        >保 存
      </el-button>
      <el-button
        :disabled="
          !(!!ruleForm.authorization_id || !!ruleForm.customer_contract_id)
        "
        type="success"
        @click="jumpContract"
        >跳转对应合同
      </el-button>
    </div>
    <el-form
      :model="ruleForm"
      ref="ruleForm"
      label-width="100px"
      class="flexAndFlexColumn mt10"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单据编号" prop="authorization_no">
            <el-input
              v-model="ruleForm.authorization_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据日期" prop="add_time">
            <el-input
              v-model="ruleForm.update_time"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="操作员" prop="add_user_name">
            <el-input
              v-model="ruleForm.add_user_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input disabled v-model="ruleForm.customer_name" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户编号" prop="customer_no">
            <el-input
              v-model="ruleForm.customer_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="单据备注" prop="remark">
            <el-input
              :disabled="!!ruleForm.authorization_id"
              v-model="ruleForm.remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <TableView
      v-if="authorizationFlag == 'customerContract'"
        :tableData="tableData"
        :isDblclick="true"
        ref="tableView"
        class="mt10 flexAndFlexColumn"
        :isOperation="!!ruleForm.authorization_no ? false : true"
      >
      </TableView>
      <TableView2
      v-if="authorizationFlag == 'customerBrand'"
        :tableData="tableData2"
        :isDblclick="true"
        ref="tableView2"
        class="mt10 flexAndFlexColumn"
        :isOperation="!!ruleForm.authorization_no ? false : true"
      >
      </TableView2>
    </el-form>

    <!-- <ContractDetailList
      ref="contractDetailList"
      dialogTitle="合同明细列表"
      @getInfo="getContract"
      :customer_no="this.ruleForm.customer_no"
      :contract_detail_ids="contract_detail_ids"
      @del="del"
    /> -->
    <CustomerBrandList
      ref="customerBrandList"
      dialogTitle="客户产品信息表"
      :contractDetailIds="contractDetailIds"
      :customerId="customerIds"
      @getInfo="getCustomerBrandInfo"
    />
    <ContractList
      ref="contractList"
      :is_has_authorization_sing="true"
      dialogTitle="可授权合同列表"
      @getInfo="getContractInfo"
      :customer_no="this.ruleForm.customer_no"
      :type="2"
      :data_state="1"
      :contract_ids="customer_contract_ids"
      isAllReceive
    />
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown + .el-dropdown {
  margin-left: 15px;
}

@import "@/assets/css/element/font-color.scss";
</style>