<template>
  <div
    class="content"
    :style="{
      backgroundColor: isHover ? (hoverColor ? hoverColor : iconColor) : '#fff',
    }"
    @mouseenter="isHover = true"
    @mouseleave="isHover = false"
    @click.stop="jumpRoute"
  >
    <div>
      <p class="title">{{ title }}</p>
      <div>
        <font class="number">{{ number }}</font>
        <font class="numberUtil">{{ numberUtil }}</font>
      </div>
    </div>
    <div class="iconContent">
      <icon class="icon" :style="{ color: iconColor }" :name="icon"></icon>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    number: {
      type: [String, Number],
      default: "0",
    },
    icon: {
      type: String,
      default: "report",
    },
    numberUtil: {
      type: String,
      default: "元",
    },
    iconColor: {
      type: String,
    },
    hoverColor: {},
    routerName: {
      type: String,
    },
    params: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      isHover: false,
    };
  },
  methods: {
    jumpRoute() {
      if (!this.routerName) return;
      this.$router.push({
        name: this.routerName,
        params: Object.assign(
          {
            type: "home",
          },
          this.params
        ),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  cursor: pointer;
  &:hover {
    color: #fff;

    .number,
    .numberUtil {
      color: #fff;
    }
  }
  .number {
    font-weight: bold;
    font-size: 18px;
    color: #000;
    margin-right: 5px;
  }
  .title {
    font-size: 16px;
  }
  .numberUtil {
    font-size: 14px;
    color: #777;
  }
  .iconContent {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60px;
    height: 60px;
    background-color: #f5f5f5;
    border-radius: 50%;
  }
  .icon {
    font-size: 28px;
    width: 35px;
    height: 35px;
  }
}
</style>
