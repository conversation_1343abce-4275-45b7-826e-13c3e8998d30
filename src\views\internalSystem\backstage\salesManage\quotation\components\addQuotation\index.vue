<template>
  <div class="body-p10 orderH100" style="overflow-y:auto;overflow-x: hidden;" v-if="dialogVisible">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button v-if="!ruleForm.quotation_id" type="primary"
        @click="submitForm('ruleForm')" :loading="loading">保 存</el-button>
      <el-button
      v-permit="'AUDIT_BACK_QUOTATION_NEW'"
        v-if="ruleForm.quotation_id&&ruleForm.audit_state != 3"
        type="primary" @click="back" :loading="loading">回退</el-button>
    </div>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="mt10 flexAndFlexColumn">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.customer_name" placeholder="请点击选择客户"
              @focus="chooseCustomer" readonly clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="操作员工" prop="add_user_name">
            <el-input v-model="ruleForm.add_user_name" disabled clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售员" prop="fk_sell_employee_name">
            <el-input v-model="ruleForm.fk_sell_employee_name" disabled clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销货单位" prop="sales_unit_id_format">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.sales_unit_id_format" @focus="chooseCompany"
              placeholder="请点击选择销货单位" readonly clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="操作员部门" prop="add_user_department_name">
            <el-input v-model="ruleForm.add_user_department_name" disabled clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售员部门" prop="fk_sell_department_name">
            <el-input v-model="ruleForm.fk_sell_department_name" disabled clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="培训方式" prop="train_type">
            <el-select :disabled="!isEdit||!isOwn" v-model="ruleForm.train_type" placeholder="请选择培训方式" filterable
              clearable>
              <el-option v-for="item in contract_train_type" :key="item.id" :label="item.label"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户传真" prop="fax">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.fax"  clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所在省份" prop="province">
            <el-select :disabled="!isEdit||!isOwn" v-model="ruleForm.province" placeholder="请选择省份"
              @change="changeProvince" filterable clearable>
              <el-option v-for="item in provinceList" :key="item.province" :label="item.origin_place"
                :value="item.province">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="付款方式" prop="pay_type">
            <el-select :disabled="!isEdit||!isOwn" v-model="ruleForm.pay_type" placeholder="请选择付款方式" filterable
              clearable>
              <el-option v-for="(item,index) in contract_pay_type" :key="index" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="手机" prop="phone">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.phone"  clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所在城市" prop="city">
            <el-select :disabled="!isEdit||!isOwn" v-model="ruleForm.city" placeholder="选择省份获取可选城市" filterable
              clearable>
              <el-option v-for="item in cityList" :key="item.city" :label="item.origin_place" :value="item.city">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售类型" prop="sell_type">
            <el-select :disabled="!isEdit||!isOwn" v-model="ruleForm.sell_type" placeholder="请选择销售类型" filterable
              clearable>
              <el-option v-for="(item,index) in sell_type" :key="index" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="介绍人" prop="introducer_name">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.introducer_name" placeholder="请点击选择介绍人"
              @focus="chooseIntroducer" readonly clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="详细地址" prop="customer_address">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.customer_address" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人" prop="link_man">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="介绍合同" prop="introducer_contract_format">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.introducer_contract_format" placeholder="请点击选择介绍合同"
              @focus="chooseContract" readonly clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="软件序列号" prop="software_no">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.software_no" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人QQ" prop="link_qq">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.link_qq" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据备注" prop="remark">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.remark" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div>     <el-button v-if="!ruleForm.quotation_id" type="primary" @click="add">新增
      </el-button></div>
 
      <el-tabs v-model="activeName" class="flexAndFlexColumn">
        <el-tab-pane label="合同产品" name="first" class="flexAndFlexColumn">

             <table-custom ref="proTableCustom" :obj="proObj" :tableCol="proTableCol"  :isDel="!ruleForm.quotation_id"/>
      
         
        </el-tab-pane>
        <el-tab-pane label="功能模块" name="second"  class="flexAndFlexColumn">
          <table-custom ref="moduleTableCustom" :obj="moduleObj"  :tableCol="moduleTableCol" :isDel="!ruleForm.quotation_id" />
        </el-tab-pane>
      </el-tabs>

    </el-form>
    <customer-list ref="dealCustomerList" dialogTitle="成交客户列表" :customerStage="3" @getInfo="getInfo" />
    <customer-list ref="customerList" dialogTitle="客户列表" :isJudge="true" @getInfo="getCustomerInfo" />
    <sales-unit-list ref="salesUnitList" dialogTitle="销货单位列表" @getInfo="getCampanyInfo" />
    <contract-list ref="contractList" dialogTitle="合同列表" :isJudge="true" @getInfo="getContractInfo" />
  </div>
</template>
<script src="./index.js">

</script>

<style lang="scss" scoped>

	@import "@/assets/css/element/font-color.scss";
</style>