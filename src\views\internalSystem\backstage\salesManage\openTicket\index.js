import API from "@/api/internalSystem/salesManage/openTicket";
import paramAPI from "@/api/internalSystem/basicManage/parameter";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import AddOpenTicket from "./components/addOpenTicket/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import FileList from "@/views/internalSystem/backstage/components/fileList/index.vue";
import InvoiceAttachmentBatch from "./components/invoiceAttachmentBatch/index.vue";
import AuditDetail from "@/mixins/auditDetail.js";
import salesUnitAPI from '@/api/internalSystem/basicManage/salesUnit'
import { getOptions } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";
export default {
  name: "openTicket",
  mixins: [AuditDetail],
  data() {
    return {
      title: "销售开票单",
      loading: false,
      tableData: [],
      formSearch: {
        customer_name: "",
        fk_sale_employee_id: "",
        audit_state: "",
        startTime: "",
        endTime: "",
        invoice_number: "",
        invoiceTaxRate:"",
        time_type: ''
      },
      tableList: [
        {
          name: "审核状态",
          value: "audit_state_format",
          width: 70,
        },
        {
          name: "编号",
          value: "ticket_no",
          width: 112,
        },
        {
          name: "销售合同号",
          value: "contract_no",
          width: 112,
        },
        {
          name: "开票税率",
          value: "invoice_tax_rate",
          width: 80,
        },
        {
          name: "客户名称",
          value: "customer_name",
          width: 200,
        },
        {
          name: "销售员",
          value: "fk_sale_employee_name",
          width: 80,
        },
        {
          name: "含税开票金额",
          value: "leved_total",
          width: 90,
        },
        {
          name: "客户税号",
          value: "customer_tax_number",
          width: 150,
        },
        {
          name: "客户账号",
          value: "customer_account",
          width: 150,
        },
        {
          name: "发票号码",
          value: "invoice_number",
          width: 80,
        },
        {
          name: "快递单号",
          value: "express_number",
          width: 90,
        },
        {
          name: "开票地址",
          value: "open_ticket_address",
          width: 100,
        },
        {
          name: "开户行",
          value: "open_account_bank",
          width: 100,
        },
        {
          name: "开票电话",
          value: "open_ticket_phone",
          width: 100,
        },
        {
          name: "收票地址",
          value: "receive_ticket_address",
          width: 100,
        },
        {
          name: "收票人",
          value: "receive_ticket_person",
          width: 62,
        },
        {
          name: "收票电话",
          value: "receive_ticket_phone",
          width: 100,
        },
        {
          name: "操作员",
          value: "update_user_name",
          width: 62,
        },
        {
          name: "单据日期",
          value: "ticket_date",
          width: 84,
        },
      ],
      auditStateList: [],
      employeeList: [],
      salesUnits: [],
      isAdd: false,
      allList: {
        open_ticket_money: 0,
        leved_total: 0,
        tax: 0,
      },
      totalOpenMoney:0,
      quotationRateList:[],
      time_type_list: [
        {
          label: '更新时间',
          value: 1
        },
        {
          label: '审核时间',
          value: 2
        }
      ]
    };
  },
  activated() {
    let params = this.$route.params;
    this.$nextTick(() => {
      if (params.type === "home" && this.$refs.addOpenTicket) {
        this.formSearch.audit_state = 1;
        Object.assign(this.formSearch, params);
        this.$refs.addOpenTicket.fromHomeCancel();
      }
    });
  },
  created() {
    if (!['总经理','财务经理','财务专员'].includes(this.cookiesUserInfo.role_name)) {
      this.formSearch.fk_sale_employee_id = this.cookiesUserInfo.userId;
    }
    this.getParam();
  },
  mounted() {
    if (!this.$route.params.type) this.getList();
    this.getSalesUnit();
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
  },
  methods: {
    getParam() {
      paramAPI
      .query()
      .then((data) => {
        data.data.forEach((item) => {
          if (item.parameter_type == 5)
            this.quotationRateList.push({
              label: item.content,
              value: item.content,
            });
        });
      })
      .catch(() => {});
    },
    getSalesUnit() {
      salesUnitAPI["query"]().then((data) => {
        this.salesUnits = data.data
      });
    },
    getList(f = false) {
      this.auditStateList = getOptions("t_open_ticket", "audit_state");
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          if (f) param.pageNum = 1;
          // let isJurisdiction = this.buttonPermissions.length ? (this.existence(this.buttonPermissions, 'ALL_OPENTICKET_NEW')) : false
          // param.isJurisdiction = isJurisdiction ? 1 : 0;
          // param.isJurisdiction = this.permissionToCheck("ALL_OPENTICKET_NEW")
          //   ? 1
          //   : 0;
          API.query(param)
            .then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
            })
            .finally(() => {
              this.loading = false;
            });
          API.queryAll(param).then((res) => {
            if (res.data) this.allList = res.data;
          });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addOpenTicket.Show();
    },
    modify(item) {
      let params = {
        ticket_id: item.ticket_id,
      };
      API.getInfo(params)
        .then((data) => {
          this.isAdd = true;
          this.$refs.addOpenTicket.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if (item.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }

      if (item.audit_state == 1 || item.audit_state == 0)
        return this.error("该单据已发出审核，不允许删除");
      let params = {
        ticket_id: item.ticket_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    // 打开附件列表
    openFileList(item) {
      this.$refs.fileList.Show("openTicket", item.ticket_id, item);
    },
    // 打开发票附件批量发送
    openInvoiceAttachmentBatch() {
      console.log("openInvoiceAttachmentBatch方法被调用");
      console.log("invoiceAttachmentBatch组件引用:", this.$refs.invoiceAttachmentBatch);
      if (this.$refs.invoiceAttachmentBatch) {
        this.$refs.invoiceAttachmentBatch.Show();
      } else {
        console.error("invoiceAttachmentBatch组件引用不存在");
      }
    },
    handleExcel(flag) {
      this.$confirm("是否导出数据到excel文件?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const expLoading = this.$loading({
            lock: true,
            text: "正在导出数据，请稍候...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          if(flag == 2 || flag == 4){
            param.pageNum = 1
            param.pageSize = 999999
          }
   
          const { data } = await API.query(param);
          this.excelData = data;
           this.export2Excel(expLoading,flag);
        })
        .catch(() => {});
    },
    export2Excel(expLoading,flag) {
      const that = this;
      const option = {};
      if(flag == 1 || flag == 2){
        option.tHeader = [
          "客户名称",
          "收票人",
          "收票电话",
          "收票地址",
          "业务员"
        ];
        option.filterVal = [
          "customer_name",
          "receive_ticket_person",
          "receive_ticket_phone",
          "receive_ticket_address",
          "update_user_name"
        ];
      }else{
        option.tHeader = this.tableList.map(item=>{
          return item.name
        });
        option.filterVal = this.tableList.map(item=>{
          return item.value
        });
      }

      option.name = "销售开票单导出";
      require.ensure([], () => {
        const { export_json_to_excel } = require("@/utils/Export2Excel"); // 这里必须使用绝对路径
        const tHeader = option.tHeader; // 导出的表头名
        const filterVal = option.filterVal; // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        expLoading.close();
        export_json_to_excel(tHeader, data, option.name); // 导出的表格名称，根据需要自己命名
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          return v[j];
        })
      );
    },
  },

  components: {
    AddOpenTicket,
    Pagination,
    TableView,
    MyDate,
    FileList,
    InvoiceAttachmentBatch,
  },
  computed: {
    ...mapGetters(["buttonPermissions", "audit_state","cookiesUserInfo"]),
  },
};
