import API from "@/api/internalSystem/basicManage/brand";
import { getOptions } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";
let moment = require("moment");
export default {
  name: "addBrand",
  props: {
    parentList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogTitle: "新增产品信息",
      dialogVisible: false,
      ruleForm: {
        brand_name: "",
        brand_no: "",
        parent_id: "",
        complete_time: new Date(),
        brand_cost_price: "",
        brand_sell_price: "",
        brand_classify: "",
        rate: "",
        disable_state: 1,
        detail_sell_type: "",
        product_is_storage: "",
        product_is_period: "",
        product_is_grant: "",
        product_is_default: "",
        product_is_port: "",
        brand_bill_code: "",
        product_attribution:""
      },
      rules: {
        brand_name: [
          {
            required: true,
            message: "请输入产品服务",
            trigger: "blur",
          },
        ],
        complete_time: [
          {
            required: true,
            message: "请输入开发完成时间",
            trigger: "blur",
          },
        ],
        brand_no: [
          {
            required: true,
            message: "请输入软件证书编号",
            trigger: "blur",
          },
        ],
        brand_cost_price: [
          {
            required: true,
            message: "请输入成本价",
            trigger: "blur",
          },
        ],
        rate: [
          {
            required: true,
            message: "请输入税率",
            trigger: "blur",
          },
        ],
        disable_state: [
          {
            required: true,
            message: "请选择是否启用",
            trigger: "blur",
          },
        ],
        detail_sell_type: [
          {
            required: true,
            message: "请选择销售类型",
            trigger: "blur",
          },
        ],
        brand_sell_price: [
          {
            required: true,
            message: "请输入建议售价",
            trigger: "blur",
          },
        ],
        brand_classify: [
          {
            required: true,
            message: "请选择品牌分类",
            trigger: "blur",
          },
        ],
        product_is_grant: [
          {
            required: true,
            message: "请选择是否有授权码",
            trigger: "blur",
          },
        ],
        product_is_period: [
          {
            required: true,
            message: "请选择有无维护期",
            trigger: "blur",
          },
        ],
        product_is_storage: [
          {
            required: true,
            message: "请选择是否加入客户产品表",
            trigger: "blur",
          },
        ],
        product_is_default: [
          {
            required: true,
            message: "请选择是否默认产品",
            trigger: "blur",
          },
        ],
        product_is_port: [
          {
            required: true,
            message: "请选择是否需要端口",
            trigger: "blur",
          },
        ],
        product_attribution:[
          {
            required: true,
            message: "请选择产品归属",
            trigger: "blur",
          },
        ]
      },
      brandClassifyList: [],
      isEdit: false,
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.brandClassifyList = getOptions(
        "t_brand_management",
        "brand_classify"
      );
      this.isEdit = false;
      if (!data) {
        this.dialogTitle = "新增产品信息";
      } else {
        this.dialogTitle = "修改产品信息";
        this.ruleForm = data;
        this.ruleForm.brand_name = data.brand_type;

        this.isEdit = true;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm("ruleForm");
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      if (
        Number(params.product_is_period) === 1 ||
        Number(params.product_is_grant) === 1
      ) {
        if (Number(params.product_is_storage) === 2) {
          return this.error("有授权码或维护期，必须选择加入客户");
        }
      }
      if (Number(params.product_is_grant) === 1) {
        
        if (!params.brand_bill_code) {
          return this.error("有授权码一定要填写软件编码");
        }
      }

      // params.product_is_port = params.product_is_period

      if (!params.parent_id || params.parent_id === 0) {
        params.brand_classify = 1;
      } else {
        params.brand_classify = 2;
      }
      params.brand_type = params.brand_name;
      params.complete_time = moment(params.complete_time).format("YYYY-MM-DD");
      // params.brand_classify = params.parent_id ? 1 : 2
      let APIName = params.brand_id ? "update" : "add";
      API[APIName](params)
        .then(() => {
          this.dialogCancel();
        })
        .catch(() => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        brand_type: "",
        brand_name: "",
        brand_no: "",
        complete_time: ``,
        parent_id: ``,
        brand_cost_price: "",
        brand_sell_price: "",
        brand_classify: "",
        product_is_period: "",
        product_is_grant: "",
        product_is_storage: "",
        product_is_default: "",
        product_is_port: "",
        brand_bill_code: "",
        product_attribution:""
      };
    },
    // 品牌下拉改变
    brandChange(val) {
      if (val === 1) {
        // 产品时要清除上级产品的选择
        this.ruleForm.parent_id = "";
      }
    },
  },
  computed: {
    ...mapGetters([
      "brand_classify",
      "sale_pattern",
      "product_sale_model",
      "product_is_period",
      "product_is_grant",
      "sell_type",
    ]),
  },
};
