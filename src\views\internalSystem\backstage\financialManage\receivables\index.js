import API from "@/api/internalSystem/financialManage/receivables";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import AddReceivables from "./components/addReceivables/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import DictAPI from "@/api/internalSystem/dict";
import salesUnitAPI from '@/api/internalSystem/basicManage/salesUnit'
import { getOptions } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";
export default {
  name: "receivables",
  data() {
    return {
      title: "销售收款单",
      loading: false,
      tableData: [],
      formSearch: {
        contract_no: "",
        customer_name: "",
        fk_sell_employee_id: "",
        project: "",
        startTime: "",
        endTime: "",
        confirmStartTime: "",
        confirmEndTime: "",
      },
      tableList: [
        {
          name: "审核状态",
          value: "check_state_name",
          width: 70,
        },
        {
          name: "单据编号",
          value: "bill_code",
          width: 112,
        },
        {
          name: "客户编号",
          value: "customer_no",
          width: 96,
        },
        {
          name: "客户名称",
          value: "customer_name",
          width: 200,
        },
        {
          name: "收款项目",
          value: "project_name",
          width: 82,
        },
        {
          name: "本次收款金额",
          value: "amount",
          width: 96,
        },
        {
          name: "到款日期",
          value: "confirm_money_time",
          width: 90,
        },
        {
          name: "单据备注",
          value: "receivables_remarks",
          width: 100,
        },
        {
          name: "收款合同编号",
          value: "contract_no",
          width: 112,
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 60,
        },
        {
          name: "销售部门",
          value: "fk_sell_department_name",
          width: 70,
        },
        {
          name: "到期日期",
          value: "maintain_stop_time",
          width: 90,
        },
        {
          name: "收款银行名称",
          value: "bank_name",
          width: 110,
        },
        {
          name: "结算方式",
          value: "settlementMethod_name",
          width: 72,
        },
        {
          name: "到款备注",
          value: "confirm_money_remarks",
          width: 100,
        },
        {
          name: "技术员",
          value: "bear_employee_name",
          width: 80,
        },
        {
          name: "制单人",
          value: "add_user_name",
          width: 60,
        },
        {
          name: "制单时间",
          value: "update_time",
          width: 90,
        },
      ],
      employeeList: [],
      projectList: [],
      salesUnits: [],
      isAdd: false,
      amount: 0,
      exLoading: false,
    };
  },
  activated() {
    let params = this.$route.params;

    if (params.type === "home") {
      Object.assign(this.formSearch, params);
      this.$refs.addReceivables.dialogCancel();
    }
  },
  mounted() {
    // this.$store.dispatch('getEmployee').then(res => {
    //   this.employeeList = res;
    // });

    this.getDict();
    this.getSalesUnit();
    if (!this.$route.params.type) this.getList();
  },
  methods: {
    getDict() {
      DictAPI.getDict({
        type: "user_in_position",
      }).then((res) => {
        this.employeeList = res.data;
      });
    },
    getSalesUnit() {
      salesUnitAPI["query"]().then((data) => {
        this.salesUnits = data.data
      });
    },
    getList(f = false) {
      this.projectList = getOptions("t_receivables", "project");
      this.isAdd = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          if (f) param.pageNum = 1;
          if (
            !["总经理", "财务经理", "财务专员"].includes(
              this.userInfo.role_name
            )
          ) {
            param.fk_sell_employee_id = this.userInfo.employeeId;
          }
          API.query(param)
            .then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
            })
            .finally(() => {
              this.loading = false;
            });
          API.queryAll(param).then((res) => {
            if (res.data) this.amount = res.data.amount;
          });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addReceivables.Show();
    },
    modify(item) {
      let params = {
        receivables_id: item.receivables_id,
      };
      API.getInfo(params)
        .then((data) => {
          this.isAdd = true;
          this.$refs.addReceivables.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if (item.check_state == 1)
        return this.error("该单据已审核通过，不允许删除");
      let params = {
        receivables_id: item.receivables_id,
        fk_financial_bank_accout_id: item.fk_financial_bank_accout_id,
        fk_customer_id: item.fk_customer_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    //导出销售收款单
    exportReceivables() {
      this.exLoading = true;
      let param = this.formSearch;
      let data = {
        url: "excel/receivablesExport",
        data: param,
      };
      this.ws.send(JSON.stringify(data));
      this.ws.onmessage = (e) => {
        let res = JSON.parse(e.data);
        if (res.code === 1) {
          this.success(res.message);
          this.Download(res.data);
        } else this.error(res.message);
        this.exLoading = false;
      };
    },
    handleExcel() {
      this.$confirm("是否导出数据到excel文件?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const expLoading = this.$loading({
            lock: true,
            text: "正在导出数据，请稍候...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          param.pageNum = 1
          param.pageSize = 999999
          const { data } = await API.query(param);
          this.excelData = data;
           this.export2Excel(expLoading);
        })
        .catch(() => {});
    },
    export2Excel(expLoading) {
      const that = this;
      const option = {};
      option.tHeader = [
        "审核状态",
        "单据编号",
        "客户编号",
        "客户名称",
        "收款项目",
        "本次收款金额",
        "到款日期",
        "单据备注",
        "收款合同编号",
        "销售员",
        "销售部门",
        "到期日期",
        "收款银行名称",
        "结算方式",
        "到款备注",
        "技术员",
        "制单人",
        "制单时间",
      ];
      option.filterVal = [
        "check_state_name",
        "bill_code",
        "customer_no",
        "customer_name",
        "project_name",
        "amount",
        "confirm_money_time",
        "receivables_remarks",
        "contract_no",
        "fk_sell_employee_name",
        "fk_sell_department_name",
        "maintain_stop_time",
        "bank_name",
        "settlementMethod_name",
        "confirm_money_remarks",
        "bear_employee_name",
        "add_user_name",
        "update_time",
       
      ];
      option.name = "销售收款导出";
      require.ensure([], () => {
        const { export_json_to_excel } = require("@/utils/Export2Excel"); // 这里必须使用绝对路径
        const tHeader = option.tHeader; // 导出的表头名
        const filterVal = option.filterVal; // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        expLoading.close();
        export_json_to_excel(tHeader, data, option.name); // 导出的表格名称，根据需要自己命名
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          return v[j];
        })
      );
    },

  },

  components: {
    AddReceivables,
    MyDate,
    Pagination,
    TableView,
  },
  computed: {
    ...mapGetters([
      "ws",
      "buttonPermissions",
      "receivables_project",
      "userInfo",
    ]),
  },
};
