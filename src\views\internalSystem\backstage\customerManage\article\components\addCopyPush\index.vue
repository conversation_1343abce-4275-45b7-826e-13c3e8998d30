<template>
  <div class="orderH100" v-show="dialogVisible">
    <div>
      <el-button @click="dialogCancel(true)">返 回</el-button>
      <el-button type="primary" @click="dialogCancel(false)">新增</el-button>
      <el-button
        type="primary"
        @click="submitForm('ruleForm')"
        :loading="loading"
        :disabled="!!ruleForm.copy_push_log_id"
        >推送
      </el-button>
      <el-button
        :disabled="!!ruleForm.copy_push_log_id"
        type="primary"
        @click="chooseCustomer"
        >调入客户</el-button
      >
      <el-button
        v-if="copyListFlag"
        :disabled="!!ruleForm.copy_push_log_id"
        type="primary"
        @click="chooseCopy"
        >调入文案</el-button
      >
    </div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="mt10"
    >
      <el-row :gutter="20">
        <el-col :span="8" class="ellipsis">
          <el-form-item label="单据编号">
            <el-input
              v-model="ruleForm.copy_push_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="ellipsis">
          <el-form-item label="单据日期">
            <el-input
              v-model="ruleForm.create_time"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="ellipsis">
          <el-form-item label="制单人">
            <el-input
              v-model="ruleForm.employee_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="ellipsis formItem2">
          <el-form-item label="文案主题" prop="title">
            <el-input
              v-model="ruleForm.title"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
                <el-col :span="8" class="ellipsis formItem2">
          <el-form-item label="文案二级关键词" prop="keywords">
            <el-input
              v-model="ruleForm.keywords"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="ellipsis formItem2">
          <el-form-item label="计划推送时间" >
            <my-date2
              :disabled="!!ruleForm.copy_push_log_id"
              v-model="ruleForm.plan_send_time"
              hint="请选择计划推送时间"
            ></my-date2>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="ellipsis formItem2">
          <el-form-item label="推送记录备注">
            <el-input
              :disabled="!!ruleForm.copy_push_log_id"
              v-model="ruleForm.remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <table-view
      ref="SendCustomer"
      :tableList="tableList"
      :tableData="tableData"
      :isDblclick="true"
      class="mt10 tableContent"
      :isDel="isDel ? 'DEL_COPY_PUSH_USER_NEW' : null"
      @del="del"
    >
    </table-view>
    <CustomerList
      ref="customerAllList"
      dialogTitle="所有客户列表"
      :isJudge="true"
      @getInfo="getCustomerInfo"
      :isContract="true"
      :selectMultiple="true"
      :wechatBindingState="1"
    />
    <CopyList
      ref="copyList"
      dialogTitle="文案列表"
      @getInfo="getCopyInfo"
      :copyState="1"
    />
  </div>
</template>
<script src="./index.js"></script>
<style lang="scss" scoped>
.w100 {
  width: 100% !important;
}

@import "@/assets/css/element/font-color.scss";
</style>
