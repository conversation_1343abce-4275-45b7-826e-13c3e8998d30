export default [{
  path: 'statistics',
  name: 'statistics',
  title: '首页',
  icon: 'el-icon-menu',
  meta: {
    title: "首页",
    keepAlive: false,
    closable: true
  },
  component: resolve =>
    require([
      "@/views/internalSystem/backstage/components/view/view.vue"
    ], resolve),
  children: [{
      path: 'statisticsPage',
      name: 'statisticsPage',
      title: '首页',
      meta: {
        title: "首页"
      },
      component: () => import('@/views/internalSystem/backstage/statisticsPage/index.vue')
    }]
}]