// 云平台风格左侧菜单样式
.sidebar {
  background-color: #2f2f2f;

  .logo {
    height: 54px;
    flex: 0 0 54px;
    width: 100%;
    color: #fff;
    font-size: 20px;
    font-weight: 600;
    align-items: center;
    background-color: #2b2b2b;
    box-sizing: border-box;
    padding: 0 25px;

    img {
      width: 100%;
    }
  }

  .navList {
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 0;
    }

    .navList-li {
      width: 100%;
      height: auto;
      background-color: #373737;
      user-select: none;

      .title {
        height: 70px;
        overflow: hidden;
        line-height: 70px;
        color: #b3c0d1;
        transition: all 0.3s;

        .icon-arrow {
          width: 18px;
          height: 100%;
          flex: 0 0 18px;
          color: #b3c0d1;
          transition: all 0.3s;
        }

        .icon {
          width: 25px;
          height: 100%;
          flex: 0 0 25px;
          color: #b3c0d1;
          transition: all 0.3s;
        }

        .icon-rotate {
          transform: rotate(90deg);
        }
      }

      &:hover {
        .title {
          color: #fff;
          background-color: $--sideBar-active-title-background-color;
        }

        .icon-arrow,
        .icon {
          color: #fff;
        }

        .submenu {
          display: block;
        }
      }

      .active-title {
        color: #fff;
        background-color: $--sideBar-active-title-background-color;

        .icon-arrow,
        .icon {
          color: #fff;
        }
      }
    }
  }
}
