<template>
  <div class="top-title">
    <div class="logo-container">
      <img :src="img" alt />
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      img: require("@/assets/images/stock/logo.png")
    }
  }
};
</script>
<style scoped lang="scss">
.top-title {
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  background-color: #30403d;
  .logo-container {
    margin-left: 10px;
    width: auto;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
}
</style>
