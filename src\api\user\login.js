import Axios from "../index";
import environment from "@/api/environment";

export default {
  // 登录
  login: params => Axios.post(`${environment.basicAPI}user/login`, params),
  // token免登
  freeLogin: params =>
    // Axios.post(`${environment.tradeAPI}user/checkNoLogin`, params),
    Axios.post(`${environment.basicAPI}user/tokenUserInfo`, params),

  // 通用云平台获取用户数据的接口
  getUserInfoByToken: () => {
    return Axios.post(`${environment.basicAPI}user/cloudRefreshUserInfo`);
  },

  //重置密码
  resetPass: (params) => {
    return Axios.post(`${environment.basicAPI}/user/resetPass`,params);
  },

  

  // erp 平台页面刷新token重新获取用户信息
  refreshLogin: params =>
    Axios.post(`${environment.tradeAPI}user/userTokenData`, params),
  //内部系统登录接口
  internalLogin: params =>
    Axios.post(`${environment.internalSystemAPI}user/login`, params),
  internalGetInfo: params =>
    Axios.post(`${environment.internalSystemAPI}user/getInfo`, params)
};
