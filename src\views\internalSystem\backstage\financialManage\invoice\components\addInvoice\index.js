import API from '@/api/internalSystem/financialManage/invoice'
import compAPI from '@/api/internalSystem/basicManage/salesUnit'
import TableCustom from "@/views/internalSystem/backstage/components/tableCustom/index.vue";
import {
  mapGetters
} from "vuex";
export default {
  name: "addInvoice",
  components: {
    TableCustom
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        add_user_name: "",
        department_name: "",
        invoice_unit: "",
        invoice_remark: ""
      },
      loading: false,
      rules: {
        invoice_unit: [{
          required: true,
          message: "请选择购销单位",
          trigger: "change"
        }]
      },
      tableCol: [{
        label: "费用项目",
        prop: "cost_project_no",
        width: 160,
        
      }, {
        label: "项目名称",
        prop: "cost_project_name",
        width: 160
      }, {
        label: "费用明细",
        prop: "cost_detail_no",
        isEditConfig:true,
        width: 160
      }, {
        label: "明细名称",
        prop: "cost_detail_name",
        width: 160,
        isEditConfig:true,
        need: true
      }, {
        label: "报销银行",
        prop: "bank_name",
        width: 160,
        isEditConfig:true,
        need: true
      }, {
        label: "fk_bank_id",
        prop: "fk_bank_id",
        
        isHide: true
      }, {
        label: "承担员工",
        prop: "employee_name",
        width: 160,
        isEditConfig:true,
        need: true
      }, {
        label: "部门名称",
        prop: "department_name",
        width: 160
      }, {
        label: "承担金额",
        prop: "bear_money",
        width: 160,
        isEditConfig:true,
        need: true
      }, {
        label: "承担日期",
        prop: "bear_date",
        width: 160,
        isEditConfig:true,
        need: true
      }, {
        label: "凭证号码",
        prop: "voucher_number",
        width: 160,
        isEditConfig:true,
        need: true
      }, {
        label: "摘要",
        prop: "summary",
        width: 160,
        isEditConfig:true,
        need: true
      }, {
        label: "备注说明",
        prop: "remark",
        isEditConfig:true,
        width: 160
      }],
      obj: {},
      companyList: []
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.ruleForm.department_name = this.userInfo.department_name;
      this.ruleForm.add_user_name = this.userInfo.fullName;
      compAPI.query({}).then(res => {
          this.companyList = res.data;
        })
        .catch(() => {}).finally(() => {});
      this.obj = {
        cost_project_no: {
          value: "",
          type: "input",
          disabled: true
        },
        cost_project_name: {
          value: "",
          type: "input",
          disabled: true
        },
        cost_detail_no: {
          value: "",
          type: "input",
          disabled: true
        },
        cost_detail_name: {
          value: "",
          type: "dialog",
          dialogType: "cost"
        },
        bank_name: {
          value: "",
          type: "dialog",
          dialogType: "bank"
        },
        fk_bank_id: {
          value: "",
          type: "input"
        },
        employee_name: {
          value: "",
          type: "dialog",
          dialogType: "employee"
        },
        department_name: {
          value: 1,
          type: "input",
          disabled: true
        },
        bear_money: {
          value: "",
          type: "float"
        },
        bear_date: {
          value: "",
          type: "date"
        },
        voucher_number: {
          value: "",
          type: "input"
        },
        summary: {
          value: "",
          type: "input"
        },
        remark: {
          value: "",
          type: "input"
        }
      }
      if (data) {
        this.ruleForm = data;
        this.ruleForm.invoice_unit = parseInt(data.invoice_unit);
        API.detailList({
            financial_cost_invoice_id: data.financial_cost_invoice_id
          })
          .then(res => {
            res.data.map(item => {
              let pro = JSON.parse(JSON.stringify(this.obj))
              for (let v in item) {
                if (pro[v]) {
                  pro[v].value = item[v];
                  pro[v].disabled = true;
                }
              }
              this.$refs.tableCustom.add2(pro);
            })
          })
          .catch(() => {}).finally(() => {});
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    async save() {
      let params = this.ruleForm;
      let proList = [];
      try {
        proList = await this.$refs.tableCustom.getData();
      } catch {
        return;
      }
      let detail = []
      if (proList.length == 0)
        return this.error("至少有一条数据");
      proList.forEach(item => {
        detail.push({
          cost_project_no: item.cost_project_no.value,
          cost_project_name: item.cost_project_name.value,
          cost_detail_no: item.cost_detail_no.value,
          cost_detail_name: item.cost_detail_name.value,
          bank_name: item.bank_name.value,
          fk_bank_id: item.fk_bank_id.value,
          employee_name: item.employee_name.value,
          department_name: item.department_name.value,
          bear_money: item.bear_money.value,
          bear_date: item.bear_date.value,
          voucher_number: item.voucher_number.value,
          summary: item.summary.value,
          remark: item.remark.value
        })
      });
      params.detail = detail;
      this.loading = true;
      if (params.financial_cost_invoice_id) {
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      }

    },
    back() {
      if(this.ruleForm.data_state ===2){
        return this.error('该记录是旧数据，请上旧系统继续操作')
      }
      this.$confirm('此操作将回退该条记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          financial_cost_invoice_id: this.ruleForm.financial_cost_invoice_id,
          auditState: 3
        }
        API.updateAudit(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {});
      }).catch(() => {})
    },
    //添加明细
    add() {
      this.$refs.tableCustom.add();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        add_user_name: "",
        department_name: "",
        invoice_unit: "",
        invoice_remark: ""
      }
    }
  },
  computed: {
    ...mapGetters(["buttonPermissions", "userInfo"])
  }
};