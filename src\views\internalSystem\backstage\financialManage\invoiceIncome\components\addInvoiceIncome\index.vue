<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="660px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
       <el-form-item label="单据编号" v-if="ruleForm.invoice_income_id">
        <el-input v-model="ruleForm.invoice_income_no" disabled clearable></el-input>
      </el-form-item>
      <el-form-item label="开票单位" prop="invoice_unit">
        <el-input v-model="ruleForm.invoice_unit" clearable></el-input>
      </el-form-item>
      <el-form-item label="发票明细" prop="invoice_detail">
        <el-input v-model="ruleForm.invoice_detail" clearable></el-input>
      </el-form-item>
      <el-form-item label="发票号码" prop="invoice_number">
        <el-input v-model="ruleForm.invoice_number" clearable></el-input>
      </el-form-item>
      <el-form-item label="收票金额" prop="invoice_money">
        <el-input v-model="ruleForm.invoice_money"  clearable></el-input>
      </el-form-item>
      <el-form-item label="发票时间" prop="invoice_time">
        <my-date v-model="ruleForm.invoice_time" hint="请选择发票时间" :isAllDate="false"></my-date>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="back"
        v-permit="'AUDIT_BACK_INVOICEINCOME_NEW'"
        v-if="ruleForm.invoice_income_id&&ruleForm.audit_state!=3">
        回退审核</el-button>
      <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>