
// import API from '@/api/internalSystem/basicManage/salesUnit'
import QRCode from "qrcodejs2";
export default {
	data() {
		return {
			qrCode: require("@/assets/images/stock/qr-code.png")
		}
	},
	methods: {
		confirmBind(){
			// 二维码URL
			// getCodeUrl(this.seller_id).then( res => {
				this.qrUrl ='';
				this.QRVisible = true;
				this.$nextTick(()=>{
					this.crateQrcode()
				})
				// 请求绑定返回（1分钟内）
				let time = 60;
				this.requestTimer = setInterval(() => {
					if(time>0){
						time--;
						new Promise((resolve,reject) => {
							// 授权结果返回接口
							this.seller_id = getSellerId();
							getBindResult(this.seller_id)
							.then((res) => {
								resolve(res)
							})
							.catch((res) => {
								reject(res)
							})
						}).then( res => {
							if(res.data == true){
								this.$notify({
									title: '成功',
									message: '公众号绑定成功！',
									type: 'success',
									duration: 4000
								});
								this.QRVisible = false;
								this.authDialog = false;
								this.getHomeToday();
							}
						}).catch( res => {
							this.$notify({
								title: '失败',
								message: '公众号绑定失败，请重新绑定！',
								type: 'error',
								duration: 4000
							});
							this.QRVisible = false;
							this.authDialog = true;
						})
					}else{
						clearInterval(this.requestTimer);
						this.$notify({
							title: '失败',
							message: '公众号绑定失败，请重新绑定！',
							type: 'error',
							duration: 4000
						});
						this.QRVisible = false;
						this.authDialog = true;
					}
				},6000)
			// }).catch(() => {
			// 	clearInterval(this.requestTimer);
			// })
		},
		crateQrcode(){
			let qrcode = new QRCode(this.$refs.qrBox, {
				text: this.qrUrl,  
				width: 120,  
				height: 120,
				colorDark: '#000',  
				colorLight: '#fff',
				correctLevel: QRCode.CorrectLevel.L  //容错率，L/M/H
			})
		}

	},
	components: {
	
	},
	mounted() {

	}
}
