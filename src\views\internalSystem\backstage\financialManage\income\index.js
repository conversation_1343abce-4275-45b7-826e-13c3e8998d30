import API from '@/api/internalSystem/financialManage/income'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddIncome from "./components/addIncome/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import AuditDetail from '@/mixins/auditDetail.js'
import {
  mapGetters
} from "vuex";
export default {
  name: "income",
  mixins: [AuditDetail],
  data() {
    return {
      title: "其他收入单",
      loading: false,
      tableData: [],
      formSearch: {
        startTime: "",
        endTime: ""
      },
      tableList: [{
          name: "审核状态",
          value: "audit_state_name"
        },
        {
          name: "编号",
          value: "income_code"
        },
        {
          name: "收入类型",
          value: "income_type_name"
        },
        {
          name: "收入明细",
          value: "content"
        },
        {
          name: "收入金额(元)",
          value: "amount"
        },
        {
          name: "收入银行",
          value: "fk_bank_account_name"
        },
        {
          name: "收入日期",
          value: "income_time"
        },
        {
          name: "制单人",
          value: "add_user_name"
        },
        {
          name: "审核人",
          value: "audit_name"
        },
        {
          name: "审核时间",
          value: "audit_time"
        },
        {
          name: "审核内容",
          value: "audit_remark"
        }
      ]
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    getList(f = false) {
      this.isAudit = false;
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
      if (f)
        param.pageNum = 1;
      // let isJurisdiction = this.buttonPermissions.length ? (this.existence(this.buttonPermissions, 'ALL_INCOME_NEW')) : false
      // param.isJurisdiction = isJurisdiction ? 1 : 0;
      param.isJurisdiction = this.permissionToCheck('ALL_INCOME_NEW') ? 1 : 0;
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    add() {
      this.$refs.addIncome.Show();
    },
    //打开修改其他收入单会话框
    modify(item) {
      let params = {
        income_id: item.income_id
      };
      API.getInfo(params)
        .then(data => {
          this.$refs.addIncome.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if (item.audit_state == 1)
        return this.error("该记录已审核通过，不允许删除")
      let params = {
        income_id: item.income_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },

  components: {
    AddIncome,
    Pagination,
    TableView,
    MyDate
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};