<template>
  <el-dialog
    title="审核"
    :visible.sync="dialogVisibleAudit"
    append-to-body
    @close="dialogCancelAudit"
    width="660px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    v-dialogDrag
  >
    <el-form :model="auditForm" :rules="rules" ref="auditForm" label-width="100px">
      <el-form-item label="审核状态" prop="auditState">
        <el-select v-model="auditForm.auditState" placeholder="请选择审核状态" filterable clearable>
          <template v-for="item in auditStateList">
            <el-option
              v-if="item.sysValue!=ruleForm.auditState"
              :key="item.sysValue"
              :label="item.sysName"
              :value="item.sysValue"
            ></el-option>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="auditRemark">
        <el-input v-model="auditForm.auditRemark" type="textarea" clearable></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm('auditForm')" :loading="loading">保 存</el-button>
      <el-button @click="dialogCancelAudit">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      dialogVisibleAudit: false,
      auditForm: {
        auditState: "1",
        auditRemark: ""
      },
      rules: {
        auditState: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }]
      },
      auditStateList: [],
      loading: false
    }
  },
  methods: {
    show(data) {
      this.dialogVisibleAudit = true;
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    save() {
      let params = this.auditForm;
      params.implementation_id = this.ruleForm.implementation_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
      Object.assign(this.$data, this.$options.data())
    },
  }
}
</script>