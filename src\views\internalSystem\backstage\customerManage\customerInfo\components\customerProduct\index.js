import API from '@/api/internalSystem/customerManage/customerInfo'
import BasicData from "../basicData2/index.vue";
import ProductInformation from "../productInformation3/index.vue";
// import ProductInformation from "../productInformation/index.vue";
import {
  mapGetters
} from "vuex";
export default {
  name: "customerDetail",
  data() {
    return {
      dialogVisible: false,
      activeName: "first",
      updateForm: {
        fk_sale_employee_id: ""
      },
      updateFormRules: {
        fk_sale_employee_id: [{
          required: true,
          message: "请选择销售员",
          trigger: "change"
        }],
      },
      changeForm: {
        customer_stage: ""
      },
      changeFormRules: {
        customer_stage: [{
          required: true,
          message: "请选择客户阶段",
          trigger: "change"
        }],
      },
      customer_id: "",
      port_number:0,
      salesList: [], //销售员列表
      dialogUpdate: false,
      dialogChange: false
    }
  },
  methods: {
    Show(data = {}) {
      this.dialogVisible = true;
      this.activeName = "first";
      this.customer_id = data.customer_id;
      this.port_number = data.port_number
      this.$store.dispatch('getEmployee').then(res => {
        this.salesList = res;
      });
    },
    dialogCancel() {
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    del() {
      this.$confirm('此操作将删除该客户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          customer_id: this.customer_id
        };
        API.remove(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      }).catch(() => {})
    },
    updateSales() {
      this.dialogUpdate = true;
    },
    dialogUpdateClose() {
      this.dialogUpdate = false;
      this.resetForm("updateForm");
    },
    dialogUpdatePost() {
      this.$refs['updateForm'].validate(valid => {
        if (!valid) {
          return false;
        } else {
          let params = {
            customer_id: this.customer_id,
            fk_sale_employee_id: this.updateForm.fk_sale_employee_id
          };
          API.updateSalesman(params)
            .then(() => {
              this.dialogUpdate = false;
              this.$refs.basicData.getInfo();
              this.$refs.salesTransferList.getList();
            })
            .catch(() => {});
        }
      });

    },
    changeStage() {
      this.dialogChange = true;
    },
    dialogChangeClose() {
      this.dialogChange = false;
      this.resetForm("changeForm");
    },
    dialogChangePost() {
      this.$refs['changeForm'].validate(valid => {
        if (!valid) {
          return false;
        } else {
          let params = {
            customer_id: this.customer_id,
            customer_stage: this.changeForm.customer_stage
          };
          API.updateCustomerStage(params)
            .then(() => {
              this.dialogChange = false;
              this.$refs.basicData.getInfo();
            })
            .catch(() => {});
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
  components: {
    BasicData,

    ProductInformation

  },
  computed: {
    ...mapGetters(["params_constant_customer_stage"])
  }
}