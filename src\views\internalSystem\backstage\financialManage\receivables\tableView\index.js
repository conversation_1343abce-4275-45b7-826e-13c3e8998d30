import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  mapGetters
} from "vuex";
export default {
  name: "tableView",
  components: {
    MyDate

  },
  props: {
    tableData: {
      default () {
        return {}
      },
      type: Array
    },
    tableList: {
      default () {
        return {}
      },
      type: Array
    },
    isEdit: {
      default: '',
      type: String
    },
    isDel: {
      default: '',
      type: Boolean
    },
    isSel: {
      default: false,
      type: Boolean
    },
    tableHeight: {
      type: Number
    },
    isThrid: {
      default: '',
      type: String
    },
    thridTitle: {
      type: String
    },
    isFour: {
      default: '',
      type: String
    },
    fourTitle: {
      type: String
    },
    handleWidth: {
      type: Number,
      default: 120
    },
    isOperation: { //是否显示操作
      type: Boolean,
      default: true
    },
    //是否双击
    isDblclick: {
      type: Boolean,
      default: false
    },
    isOrder: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      sell_type: [{
        label: '首次销售',
        id: 1
      }, {
        label: '二次开发',
        id: 2
      }, {
        label: '软件服务费',
        id: 3
      }, {
        label: '增加端口',
        id: 4
      }, {
        label: '软件租用',
        id: 5
      }, {
        label: '软件升级',
        id: 6
      }, {
        label: '二次销售',
        id: 7
      }, {
        label: '赠送端口',
        id: 8
      }, {
        label: '金万维',
        id: 9
      }, {
        label: '项目款',
        id: 10
      }, {
        label: '网站',
        id: 11
      }, {
        label: '硬件销售',
        id: 12
      }, ]
    }
  },
  methods: {
    del(item) {
      if(item.data_state ===2){
        return this.error('该记录是旧数据，请上旧系统继续操作')
      }
      this.$confirm('此操作将删除该条记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit("del", item);
      }).catch(() => {})
    },
    what(e) {
      this.$forceUpdate()
    },
    changeAmount(row) {
     
      if (Number(row.amount) === Number(row['not_receivables_amount'])) {
        row.children.map((item, index) => {
          item['new_money'] = this.numSub(item['contract_amount'], item['receivables_money'])
        })
      } else {
        let otherAmout = 0
        row.children.map((item, index) => {
          if (index !== row.children.length - 1) {
            const tmpReceivablesAmout = this.numSub(item['contract_amount'], item['receivables_money']) //计算剩余最大本次收款
            const tmpPercentageAmout = this.numMulti(row.amount, item['percentage']) //计算本次分配金额
            item['new_money'] = tmpPercentageAmout > tmpReceivablesAmout ? tmpReceivablesAmout : tmpPercentageAmout
            otherAmout = this.numAdd(otherAmout, item['new_money'])
          } else {
            item['new_money'] = this.numSub(Number(row.amount).toFixed(1), Number(otherAmout).toFixed(1))
          }

        })
      }
    
    },
    /**
     * 求比分比  
     * 
     */
    GetPercent(num, total) {
      num = parseFloat(num);
      total = parseFloat(total);
      if (isNaN(num) || isNaN(total)) {
        return 0;
      }
      return total <= 0 ? "0%" : (Math.round(num / total * 10000) / 100.00) + "%";
    },
    numAdd(num1, num2) {
      var baseNum, baseNum1, baseNum2;
      try {
        baseNum1 = num1.toString().split(".")[1].length;
      } catch (e) {
        baseNum1 = 0;
      }
      try {
        baseNum2 = num2.toString().split(".")[1].length;
      } catch (e) {
        baseNum2 = 0;
      }
      baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
      return (num1 * baseNum + num2 * baseNum) / baseNum;
    },
    /**
     * 加法运算，避免数据相减小数点后产生多位数和计算精度损失。
     * 
     * @param num1被减数  |  num2减数
     */
    numSub(num1, num2) {
      var baseNum, baseNum1, baseNum2;
      var precision; // 精度
      try {
        baseNum1 = num1.toString().split(".")[1].length;
      } catch (e) {
        baseNum1 = 0;
      }
      try {
        baseNum2 = num2.toString().split(".")[1].length;
      } catch (e) {
        baseNum2 = 0;
      }
      baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
      precision = (baseNum1 >= baseNum2) ? baseNum1 : baseNum2;
      return ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(precision);
    },
    /**
     * 乘法运算，避免数据相乘小数点后产生多位数和计算精度损失。
     * 
     * @param num1被乘数 | num2乘数
     */
    numMulti(num1, num2) {
      var baseNum = 0;
      try {
        baseNum += num1.toString().split(".")[1].length;
      } catch (e) {
        console.log(e);
      }
      try {
        baseNum += num2.toString().split(".")[1].length;
      } catch (e) {
        console.log(e);
      }
      return (Number(num1.toString().replace(".", "")) * Number(num2.toString().replace(".", "")) / Math.pow(10, baseNum)) / 100.00

    },
  },
  computed: {
    ...mapGetters(["receivables_project"])
  }

};