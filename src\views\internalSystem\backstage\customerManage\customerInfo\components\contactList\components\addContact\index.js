import API from '@/api/internalSystem/customerManage/customerInfo'
import {mapGetters} from 'vuex'
import {
  getOptions
} from "@/common/internalSystem/common.js"
export default {
  name: "addContact",
  data() {
    return {
      dialogTitle: "新增客户联系人",
      dialogVisible: false,
      ruleForm: {
        linkman_name: "",
        linkman_gender: "",
        phone: "",
        linkman_type: "",
        link_address: "",
        we_chat: "",
        linkman_position: "",
        email: "",
        qq: "",
        fax: ""
      },
      rules: {
        linkman_name: [{
          required: true,
          message: "请输入联系人名称",
          trigger: "blur"
        }],
        linkman_gender: [{
          required: true,
          message: "请选择联系人性别",
          trigger: "change"
        }],
        phone: [{
          required: true,
          message: "请输入电话",
          trigger: "blur"
        }],
        linkman_type: [{
          required: true,
          message: "请选择联系人类型",
          trigger: "change"
        }],
        link_address: [{
          required: true,
          message: "请输入联系地址",
          trigger: "blur"
        }],
        email: [{
          type: "email",
          message: "请输入正确的邮箱格式",
          trigger: "blur"
        }]
      },
      sexList:[],
      linkmanTypeList:[]
    };
  },
  props: {
    customer_id: {
      type: Number
    },
  },
  computed: {
    ...mapGetters([
      'params_constant_gender',
      'params_constant_linkman_type'
    ])
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      if (!data) {
        this.dialogTitle = "新增客户联系人";
      } else {

        this.dialogTitle = "修改客户联系人";
        this.ruleForm = data;
      }
      this.sexList = getOptions('t_customer_linkman', 'linkman_gender');
      this.linkmanTypeList = getOptions('t_customer_linkman', 'linkman_type');
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      params.customer_id=this.customer_id;
      if (params.customer_linkman_id) {
        API.updateCustomerLinkman(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      } else {
        API.addCustomerLinkman(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        linkman_name: "",
        linkman_gender: "",
        phone: "",
        linkman_type: "",
        link_address: "",
        we_chat: "",
        linkman_position: "",
        email: "",
        qq: "",
        fax: ""
      }
    }
  }
};