<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="80px">
    <el-form-item label="原始密码" prop="password">
      <el-input v-model="form.password" placeholder="请输入原始密码" />
    </el-form-item>
    <el-form-item label="新密码" prop="newPassword">
      <el-input v-model="form.newPassword" placeholder="请输入新密码" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="onSubmit">修改密码</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import md5 from "js-md5";
import Api from '@/api/internalSystem/basicManage/password/index.js'
export default {
  data() {
    return {
      form: {},
      rules: {
        password: [
          { required: true, message: "请输入原始密码", trigger: "blur" },
        ],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          Api.updatePassword({
            password: md5(this.form.password),
            newPassword: md5(this.form.newPassword)
          }).then(res => {
            this.success(res.msg)
          }).catch(() => {})
        }
      });
    },
  },
};
</script>