import API from '@/api/internalSystem/financialManage/bankAccout'
import comAPI from '@/api/internalSystem/basicManage/salesUnit'
import validationRules from "@/common/internalSystem/validationRules.js"
export default {
  name: "addBankAccout",
  data() {
    return {
      dialogTitle: "新增银行账户",
      dialogVisible: false,
      ruleForm: {
        bank_code: "",
        bank_name: "",
        accout_name: "",
        bank_accout: "",
        fk_sales_unit_id: "",
        remain: 0,
        remark: ""
      },
      rules: {
        bank_code: [{
          required: true,
          message: "请输入银行编码",
          trigger: "blur"
        }],
        bank_name: [{
          required: true,
          message: "请输入银行名称",
          trigger: "blur"
        }],
        accout_name: [{
          required: true,
          message: "请输入户名",
          trigger: "blur"
        }],
        bank_accout: [{
          required: true,
          validator: validationRules.checkBank,
          trigger: "blur"
        }],
        fk_sales_unit_id: [{
          required: true,
          message: "请选择企业单位",
          trigger: "change"
        }],
        remain: [{
          required: true,
          message: "请输入余额",
          trigger: "blur"
        }],
        remark: [{
          required: true,
          message: "请输入备注",
          trigger: "blur"
        }]
      },
      companyList: []
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      comAPI.query()
        .then(data => {
          this.companyList = data.data;
        })
        .catch(() => {});
      if (!data) {
        this.dialogTitle = "新增银行账户";
      } else {

        this.dialogTitle = "修改银行账户";
        this.ruleForm = data;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      if (params.financial_bank_accout_id) {
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        bank_code: "",
        bank_name: "",
        accout_name: "",
        bank_accout: "",
        fk_sales_unit_id: "",
        remain: 0,
        remark: ""
      }
    }
  }
};