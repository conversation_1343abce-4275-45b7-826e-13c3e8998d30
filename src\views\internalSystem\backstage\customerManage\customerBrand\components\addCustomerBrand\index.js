import API from "@/api/internalSystem/customerManage/customerBrand/index.js";
import brandAPI from "@/api/internalSystem/basicManage/brand";
import paramAPI from "@/api/internalSystem/basicManage/parameter";
import ContractList from "@/views/internalSystem/backstage/components/contractList/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import TableView from "./../tableView/index.vue";
import CustomerList from "@/views/internalSystem/backstage/components/customerList/index.vue";
import { dateFormat } from "@/common/internalSystem/common.js";
import { concat } from "lodash";
import { mapGetters } from "vuex";
export default {
  name: "addCustomerBrand",
  components: {
    ContractList,

    MyDate,
    TableView,
    CustomerList,
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {},

      projectList: [], //收款项目
      settlementMethodList: [], //结算方式
      sellTypeList: [], //销售类型
      loading: false,
      tableData: [],
      proList: [], //产品列表
      contract_detail_ids: [],
      tableList: [
        {
          // width: 150,
          name: "产品服务",
          value: "fk_brand_id",
          isEditRender: true,
          type: "select",
        },
        {
          width: 120,
          name: "原销售类型",
          value: "detail_sell_type_old",
          isEditRender: false,
          type: "select",
        },
        {
          width: 120,
          name: "新销售类型",
          value: "detail_sell_type_new",
          isEditRender: true,
          type: "select",
        },
        {
          width: 120,
          name: "原合同金额",
          value: "contract_amount_old",
          isEditRender: false,
          type: "input",
        },
        {
          width: 120,
          name: "新合同金额",
          value: "contract_amount_new",
          isEditRender: true,
          type: "input",
        },

        {
          width: 100,
          name: "原确认端口数量",
          value: "original_port_count_old",
          isEditRender: false,
          type: "input",
        },
        {
          width: 100,
          name: "新确认端口数量",
          value: "original_port_count_new",
          isEditRender: true,
          type: "input",
        },

        {
          width: 150,
          name: "原年维护费比例",
          value: "year_maintain_cost_old",
          isEditRender: false,
          type: "select",
        },
        {
          width: 150,
          name: "新年维护费比例",
          value: "year_maintain_cost_new",
          isEditRender: true,
          type: "select",
        },

        {
          width: 100,
          name: "原维护费",
          value: "maintenance_fee_old",
          isEditRender: false,
          type: "input",
        },
        {
          width: 100,
          name: "新维护费",
          value: "maintenance_fee_new",
          isEditRender: true,
          type: "input",
        },

        {
          width: 150,
          name: "原维护起始时间",
          value: "maintain_start_time_old",
          isEditRender: false,
          type: "date",
        },
        {
          width: 150,
          name: "新维护起始时间",
          value: "maintain_start_time_new",
          isEditRender: true,
          type: "date",
        },

        {
          width: 150,
          name: "原新维护结束时间",
          value: "new_maintain_stop_time_old",
          isEditRender: false,
          type: "date",
        },
        {
          width: 150,
          name: "新新维护结束时间",
          value: "new_maintain_stop_time_new",
          isEditRender: true,
          type: "date",
        },

        {
          width: 150,
          name: "原软件序列号",
          value: "software_no_old",
          isEditRender: false,
          type: "input",
        },
        {
          width: 150,
          name: "新软件序列号",
          value: "software_no_new",
          isEditRender: true,
          type: "input",
        },
        {
          width: 150,
          name: "原备注",
          value: "remark_old",
          isEditRender: false,
          type: "input",
        },
        {
          width: 150,
          name: "新备注",
          value: "remark_new",
          isEditRender: true,
          type: "input",
        },
      ],
      yearsFeeList: [], //年维护费比例
      proObj: {},
      isDel: true,
      rules: {
        customer_name: [
          {
            required: true,
            message: "请选择客户",
            trigger: "change",
          },
        ],
      },
    };
  },
  mounted: {},
  methods: {
    getBrand() {
      this.yearsFeeList = [];
      this.proList = [];
      brandAPI["query2"]().then((data) => {
        data.data.forEach((item) => {
          this.proList.push({
            label: item.brand_type,
            value: item.brand_id,
            parentId: item.parent_id,
          });
        });
      });
      paramAPI.query().then((data) => {
        data.data.forEach((item) => {
          if (item.parameter_type == 2)
            this.yearsFeeList.push({
              label: item.content,
              value: item.content,
            });
        });
      });
    },

    /**
     * 新增产品
     */
    add() {
      this.proObjRest();
      let pro = JSON.parse(JSON.stringify(this.proObj));

      this.$refs.proTableCustomerBrand.add2(pro);
    },
    chooseCustomer() {
      this.$refs.customerAllList.Show();
    },
    //客户列表选择
    async getCustomerInfo(info = {}) {
      this.ruleForm = info;

      this.tableData = concat(
        this.tableData,
        await this.getCustomerBrandByCustomerId(info)
      );
    },
    /**
     * 根据客户id查找对应的产品信息
     */
    async getCustomerBrandByCustomerId(info) {
      let tmpList = await this.getCustomerBrand(info["customer_id"]);

      return tmpList;
    },
    async Show(data = null) {
      this.proObjRest();

      this.dialogVisible = true;
      this.isDel = true;

      //根据客户，查询他所有的产品信息
      let tmpList = await this.getCustomerBrand(data["fk_customer_id"]);

      if (data) {
        this.ruleForm = data;
        this.ruleForm.customer_id = data.fk_customer_id;
        this.tableData = tmpList;
        this.isDel = false;
      }
    },
    //根据客户，查询他所有的产品信息
    async getCustomerBrand(customerId) {
      let params = {};
      params.customerId = customerId;
      let customerBrandList = await API.query(params);
      let tmpList = customerBrandList.data;
      if (tmpList && tmpList.length > 0) {
        tmpList.map((item) => {
          item["contract_amount_new"] = item["contract_amount"];
          item["contract_amount_old"] = item["contract_amount"];
          item["original_port_count_new"] = item["original_port_count"];
          item["original_port_count_old"] = item["original_port_count"];
          item["year_maintain_cost_new"] = item["year_maintain_cost"];
          item["year_maintain_cost_old"] = item["year_maintain_cost"];
          item["maintenance_fee_new"] = item["maintenance_fee"] || 0;
          item["maintenance_fee_old"] = item["maintenance_fee"] || 0;
          item["maintain_start_time_new"] = item["maintain_start_time"];
          item["maintain_start_time_old"] = item["maintain_start_time"];
          item["new_maintain_stop_time_new"] = item["new_maintain_stop_time"];
          item["new_maintain_stop_time_old"] = item["new_maintain_stop_time"];
          item["software_no_new"] = item["software_no"];
          item["software_no_old"] = item["software_no"];
          item["detail_sell_type_old"] = item["detail_sell_type"];
          item["detail_sell_type_new"] = item["detail_sell_type"];
          item["remark_old"] = item["remark"];
          item["remark_new"] = item["remark"];
        });
      } else {
        tmpList = [];
      }
      return tmpList;
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel(flag = true) {
      //保存之后，不退出当前页面
      if (flag) {
        this.dialogVisible = false;
        this.$emit("selectData");
      }

      this.resetForm("ruleForm");
      this.clearData();
    },
    async save() {
      let params = this.ruleForm;

      if (!params.customer_id) {
        return this.error("请先调入客户信息");
      }
      if (!this.tableData || this.tableData.length === 0) {
        return this.error("客户产品至少有一条主产品数据");
      }

      let flag1 = true;
      let flag2 = true;
      let flag3 = true;
      let flag4 = true;
      let distinctBrandIds = [];
      this.tableData.forEach((element) => {
        if (!element["fk_brand_id"]) {
          flag1 = false;
        } else {
          if (!distinctBrandIds.includes(element["fk_brand_id"])) {
            distinctBrandIds.push(element["fk_brand_id"]);
          }
        }

        if (!element["maintain_start_time_new"]) {
          flag2 = false;
        }
        if (!element["new_maintain_stop_time_new"]) {
          flag3 = false;
        }

        if (
          new Date(element["maintain_start_time_new"]).getTime() >=
          new Date(element["new_maintain_stop_time_new"]).getTime()
        ) {
          flag4 = false;
        }
      });

      if (!flag1) {
        return this.error("产品不能为空");
      }
      if (!flag2) {
        return this.error("维护开始时间不能为空");
      }
      if (!flag3) {
        return this.error("维护结束时间不能为空");
      }
      if (!flag4) {
        return this.error("维护开始时间不能大于维护结束时间");
      }
      if (!flag4) {
        return this.error("维护开始时间不能大于维护结束时间");
      }
      if (distinctBrandIds.length !== this.tableData.length) {
        return this.error("产品不能重复");
      }

      this.loading = true;
      params.detailList = this.tableData;
      API.saveOrUpdate(params)
        .then((res) => {
          this.tableData = [];
          this.getCustomerInfo(this.ruleForm);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {};
      this.tableData = [];
    },
    // 重置行数据
    proObjRest() {
      this.proObj = {
        fk_brand_id: "",
        contract_amount_new: 0,
        contract_amount_old: null,
        original_port_count_new: 0,
        original_port_count_old: null,
        year_maintain_cost_new: 0,
        year_maintain_cost_old: null,
        maintenance_fee_new: 0,
        maintenance_fee_old: null,
        maintain_start_time_new: null,
        maintain_start_time_old: null,
        new_maintain_stop_time_new: null,
        new_maintain_stop_time_old: null,
        software_no_new: null,
        software_no_old: null,
      };
    },
  },
  computed: {
    ...mapGetters(["buttonPermissions", "sell_type", "userInfo"]),
  },
};
