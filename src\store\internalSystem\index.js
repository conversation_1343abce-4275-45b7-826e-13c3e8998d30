import Vuex from 'vuex'
import span from './span'
import constant from "../constant.js"
import dictionary from './dictionary'
import environment from '../environment.js'
import params_constant from './params_constant'
import permissions from './permissions'
import userInfo from './userInfo'
import employeeList from './employeeList'
// import websocket from './websocket'
import createPersistedState from 'vuex-persistedstate'

const store = new Vuex.Store({
	modules: {
		span,
		dictionary,
		constant,
		environment,
		params_constant,
		permissions,
		userInfo,
		employeeList,
	},
	plugins: [createPersistedState({
		storage: window.sessionStorage, //选择sessionStorage 进行存储
		reducer(state,paths) {
			return {
					permissions: state.permissions  // 只存储permissions信息并且在sessionStorage命名替换成permissions
			}
		}
	})]
})
export default store
