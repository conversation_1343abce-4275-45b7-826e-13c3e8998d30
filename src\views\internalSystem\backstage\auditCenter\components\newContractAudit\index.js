import API from "@/api/internalSystem/salesManage/contract";
import paramAPI from "@/api/internalSystem/basicManage/parameter";
import brandAPI from "@/api/internalSystem/basicManage/brand";
import TableCustom from "@/views/internalSystem/backstage/components/tableCustom/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { getOptions } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";
export default {
  name: "contractAudit",
  components: {
    TableCustom,
    MyDate,
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_name: "",
        fk_customer_id: "",
        train_type: "",
        pay_type: "",
        sell_type: "",
        sales_unit_id: "",
        link_man: "",
        fk_sell_employee_id: "",
        fk_sell_department_id: "",
        phone: "",
        introducer_format: "",
        introducer: "",
        province_name: "",
        city_name: "",
        remark: "",
        customer_address: "",
        fax: "",
        software_no: "",
        link_qq: "",
        sales_unit_id_format: "",
        introducer_contract_format: "",
        introducer_contract_id: "",
        fk_recommend_employee_id: "",
      },
      trainTypeList: [], //培训方式
      payTypeList: [], //付款方式
      sellTypeList: [], //销售类型
      softwareVersionList: [], //软件版本
      measurementUnitList: [], //计量单位
      paramsList: [],
      loading: false,
      activeName: "first",
      proList: [], //产品列表
      moduleList: [], //模块列表
      quotationRateList: [], //报价税率
      prepaymentRatioList: [], //预付款比例
      yearsFeeList: [], //年维护费比例
      employeeList: [],
      proTableCol: [
        {
          label: "id",
          prop: "quotation_id",
          isHide: true,
        },
        {
          label: "产品服务",
          prop: "fk_brand_id",
          need: true,
          width: 160,
        },
        {
          label: "软件版本",
          prop: "software_version",
          need: true,
          width: 160,
        },
        {
          label: "销售方式",
          prop: "sale_pattern",
          need: true,
          width: 160,
        },
        {
          label: "软件模式",
          prop: "product_sale_model",
          need: true,
          width: 160,
        },
        {
          label: "是否授权码",
          prop: "product_is_grant",
          need: true,
          width: 160,
        },
        {
          label: "有无维护期",
          prop: "product_is_period",
          need: true,
          width: 160,
        },
        {
          label: "计量单位",
          prop: "measurement_unit",
          need: true,
          width: 160,
        },
        {
          label: "合同数量",
          prop: "contract_count",
          width: 160,
        },
        {
          label: "合同金额",
          prop: "contract_amount",
          need: true,
          width: 160,
        },
        {
          label: "发票税率",
          prop: "invoice_tax_rate",
          need: true,
          width: 160,
        },
        // {
        //   label: "预付款比例%",
        //   prop: "prepayment_rate",
        //   need: true,
        //   width: 160,
        // },
        {
          label: "年维护费比例%",
          prop: "year_maintain_cost",
          need: true,
          width: 160,
        },
        {
          label: "预付款期限",
          prop: "prepayment_time_limit",
          width: 160,
        },
        {
          label: "维护起始日期",
          prop: "maintain_start_time",
          need: true,
          width: 160,
        },
        {
          label: "新维护结束日期",
          prop: "new_maintain_stop_time",
          need: true,
          width: 160,
        },
        {
          label: "原维护结束日期",
          prop: "original_maintain_stop_time",
          width: 160,
        },
        {
          label: "原有端口数",
          prop: "original_port_count",
          width: 160,
        },
        {
          label: "新增端口数",
          prop: "add_port_count",
          width: 160,
        },
        {
          label: "合同备注",
          prop: "remark",
          width: 160,
        },
      ],
      proObj: {},
      moduleTableCol: [
        {
          label: "id",
          prop: "quotation_id",
          isHide: true,
        },
        {
          label: "模块名称",
          prop: "fk_brand_id",
          need: true,
        },
        {
          label: "计量单位",
          prop: "measurement_unit",
          need: true,
        },
        {
          label: "合同金额",
          prop: "contract_amount",
          need: true,
        },
        {
          label: "合同备注",
          prop: "remark",
        },
        {
          label: "出库数量",
          prop: "stock_removal_count",
        },
      ],
      moduleObj: {},
      dialogVisibleAudit: false,
      auditForm: {
        auditState: 1,
        auditRemark: "",
      },
      rules: {
        auditState: [
          {
            required: true,
            message: "请选择审核状态",
            trigger: "change",
          },
        ],
      },
      auditStateList: [],

    };
  },
  methods: {
    async Show(data = null) {
      this.dialogVisible = true;
      this.proList = [];
      this.moduleList = [];
      this.softwareVersionList = [];
      this.measurementUnitList = [];
      this.quotationRateList = [];
      this.prepaymentRatioList = [];
      this.yearsFeeList = [];
      this.trainTypeList = getOptions("t_customer_contract", "train_type");
      this.payTypeList = getOptions("t_customer_contract", "pay_type");
      this.sellTypeList = getOptions("t_customer_contract", "sell_type");
      this.softwareVersionList = getOptions(
        "t_contract_detail",
        "software_version"
      );
      this.measurementUnitList = getOptions(
        "t_contract_detail",
        "measurement_unit"
      );
      this.auditStateList = getOptions("t_customer_contract", "audit_state");
      this.activeName = "first";
      this.$store.dispatch("getEmployee").then((res) => {
        this.employeeList = res;
      });
      this.softwareVersionList.forEach((item) => {
        item.label = item.sysName;
        item.value = item.sysValue;
      });
      this.measurementUnitList.forEach((item) => {
        item.label = item.sysName;
        item.value = item.sysValue;
      });
      this.ruleForm.add_user_name = this.userInfo.fullName;
      this.ruleForm.fk_operator_department_name = this.userInfo.department_name;
      await this.getBrand();
      await this.getParam();
      this.proObj = {
        quotation_id: {
          value: "",
          type: "input",
        },
        fk_brand_id: {
          value: "",
          type: "select",
          option: this.proList,
        },
        software_version: {
          value: "",
          type: "select",
          option: this.software_version,
        },
        sale_pattern: {
          value: "",
          type: "select",
          option: this.sale_pattern,
        },
        product_is_grant: {
          value: "",
          type: "select",
          option: this.product_is_grant,
        },
        
        product_is_period: {
          value: "",
          type: "select",
          option: this.product_is_period,
        },

        product_sale_model: {
          value: "",
          type: "select",
          option: this.product_sale_model,
        },
        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
        },
        contract_count: {
          value: "1",
          type: "number",
        },
        contract_amount: {
          value: "",
          type: "float",
        },
        invoice_tax_rate: {
          value: "",
          type: "select",
          option: this.quotationRateList,
        },
        prepayment_rate: {
          value: "",
          type: "select",
          option: this.prepaymentRatioList,
        },
        year_maintain_cost: {
          value: "",
          type: "select",
          option: this.yearsFeeList,
        },
        prepayment_time_limit: {
          value: "",
          type: "date",
        },
        maintain_start_time: {
          value: "",
          type: "date",
        },
        new_maintain_stop_time: {
          value: "",
          type: "date",
        },
        original_maintain_stop_time: {
          value: "",
          type: "date",
        },
        original_port_count: {
          value: 0,
          type: "number",
        },
        add_port_count: {
          value: "",
          type: "number",
        },
        remark: {
          value: "",
          type: "input",
        },
      };
      this.moduleObj = {
        quotation_id: {
          value: "",
          type: "input",
        },
        fk_brand_id: {
          value: "",
          type: "select",
          option: this.moduleList,
        },
        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
        },
        contract_amount: {
          value: "",
          type: "float",
        },
        remark: {
          value: "",
          type: "input",
        },
        stock_removal_count: {
          value: "",
          type: "number",
        },
      };
      if (data) {
        this.ruleForm = data;
        API.detailList({
          customer_contract_id: data.customer_contract_id,
        })
          .then((res) => {
            res.data.map((item) => {
              let pro = JSON.parse(JSON.stringify(this.proObj));
              let module = JSON.parse(JSON.stringify(this.moduleObj));
              for (let v in item) {
                if (item.detail_type == 1) {
                  if (pro[v]) {
                    pro[v].value = item[v];
                    pro[v].disabled = true;
                  }
                } else {
                  if (module[v]) {
                    module[v].value = item[v];
                    module[v].disabled = true;
                  }
                }
              }
              if (item.detail_type == 1) {
                this.$refs.proTableCustom.add2(pro);
              } else {
                this.$refs.moduleTableCustom.add2(module);
              }
            });
          })
          .catch(() => {})
          .finally(() => {});
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm("ruleForm");
      this.clearData();
      this.$emit("selectData");
    },
    save() {
      let params = this.auditForm;
      params.customer_contract_id = this.ruleForm.customer_contract_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    getBrand() {
      return new Promise((resolve, reject) => {
        brandAPI
          .query()
          .then((data) => {
            data.data.forEach((item) => {
              if (item.brand_classify == 1)
                this.proList.push({
                  label: item.brand_type,
                  value: item.brand_id,
                });
              else if (item.brand_classify == 2)
                this.moduleList.push({
                  label: item.brand_type,
                  value: item.brand_id,
                });
            });
            resolve(1);
          })
          .catch(() => {
            reject();
          });
      });
    },
    getParam() {
      paramAPI
        .query()
        .then((data) => {
          data.data.forEach((item) => {
            if (item.parameter_type == 5)
              this.quotationRateList.push({
                label: item.content,
                value: item.content,
              });
            else if (item.parameter_type == 1)
              this.prepaymentRatioList.push({
                label: item.content,
                value: item.content,
              });
            else if (item.parameter_type == 2)
              this.yearsFeeList.push({
                label: item.content,
                value: item.content,
              });
          });
        })
        .catch(() => {});
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm("auditForm");
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_name: "",
        fk_customer_id: "",
        train_type: "",
        pay_type: "",
        sell_type: "",
        sales_unit_id: "",
        link_man: "",
        fk_sell_employee_id: "",
        fk_sell_department_id: "",
        phone: "",
        introducer_format: "",
        introducer: "",
        province_name: "",
        city_name: "",
        remark: "",
        customer_address: "",
        fax: "",
        software_no: "",
        link_qq: "",
        sales_unit_id_format: "",
        introducer_contract_format: "",
        introducer_contract_id: "",
        fk_recommend_employee_id: "",
      };
    },
  },
  computed: {
    ...mapGetters(["userInfo", "buttonPermissions","contract_train_type",
    "measurement_unit",
    "sell_type",
    "contract_pay_type",
    "software_version",
    "contract_auditStateList",
    "sale_pattern",
    "product_sale_model",
    "product_is_period",
    "product_is_grant",
    "is_open_bill"
    ]),
  },
};
