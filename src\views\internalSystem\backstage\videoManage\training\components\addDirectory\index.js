import API from "@/api/internalSystem/videoManage/directory/index.js";

import { mapGetters } from "vuex";
export default {
  props: {
    brand_id: {
      default: "",
    },
  },
  data() {
    return {
      dialogVisible: false,
      title: "新增",
      ruleForm: {
        directory_name: "",
        brand_id: "",
        directory_id: "",
        parent_id: "",
        parent_name: "",
      },
      rules: {
        directory_name: [
          {
            required: true,
            message: "请输入目录名称",
            trigger: "blur",
          },
        ],

        parent_id: [
          {
            required: true,
            message: "请选择上级目录",
            trigger: "blur",
          },
        ],
        // brand_id: [{
        //   required: true,
        //   message: "请选择对应产品",
        //   trigger: "blur"
        // }],
      },
    };
  },
  computed: {
    ...mapGetters(["params_constant_directory_type"]),
  },
  methods: {
    //获取单个目录
    getDirectory(id) {
      API.getInfo({
        directory_id: id,
      })
        .then((data) => {
          this.ruleForm = data.data;
          if (data.data.parentId == 0) this.ruleForm.parent_name = "无上级目录";
        })
        .catch(() => {})
        .finally(() => {});
    },
    Show(
      directoryObject = {
        parent_id: "",
        parent_name: "",
      }
    ) {
      //弹窗显示
      this.dialogVisible = true;
      this.ruleForm.parent_id = directoryObject.parent_id;
      this.ruleForm.parent_name = directoryObject.parent_name;
      this.ruleForm.level = directoryObject.level;
      if (!directoryObject.directory_id) {
        this.title = "新增";
      } else {
        this.title = "修改";
        this.getDirectory(directoryObject.directory_id);
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let apiName = this.ruleForm.directory_id ? "update" : "add";

          let params = {};

          Object.assign(params, this.ruleForm);
          params.brand_id = params.brand_id || this.brand_id;
          API[apiName](params)
            .then(() => {
              this.closeDialog();
              this.$emit("getList");
            })
            .catch(() => {})
            .finally(() => {});
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    closeDialog() {
      this.dialogVisible = false;
      this.resetForm("ruleForm");
      this.clearData();
      this.$emit("getDirectoryTree");
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        directory_name: "",
        brand_id: "",
        directory_id: "",
        parent_id: "",
        parent_name: "",
      };
    },
  },
};
