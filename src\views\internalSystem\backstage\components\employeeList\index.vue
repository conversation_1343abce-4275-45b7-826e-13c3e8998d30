<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="600px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    
    <table-view :tableList="tableList" :tableData="tableData" :tableHeight="460"
      @getSelectRecords="getSelectRecords" :isSel="true"></table-view>
    <Pagination ref="employee_pagination" @success="getList" />
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">选择</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>