import API from '@/api/internalSystem/customerManage/customerIntention'
import comAPI from '@/api/internalSystem/common/index.js'
import validationRules from "@/common/internalSystem/validationRules.js"
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  mapGetters
} from 'vuex'
export default {
  name: "addIntention",
  components: {
    MyDate
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_name: "",
        link_man: "",
        phone: "",
        customer_stage: 1,
        customer_source: "",
        customer_type: "",
        brand: "",
        province: "",
        city: "",
        belong_industry: "",
        contact_time: "",
        is_software: 1,
        quotation_scheme: "",
        follow_records: "",
        software_demand: "",
        use_describe: ""
      },
      rules: {
        link_man: [{
          required: true,
          message: "请输入联系人",
          trigger: "blur"
        }],
        customer_name: [{
          required: true,
          message: "请输入客户名称",
          trigger: "blur"
        }],
        phone: [{
          required: true,
          message: "请输入电话",
          // validator: validationRules.checkPhone,
          trigger: "blur"
        }],
        customer_type: [{
          required: true,
          message: "请选择客户类型",
          trigger: "change"
        }],
        belong_industry: [{
          required: true,
          message: "请选择所属行业",
          trigger: "change"
        }],
        province: [{
          required: true,
          message: "请选择所在省份",
          trigger: "change"
        }],
        customer_source: [{
          required: true,
          message: "请选择客户来源",
          trigger: "change"
        }],
        customer_stage: [{
          required: true,
          message: "请选择客户阶段",
          trigger: "change"
        }],
        city: [{
          required: true,
          message: "请选择所在城市",
          trigger: "change"
        }],
        brand: [{
          required: true,
          message: "请输入品牌",
          trigger: "blur"
        }],
        contact_time: [{
          required: true,
          message: "请输入联系时间",
          trigger: "blur"
        }],
        quotation_scheme: [{
          required: true,
          message: "请输入报价方案",
          trigger: "blur"
        }],
        follow_records: [{
          required: true,
          message: "请输入跟进记录",
          trigger: "blur"
        }],
        software_demand: [{
          required: true,
          message: "请输入企业痛点",
          trigger: "blur"
        }],
        is_software: [{
          required: true,
          message: "请选择是否用过erp软件",
          trigger: "change"
        }],
        use_describe: [{
          required: true,
          message: "请输入使用描述",
          trigger: 'blur'
        }]
      },
      provinceList: [], //省
      cityList: [], //市
      loading: false,
      isEdit: false
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.getProvinceList();
      this.isEdit = true;
      if (data) {
        this.isEdit = this.permissionToCheck("UPDATE_CUSTOMER_INTENTION_NEW") ? true : false;
        this.ruleForm = data;
        this.getCityList();
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    save() {
      let params = this.ruleForm;
      params.customer_name = params.customer_name.replace(/\s*/g, "");
      params.fk_sale_employee_id = this.userInfo.employeeId;
      this.loading = true;
      if (params.customer_intention_id) {
        if (params.auditState == 1)
          return this.error("该单据已审核，不允许修改！");
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      }

    },
    //获取省列表
    getProvinceList() {
      comAPI
        .queryAreaCode({
          level: 1
        })
        .then(data => {
          this.provinceList = data.data;
        })
        .catch(() => {});
    },
    //更换省
    changeProvince() {
      this.cityList = [];
      this.ruleForm.city = "";
      this.getCityList();
    },
    //获取市列表
    getCityList() {
      if (!this.ruleForm.province) return;
      comAPI
        .queryAreaCode({
          level: 2,
          province: this.ruleForm.province
        })
        .then(data => {
          this.cityList = data.data;
        })
        .catch(() => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_name: "",
        link_man: "",
        phone: "",
        customer_stage: 1,
        customer_source: "",
        customer_type: "",
        brand: "",
        province: "",
        city: "",
        belong_industry: "",
        contact_time: "",
        is_software: 1,
        quotation_scheme: "",
        follow_records: "",
        software_demand: "",
        use_describe: ""
      }
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'params_constant_customer_stage',
      'params_constant_customer_type',
      'params_constant_belong_industry',
      'params_constant_customer_source'
    ])
  },
};