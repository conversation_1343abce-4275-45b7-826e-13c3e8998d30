import API from '@/api/internalSystem/customerManage/salesVisiting'
import {
  getOptions
} from "@/common/internalSystem/common.js"
import {mapGetters} from 'vuex'
export default {
  name: "visitingAudit",
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_id: "",
        customer_no: "",
        customer_name: "",
        link_man: "",
        phone: "",
        contact_time: "",
        fk_sale_employee_id: "",
        visiting_form: "",
        follow_content: "",
        existing_software_functions: "",
        customer_demand: "",
        dateBackupState: "",
        isHandMaintenance: "",
        payDataState: "",
        receiveDataState: "",
        inventoryState: "",
        invoicePortState: "",
        qq: "",
        we_chat: "",
      },
      checkSoftware: [],
      loading: false,
      confirmNormal: [{
        name: "客户确认正常",
        value: 1
      }, {
        name: "客户未确认",
        value: 2
      }],
      accurate: [{
        name: "准确",
        value: 1
      }, {
        name: "不准确",
        value: 2
      }],
      normal: [{
        name: "使用",
        value: 1
      }, {
        name: "不使用",
        value: 2
      }],
      whether: [{
        name: "是",
        value: 1
      }, {
        name: "否",
        value: 0
      }],
      softwareList: [{
        name: "进销存管理",
        value: '1'
      }, {
        name: "客户关系管理",
        value: '3'
      }, {
        name: "成套生产管理",
        value: '4'
      }, {
        name: "员工资金管理",
        value: '5'
      }, {
        name: "员工绩效管理",
        value: '6'
      }, {
        name: "产品维修管理",
        value: '7'
      }, {
        name: "客户借货管理",
        value: '8'
      }, {
        name: "供应商管理",
        value: '9'
      }, {
        name: "财务做账管理",
        value: '10'
      }],
      dialogVisibleAudit: false,
      auditForm: {
        auditState: 1,
        auditRemark: ""
      },
      rules: {
        auditState: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }]
      },
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      if (data) {
        this.ruleForm = data;
        this.checkSoftware = data.existing_software_functions.split(",");
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    save() {
      let params = this.auditForm;
      params.sales_visiting_id = this.ruleForm.sales_visiting_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_id: "",
        customer_no: "",
        customer_name: "",
        link_man: "",
        phone: "",
        contact_time: "",
        fk_sale_employee_id: "",
        visiting_form: "",
        follow_content: "",
        existing_software_functions: "",
        customer_demand: "",
        dateBackupState: "",
        isHandMaintenance: "",
        payDataState: "",
        receiveDataState: "",
        inventoryState: "",
        invoicePortState: "",
        qq: "",
        we_chat: "",
      }
      this.checkSoftware = [];
    }
  },
  computed: {
    ...mapGetters([
      'contract_auditStateList',
      'sales_visiting_form'
    ])
  },
};