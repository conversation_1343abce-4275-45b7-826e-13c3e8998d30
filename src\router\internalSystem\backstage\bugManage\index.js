export default [
  {
    path: "bugManage",
    name: "bugManage",
    title: "禅道管理",
    icon: "home",
    show: true,
    meta: {
      keepAlive: false,
    },
    component: (resolve) =>
      require([
        "@/views/internalSystem/backstage/components/view/view.vue",
      ], resolve),
    children: [
      {
        path: "model",
        name: "model",
        title: "产品模块管理",
        meta: {
          title: "产品模块管理",
        },
        component: () =>
          import("@/views/internalSystem/backstage/bugManage/model/index.vue"),
      },
      {
        path: "training",
        name: "training",
        title: "培训视频管理",
        meta: {
          title: "培训视频管理",
        },
        component: () =>
          import("@/views/internalSystem/backstage/videoManage/training/index.vue"),
      },
      // {
      //   path: "training",
      //   name: "training",
      //   title: "培训视频管理",
      //   meta: {
      //     title: "培训视频管理",
      //   },
      //   component: () =>
      //     import(
      //       "@/views/internalSystem/backstage/videoManage/training/index.vue"
      //     ),
      // },
      {
        path: "bugContent",
        name: "bugContent",
        title: "产品问题管理",
        meta: {
          title: "产品问题管理",
        },
        redirect: {
          path: "/backstage/bugManage/bugContent/bugList",
        },
        children: [
          {
            path: "addBug",
            name: "addBug",
            title: "添加产品问题",
            meta: {
              title: "添加产品问题",
            },
            component: () =>
              import(
                "@/views/internalSystem/backstage/bugManage/bugContent/addBug/index.vue"
              ),
          },
          {
            path: "bugList",
            name: "bugList",
            title: "产品问题管理",
            meta: {
              title: "产品问题管理",
            },
            component: () =>
              import(
                "@/views/internalSystem/backstage/bugManage/bugContent/bugList/index.vue"
              ),
          },
          {
            path: "bugDetail",
            name: "bugDetail",
            title: "产品问题详情",
            meta: {
              title: "产品问题详情",
            },
            component: () =>
              import(
                "@/views/internalSystem/backstage/bugManage/bugContent/bugDetail/index.vue"
              ),
          },
        ],
        component: () =>
          import(
            "@/views/internalSystem/backstage/bugManage/bugContent/index.vue"
          ),
      },
    ],
  },
];
