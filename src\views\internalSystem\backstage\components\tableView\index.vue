<template>
  <!-- :checkbox-config="{ reserve: true }" 缓存上次选择的数据 -->
  <!-- row-id="id"   把row-id变成id -->
  <div class="tableContent">
    <vxe-table
      :id="tableId"
      column-key
      border
      resizable
      highlight-hover-row
      highlight-current-row
      auto-resize
      ref="xTable"
      :height="tableHeight ? tableHeight : '100%'"
      :loading="loading"
      header-row-class-name="head-row-class"
      align="center"
      row-id="_XID"
      @cell-click="getSelectRecords"
      @checkbox-change="getSelectRecords"
      @radio-change="getRadioRow"
      @checkbox-all="getSelectRecords"
      @cell-dblclick="rowDblclick"
      @sort-change="sortChange"
      :checkbox-config="{
        reserve: true,
        showHeader: true,
        trigger: 'row',
      }"
      :custom-config="{
        storage: {
          visible: true,
          resizable: true,
        },
      }"
      :data="tableData"
      size="small"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
    >
      <!-- :customs.sync="customColumns"    隐藏指定列 -->
      <!-- 多选 -->
      <vxe-table-column type="checkbox" align="center" width="40" v-if="isSel">
      </vxe-table-column>
      <!-- 单选框 -->
      <vxe-table-column
        type="radio"
        align="center"
        width="40"
        v-if="isRadio"
      ></vxe-table-column>
      <vxe-table-column
        type="seq"
        align="center"
        title="序号"
        width="50"
      ></vxe-table-column>
      <template v-for="item in tableList">
        <vxe-table-column
          v-if="!item.isHide"
          :key="item.value"
          :field="item.value"
          :fixed="item.fixed || ''"
          :title="item.name === '税率' ? '税率(%)' : item.name"
          :width="item.width ? +item.width + 10 : ''"
          :min-width="'100'"
          :header-class-name="'col-header'"
          :class-name="'col-operation'"
          footer-class-name="col-operation"
        >
          <template v-slot="{ row }">
            <!-- <el-popover
              placement="bottom"
              title=""
              width="200"
              :open-delay="300"
              trigger="hover"
              :content="row[item.value] + ''"
            > -->
            <div slot="reference" class="">{{ row[item.value] }}</div>
            <!-- </el-popover> -->
          </template>
        </vxe-table-column>
        <vxe-table-column
          v-if="item.isSlot"
          :key="item.value"
          :title="item.name"
          :field="item.value"
          :width="item.width ? +item.width + 10 : ''"
          :sortable="item.sortable ? item.sortable : false"
        >
          <!-- 销售合同单 => 剩余维护时间 -->
          <template v-slot="scope" v-if="item.value === 'balance_days'">
            <!-- <el-popover
              placement="bottom"
              title=""
              width="200"
              :open-delay="300"
              trigger="hover"
              :content="scope.row[item.value] + ''"
            > -->
            <div
              slot="reference"
              class="ellipsis"
              v-if="scope.row[item.value] > 30"
            >
              {{ scope.row[item.value] }}
            </div>
            <div
              slot="reference"
              class="ellipsis"
              style="color: bleak; font-size: large"
              v-if="scope.row[item.value] <= 30 && scope.row[item.value] >= 0"
            >
              {{ scope.row[item.value] }}
            </div>
            <div
              slot="reference"
              class="ellipsis"
              v-if="scope.row[item.value] < 0"
            >
              {{ scope.row[item.value] }}
            </div>
            <!-- </el-popover> -->
          </template>
        </vxe-table-column>
      </template>
      <vxe-table-column
        v-if="(isEdit || isDel || isThrid) && isOperation"
        :align="'center'"
        fixed="right"
        field="operation"
        title="操作"
        :width="handleWidth"
        header-class-name="col-operation"
        class-name="col-operation"
        footer-class-name="col-operation"
      >
        <template v-slot="{ row }">
          <el-button
            @click="modify(row)"
            type="text"
            size="small"
            v-permit="isEdit"
            >详情</el-button
          >
          <el-button
            @click="five(row)"
            v-permit="isFive"
            v-if="isFive"
            type="text"
            size="small"
            class="success ml10"
            >{{ fiveTitle }}
          </el-button>
          <el-button
            @click="four(row)"
            v-permit="isFour"
            v-if="isFour"
            type="text"
            size="small"
            class="success ml10"
            >{{ fourTitle }}
          </el-button>
          <el-button
            @click="thrid(row)"
            v-permit="isThrid"
            v-if="isThrid"
            type="text"
            size="small"
            class="success ml10"
            >{{ thridTitle }}{{ row.email_log_count !== undefined ? '(' + (row.email_log_count || 0) + ')' : '' }}
          </el-button>
          <el-button
            @click="del(row)"
            v-permit="isDel"
            type="text"
            size="small"
            class="danger ml10"
            >删除</el-button
          >
        </template>
      </vxe-table-column>
      <slot></slot>
    </vxe-table>
  </div>
</template>

<script src="./index.js"></script>
<style scoped lang="scss">
/deep/.head-row-class {
  background-color: #409eff;

  .vxe-cell {
    color: #fff;
  }
}
.col-header {
  font-weight: bold;
  // color: #fff;
}
.danger {
  color: #f56c6c;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.tableContent {
  flex: 1;
  height: 99%;
  overflow: hidden;
}

.el-table td,
.building-top .el-table th.is-leaf {
  border-bottom: 1px solid #f00;
}
</style>

<style>
.el-table .danger-row {
  /* background: #f56c6c;*/
  background: oldlace;
}

.el-table .death-row {
  /* background: rgb(148, 148, 148); */
  background: #f0f9eb;
}

.el-table .success-row {
  background: #f0f9eb;
}
</style>
