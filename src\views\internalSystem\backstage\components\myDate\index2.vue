<template>
  <div ref="d">
    <el-date-picker
      style="width: 100%"
      v-model="dateVal"
      class="myselectdate"
      @change="
        (e) => {
          $emit('input', e);
        }
      "
      @focus="dateFoucus"
      align="right"
      type="datetime"
      :placeholder="hint"
      value-format="yyyy-MM-dd HH:mm:ss"
      format="yyyy-MM-dd HH:mm:ss"
      :picker-options="pickerOptions"
      :disabled="disabled"
      ref="mydata2"
    >
    </el-date-picker>
  </div>
</template>

<script>
export default {
  name: "MyDate2",
  props: {
    value: {},
    hint: {
      type: String,
      default: "请选择日期",
    },
    isAllDate: {
      type: Boolean,
      default() {
        return true;
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        this.dateVal = newValue;
      },
    },
  },
  data() {
    return {
      dateVal: "",
      pickerOptions: {
        disabledDate: (time) => {
          return this.isAllDate ? false : time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      },
    };
  },
  methods: {
    dateFoucus(){
      this.$nextTick(()=>{
        
      document.querySelectorAll('.el-picker-panel__link-btn.el-button--text')[0].style.display="none"
      })
    }
  },
};
</script>

<style  lang="scss">
.myselectdate .el-picker-panel__link-btn {
      display:none !important;
}
// .el-picker-panel__footer /deep/ {
//     border-top: 1px solid #e4e4e4;
//     padding: 4px;
//     text-align: right;
//     background-color: #FFF;
//     position: relative;
//     font-size: 0;
//     display:none;
// }
</style>
