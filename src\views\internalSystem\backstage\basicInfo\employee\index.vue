<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.employee_number" placeholder="请输入员工工号"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="formSearch.employee_name" placeholder="请输入员工名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="formSearch.in_position" clearable placeholder="请选择在职状态">
          <el-option v-for="item in in_position" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button
          type="primary"
          @click="add"
          v-permit="'ADD_EMPLOYEE_NEW'"
        >添加</el-button>
      </el-form-item>
    </el-form>
    <!-- 新增修改员工信息 -->
    <add-user ref="AddUser" @selectData="getList" />
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      isEdit='UPDATE_EMPLOYEE_NEW'
      isDel='DEL_EMPLOYEE_NEW'
      @getSelectRecords="getSelectRecords"
      @modify="modify"
      @del="del"
    ></table-view>
    <Pagination ref="pagination" @success="getList" />
  </div>
</template>

<script src="./index.js"></script>
