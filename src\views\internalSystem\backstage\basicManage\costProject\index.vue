<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.project_name" placeholder="请输入项目名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button type="primary" @click="add"
          v-permit="'ADD_COSTPROJECT_NEW'">添加</el-button>
      </el-form-item>
    </el-form>
    <!-- 新增修改费用项目 -->
    <add-cost-project ref="AddCostProject" @selectData="getList" />
    <table-view :tableList="tableList" :tableData="tableData" @modify="modify" @del="del"
      :isEdit="'UPDATE_COSTPROJECT_NEW'"
      :isDel="'DEL_COSTPROJECT_NEW'"></table-view>
    <Pagination ref="pagination" @success="getList" />
  </div>
</template>

<script src="./index.js"></script>