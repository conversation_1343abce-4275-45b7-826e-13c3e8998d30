import API from '@/api/internalSystem/basicManage/parameter'
import { mapGetters } from "vuex"
export default {
  data() {
    return {
      dialogTitle: "新增税率比例参数",
      dialogVisible: false,
      ruleForm: {
        parameter_type: "",
        content: '0'
      },
      rules: {
        parameter_type: [{
          required: true,
          message: "请选择参数类型",
          trigger: "blur"
        }],
        content: [{
          required: true,
          message: "请输入税率比例",
          trigger: "blur"
        }]
      }
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      if (!data) {
        this.dialogTitle = "新增税率比例参数";
      } else {
        this.dialogTitle = "修改税率比例参数";
        this.ruleForm = data;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      if (params.parameter_id) {
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        parameter_type: '',
        content: '0'
      }
    }
  },
	computed: {
    ...mapGetters(["params_constant_parameter_type"])
  }
};
